
#飞书应用配置
appId=cli_9fc22bccfab0100e
appSecret=EhdXNhCkblzRvKgMTniWwhGggnXHVlLn
messageChatId=oc_a5f385ef52eea0ad201967934f32e2b4
gitLabAccessToken=********************
#值班运维顺序
dutyOrder=0

#是否正处于寒暑假窗口时段
holidayLimit=no

# 数据库配置
DB_HOST=**********
DB_PORT=5412
DB_USERNAME=postgres
DB_PASSWORD=7to12pg12
DB_NAME=jarvis


# git clone 目录
CLONE_PATH=/home/<USER>/test_projects/

# docker.sock
DOCKER_SOCK_PATH=/var/run/docker.sock

# git ssh
SSH_PATH=/home/<USER>/.ssh

# 日志文件

LOG_PATH=/home/<USER>/pipeline_logs

# 异常上线多维表格
EXCEPTION_BITABLE_APPTOKEN=NHEvbGdUmauYxqsOByncS4Kmnse
EXCEPTION_BITABLE_TABLEID=tbljnyA34UVdNcaW
