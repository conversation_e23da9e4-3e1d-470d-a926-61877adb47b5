# JARVIS-FE 🤖

> 前端技术栈一站式开发平台 - 致力于提供完整的开发服务体验

[![Node.js Version](https://img.shields.io/badge/node-%3E%3D22.0.0-brightgreen.svg)](https://nodejs.org/)
[![pnpm Version](https://img.shields.io/badge/pnpm-%3E%3D10.5.2-blue.svg)](https://pnpm.io/)
[![Vue 3](https://img.shields.io/badge/Vue-3.5.13-4FC08D.svg)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8.2-3178C6.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-6.2.0-646CFF.svg)](https://vitejs.dev/)

## 📋 目录

- [项目简介](#-项目简介)
- [技术栈](#-技术栈)
- [环境要求](#-环境要求)
- [快速开始](#-快速开始)
- [项目结构](#-项目结构)
- [开发规范](#-开发规范)
- [构建部署](#-构建部署)
- [常用工具和资源](#-常用工具和资源)
- [贡献指南](#-贡献指南)
- [常见问题](#-常见问题)
- [支持](#-支持)

## 🚀 项目简介

JARVIS-FE 是一个基于 Vue 3 + TypeScript 的现代化前端项目，提供：

- 🏗️ **服务构建部署** - 一键构建和部署服务
- 📊 **监控管理** - 实时监控和告警管理
- 📈 **数据分析** - 性能监控和数据可视化
- 🔧 **工具集成** - 开发工具和文档快捷访问
- 👥 **用户管理** - 完整的用户权限体系

## 🛠️ 技术栈

### 核心框架

- **[Vue 3](https://vuejs.org/)** `^3.5.13` - 渐进式JavaScript框架
- **[TypeScript](https://www.typescriptlang.org/)** `^5.8.2` - JavaScript的超集，提供静态类型检查
- **[Vite](https://vitejs.dev/)** `^6.2.0` - 下一代前端构建工具

### UI & 样式

- **[Element Plus](https://element-plus.org/)** `2.9.5` - Vue 3 组件库
- **[UnoCSS](https://unocss.dev/)** `66.1.0-beta.3` - 即时原子化CSS引擎
- **[Iconify](https://iconify.design/)** - 统一的图标框架

### 状态管理 & 路由

- **[Pinia](https://pinia.vuejs.org/)** `^3.0.1` - Vue 3 状态管理
- **[Vue Router](https://router.vuejs.org/)** `^4.5.0` - Vue.js 官方路由管理器
- **[unplugin-vue-router](https://github.com/posva/unplugin-vue-router)** - 文件系统路由

### 工具库

- **[VueUse](https://vueuse.org/)** `^12.7.0` - Vue 组合式工具集
- **[Axios](https://axios-http.com/)** `^1.8.2` - HTTP 客户端
- **[Day.js](https://day.js.org/)** `^1.11.13` - 轻量级日期处理库
- **[ECharts](https://echarts.apache.org/)** `^5.6.0` - 数据可视化图表库

### 开发工具

- **[ESLint](https://eslint.org/)** `^9.21.0` - 代码质量检查
- **[Vitest](https://vitest.dev/)** `^3.0.7` - 单元测试框架
- **[Cypress](https://www.cypress.io/)** `^14.1.0` - E2E 测试框架

### 构建增强

- **[vite-plugin-pwa](https://vite-pwa-org.netlify.app/)** `^0.21.1` - PWA 支持
- **[unplugin-auto-import](https://github.com/antfu/unplugin-auto-import)** - 自动导入
- **[unplugin-vue-components](https://github.com/antfu/unplugin-vue-components)** - 组件自动导入

## 📋 环境要求

> ⚠️ **重要**: 本项目要求 Node.js 版本 >= 22.0.0

### 必需环境

- **Node.js** >= 22.0.0 ([下载地址](https://nodejs.org/))
- **pnpm** >= 10.5.2 ([安装指南](https://pnpm.io/installation))

### 环境检查

```bash
# 检查 Node.js 版本
node --version  # 应该 >= v22.0.0

# 检查 pnpm 版本
pnpm --version  # 应该 >= 10.5.2
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://git.yc345.tv/fe/jarvis-fe.git
cd jarvis-fe
```

### 2. 安装依赖

```bash
# 使用 pnpm 安装依赖
pnpm install
```

### 3. 启动开发服务器

```bash
# 开发环境启动（默认端口 3333）
pnpm dev

# 启动后访问: http://localhost:3333
```

### 4. 构建项目

```bash
# 测试环境构建
pnpm build:test

# 生产环境构建
pnpm build:prod
```

## 📁 项目结构

```
jarvis-fe/
├── public/                     # 静态资源目录
│   ├── favicon.svg            # 网站图标
│   ├── logo.svg               # Logo文件
│   ├── pwa-*.png              # PWA图标
│   └── _headers               # 部署配置
├── src/                       # 源代码目录
│   ├── apis/                  # API接口定义（有页面或组件公用的提取到此文件夹下）
│   │   ├── enums.ts          # 枚举定义
│   │   ├── monitor.ts        # 监控相关API
│   │   ├── pipeline.ts       # 流水线API
│   │   ├── project.ts        # 项目管理API
│   │   ├── release.ts        # 发布管理API
│   │   └── user.ts           # 用户管理API
│   ├── components/            # 公共组件
│   │   ├── pipeline/         # 流水线组件
│   │   ├── Header.vue        # 头部组件
│   │   ├── Menu.vue          # 菜单组件
│   │   └── Welcome.vue       # 欢迎页组件
│   ├── composables/           # 组合式函数
│   │   └── dark.ts           # 暗色模式
│   ├── helpers/               # 工具函数
│   │   ├── auth.ts           # 认证相关
│   │   ├── enum.util.ts      # 枚举工具
│   │   ├── menu.ts           # 菜单处理
│   │   └── request.ts        # 请求封装
│   ├── layouts/               # 布局组件
│   │   ├── default.vue       # 默认布局
│   │   ├── empty.vue         # 空布局
│   │   └── 404.vue           # 404页面
│   ├── modules/               # 模块配置
│   │   ├── nprogress.ts      # 进度条
│   │   ├── permission.ts     # 权限控制
│   │   ├── pinia.ts          # 状态管理
│   │   └── pwa.ts            # PWA配置
│   ├── pages/                 # 页面组件（文件系统路由）
│   │   ├── build/            # 构建管理
│   │   ├── lit-c/            # Lit-C相关
│   │   ├── login/            # 登录页面
│   │   ├── monitor/          # 监控管理
│   │   ├── projects/         # 项目管理
│   │   ├── release/          # 发布管理
│   │   ├── report-chart/     # 报表图表
│   │   ├── user/             # 用户管理
│   │   ├── ycac/             # YCAC相关
│   │   ├── index.vue         # 首页
│   │   └── [...all].vue      # 404捕获
│   ├── stores/                # Pinia状态管理
│   │   └── user.ts           # 用户状态
│   ├── styles/                # 样式文件
│   │   ├── main.css          # 主样式
│   │   └── markdown.css      # Markdown样式
│   ├── types/                 # 类型定义
│   │   ├── env.d.ts          # 环境变量类型
│   │   └── types.ts          # 通用类型
│   ├── utils/                 # 工具函数
│   │   └── env.ts            # 环境配置
│   ├── App.vue               # 根组件
│   ├── main.ts               # 入口文件
│   ├── auto-imports.d.ts     # 自动导入类型（自动生成）
│   ├── components.d.ts       # 组件类型（自动生成）
│   └── typed-router.d.ts     # 路由类型（自动生成）
├── test/                      # 测试文件
│   ├── __snapshots__/        # 快照测试
│   ├── basic.test.ts         # 基础测试
│   ├── component.test.ts     # 组件测试
│   └── menu.test.ts          # 菜单测试
├── cypress/                   # E2E测试
│   └── e2e/                  # E2E测试用例
├── dist/                      # 构建输出目录
├── .env.development          # 开发环境配置
├── .env.test                 # 测试环境配置
├── .env.production           # 生产环境配置
├── .nvmrc                    # Node版本配置
├── package.json              # 项目配置
├── pnpm-lock.yaml           # 依赖锁定文件
├── tsconfig.json            # TypeScript配置
├── vite.config.ts           # Vite配置
├── uno.config.ts            # UnoCSS配置
├── eslint.config.js         # ESLint配置
└── cypress.config.ts        # Cypress配置
```

## 📐 开发规范

### 代码风格

项目使用 **[@antfu/eslint-config](https://github.com/antfu/eslint-config)** 作为代码规范，特点：

- 单引号，无分号
- 2空格缩进
- 自动格式化
- TypeScript 严格模式

### 命令规范

```bash
# 代码检查
pnpm lint

# 自动修复
pnpm fix

# 类型检查
pnpm typecheck
```

### 路由添加规则

项目使用 **文件系统路由**，基于 `unplugin-vue-router` 实现：

#### 1. 路由文件命名规范

```
src/pages/
├── index.vue                 # 根路由 "/"
├── about.vue                 # "/about"
├── user/
│   ├── index.vue            # "/user"
│   ├── profile.vue          # "/user/profile"
│   └── [id].vue             # "/user/:id" (动态路由)
├── [...all].vue             # 捕获所有未匹配路由 (404)
└── admin/
    └── dashboard.vue        # "/admin/dashboard"
```

#### 2. 路由元信息配置

在 `.vue` 文件中使用 `<route>` 块配置路由元信息：

```vue
<script setup lang="ts">
// 页面逻辑
</script>

<template>
  <div>页面内容</div>
</template>

<!-- 路由配置 -->
<route lang="yaml">
  meta:
    menu:
      name: 页面名称
      sort: 1                    # 菜单排序
      path: /custom-path         # 自定义路径（可选）
      icon: i-carbon-home        # 菜单图标
      parentMenu:                # 父级菜单（可选）
        name: 父菜单名称
        path: /parent
        icon: i-carbon-folder
        sort: 0
</route>
```

#### 3. 菜单配置示例

```vue
<!-- 一级菜单 -->
<route lang="yaml">
  meta:
    menu:
      name: 首页
      sort: 0
      path: /
      icon: i-hugeicons-home-09
</route>

<!-- 二级菜单 -->
<route lang="yaml">
  meta:
    menu:
      parentMenu:
        name: 奥创监控
        path: /monitor
        icon: i-solar-iphone-outline
        sort: 6
      name: 告警管理
      sort: 1
      path: /monitor/manage
      icon: i-material-symbols-ssid-chart
</route>
```

### 图标使用规范

项目使用 **Iconify** 图标系统，通过 UnoCSS 集成：

#### 1. 图标命名规则

```html
<!-- 格式: i-{collection}-{icon-name} -->
<div class="i-carbon-home" />
<!-- Carbon 图标集 -->
<div class="i-solar-user-outline" />
<!-- Solar 图标集 -->
<div class="i-hugeicons-building-02" />
<!-- Hugeicons 图标集 -->
```

#### 2. 常用图标集

- **carbon**: `i-carbon-*` - IBM Carbon 图标
- **solar**: `i-solar-*` - Solar 图标集
- **hugeicons**: `i-hugeicons-*` - Huge Icons
- **akar-icons**: `i-akar-icons-*` - Akar Icons
- **material-symbols**: `i-material-symbols-*` - Material Symbols

#### 3. 图标使用方式

```vue
<script setup lang="ts">
const iconClass = 'i-carbon-user'
</script>

<template>
  <!-- 直接使用类名 -->
  <i class="i-carbon-home text-xl" />

  <!-- 动态图标 -->
  <i :class="iconClass" />

  <!-- 在按钮中使用 -->
  <el-button>
    <i class="i-carbon-add mr-2" />
    添加
  </el-button>
</template>
```

#### 4. 图标预编译配置

常用图标已添加到 `uno.config.ts` 的 `safelist` 中，确保构建时被包含：

```typescript
// uno.config.ts
safelist: [
  // 首页图标
  'i-solar-flag-linear',
  'i-hugeicons-home-09',
  // 菜单图标
  'i-ep-fold',
  'i-ep-expand',
  // 状态图标
  'i-akar-icons-circle-check-fill',
  'i-akar-icons-circle-x-fill',
  // ... 更多图标
]
```

### 环境配置规范

项目支持三种环境配置：

#### 1. 环境文件

```
.env.development    # 开发环境
.env.test          # 测试环境
.env.production    # 生产环境
```

#### 2. 环境变量定义

```bash
# .env.development
VITE_ENV_NAME = 本地环境
VITE_APP_ENV = development
VITE_DOMAIN = https://jarvis-test-api.yc345.tv/v2
VITE_FEISHU_APPID = cli_9fc22bccfab0100e
```

#### 3. 环境变量使用

```typescript
// src/utils/env.ts
import { envConfig, isDev, isProd, isTest } from '~/utils/env'

// 获取环境信息
console.log(envConfig.env) // 'development' | 'test' | 'production'
console.log(envConfig.apiDomain) // API域名
console.log(envConfig.envName) // 环境显示名称

// 环境判断
if (isDev()) {
  // 开发环境逻辑
}

if (isTest()) {
  // 测试环境逻辑
}

if (isProd()) {
  // 生产环境逻辑
}
```

### 自动导入规范

项目配置了自动导入，以下内容无需手动导入：

- **Vue 3 API**: `ref`, `computed`, `watch`, `onMounted` 等
- **Vue Router**: `useRouter`, `useRoute` 等
- **VueUse**: `useLocalStorage`, `useDark`, `useToggle` 等
- **Element Plus 组件**: 自动解析和导入
- **自定义组件**: `src/components/` 目录下的组件
- **Composables**: `src/composables/` 目录下的组合式函数
- **Stores**: `src/stores/` 目录下的状态管理

```vue
<script setup lang="ts">
// 以下内容无需手动导入，可直接使用
const count = ref(0) // Vue 3 API
const router = useRouter() // Vue Router
const isDark = useDark() // VueUse
const userStore = useUserStore() // Pinia Store

// 组件和工具函数也会自动导入
</script>

<template>
  <!-- Element Plus 组件无需导入 -->
  <el-button @click="count++">
    {{ count }}
  </el-button>
</template>
```

## 🏗️ 构建部署

### 环境配置

项目支持三种环境的构建：

#### 1. 开发环境 (development)

- **配置文件**: `.env.development`
- **API域名**: `https://jarvis-test-api.yc345.tv/v2`
- **飞书应用ID**: `cli_9fc22bccfab0100e`
- **启动命令**: `pnpm dev`

#### 2. 测试环境 (test)

- **配置文件**: `.env.test`
- **API域名**: `https://jarvis-test-api.yc345.tv/v2`
- **飞书应用ID**: `cli_9fc22bccfab0100e`
- **构建命令**: `pnpm build:test`

#### 3. 生产环境 (production)

- **配置文件**: `.env.production`
- **API域名**: `https://jarvis-api.yc345.tv/v2`
- **飞书应用ID**: `cli_a379a9767ce1100e`
- **构建命令**: `pnpm build:prod`

### 构建命令详解

```bash
# 开发模式启动
pnpm dev
# 等价于: vite --port 3333 --open --mode development

# 测试环境构建
pnpm build:test
# 等价于: cross-env NODE_ENV=production vite build --mode test

# 生产环境构建
pnpm build:prod
# 等价于: cross-env NODE_ENV=production vite build --mode production
```

### 输出目录

构建完成后，所有文件输出到 `dist/` 目录：

## 🤝 贡献指南

### 开发流程

1. **克隆项目** 拉取master分支最新内容
2. **创建功能分支**: `git checkout -b feat/xxx`
3. **安装依赖**: `pnpm install`
4. **开发功能** 并确保代码质量
5. **部署测试环境**: 将开发分支merge到dev分支，自动触发ci构建，[测试环境](https://jarvis-test.yc345.tv/)
6. **合并master**: 提MR到master分支，需要至少一位审核人进行Review
7. **部署线上环境**: 代码审核通过后，在master分支上修改package.json文件的版本号，然后打上线tag
8. **上线时间注意**: 为保证上线窗口期的稳定性，此项目需要在公司的非线窗口期时间段将tag推到远程

### 提交规范

项目使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能开发
git commit -m "feat: 添加用户管理功能"

# 问题修复
git commit -m "fix: 修复登录状态异常问题"

# 文档更新
git commit -m "docs: 更新 API 文档"

# 样式调整
git commit -m "style: 调整按钮样式"

# 重构代码
git commit -m "refactor: 重构用户状态管理"

# 性能优化
git commit -m "perf: 优化图表渲染性能"

# 测试相关
git commit -m "test: 添加用户组件测试"
```

### 代码审查

- 确保代码通过 ESLint 检查
- 确保 TypeScript 类型检查通过
- 确保测试用例通过
- 遵循项目的代码风格和规范
- 添加必要的注释和文档

## 📚 常用工具和资源

### 图标资源

- **[Iconify](https://iconify.design/)** - 图标搜索和预览
- **[Icônes](https://icones.js.org/)** - Iconify 图标浏览器
- **[UnoCSS Interactive](https://uno.antfu.me/)** - UnoCSS 在线工具

### 开发资源

- **[Vue 3 文档](https://vuejs.org/)** - Vue.js 官方文档
- **[Element Plus](https://element-plus.org/)** - UI 组件库文档
- **[UnoCSS](https://unocss.dev/)** - 原子化 CSS 框架
- **[VueUse](https://vueuse.org/)** - Vue 组合式工具集

### API 文档

- **测试环境**: `https://jarvis-test-api.yc345.tv/v2/api`
- **生产环境**: `https://jarvis-api.yc345.tv/v2/api`

## ❓ 常见问题

### 环境相关

**Q: Node.js 版本要求为什么这么高？**
A: 项目使用了 Node.js 22+ 的新特性，包括更好的 ES 模块支持和性能优化。建议使用 [nvm](https://github.com/nvm-sh/nvm) 管理 Node.js 版本。

**Q: 为什么必须使用 pnpm？**
A: pnpm 提供更快的安装速度、更少的磁盘占用，并且能更好地处理依赖关系。项目的 lock 文件是 `pnpm-lock.yaml` 格式。

### 开发相关

**Q: 自动导入不生效怎么办？**
A: 检查 `auto-imports.d.ts` 和 `components.d.ts` 文件是否存在，如果不存在可以重新运行 `pnpm dev` 生成。

**Q: 图标不显示怎么办？**
A: 确保图标名称正确，格式为 `i-{collection}-{icon-name}`。可以在 [Icônes](https://icones.js.org/) 中搜索图标。

**Q: 路由不生效怎么办？**
A: 检查文件是否放在 `src/pages/` 目录下，文件名是否符合路由规范。重启开发服务器让路由重新生成。

### 构建相关

**Q: 构建失败怎么办？**
A: 首先运行 `pnpm typecheck` 检查类型错误，然后运行 `pnpm lint` 检查代码规范问题。

**Q: 构建后页面空白怎么办？**
A: 检查控制台错误信息，通常是路由配置或资源路径问题。确保 `base` 配置正确。

### 部署相关

**Q: 如何配置不同环境？**
A: 修改对应的 `.env.*` 文件，然后使用对应的构建命令：`pnpm build:test` 或 `pnpm build:prod`。

## 🙋‍♂️ 支持

如果你在使用过程中遇到问题，可以通过以下方式获取帮助：

- 可以随时飞书联系：工程效率小组成员（文丽，天宇，景齐，振泽）
- 🐛 **问题反馈**: 在 [工程效率需求工单](https://guanghe.feishu.cn/share/base/form/shrcn0VY5Nyw0YJfXyTgLdckzkg) 中提交问题
- 💡 **功能建议**: 在 [工程效率需求工单](https://guanghe.feishu.cn/share/base/form/shrcn0VY5Nyw0YJfXyTgLdckzkg) 中提交功能请求

---

**愿 bug 退散，按时下班！** 🎉
