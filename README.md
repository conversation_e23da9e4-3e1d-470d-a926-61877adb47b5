<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo_text.svg" width="320" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

## Description

JARVIS 平台后端服务


## 项目代码目录结构介绍

```
.
├── src 源码目录
│   ├── app.module.ts 项目根模块入口
│   └── main.ts 项目启动文件
|   ├── kuber k8s相关模块
│   ├── fetshu-api 飞书相关API
│   ├── global-filters 全局请求过滤 
|        器，用于对请求和响应进行处理
│   ├── gitlab gitlab相关API
│   ├── auth 用户鉴权模块
    ├── user 用户管理模块
    ├── project 项目管理模块
    ├── publisher 项目上线功能模块
    ├── pipeline 流水线相关模块
    ├── process 旧版本构建功能模块  
    ├── docker docker API模块，用于新版本流水线
    |—— common 公共模块，主要导出一些枚举值
    ├── feishu-card 飞书卡片的模板
├── nest-cli.json 构建配置文件
├── package.json npm包管理配置文件
└── README.md 项目说明文档
└── .env 配置文件
└── .ecosystem.config.js pm2部署配置文件
└── logs 服务日志输出目录
```

## 开发环境

### 配置文件

```
DB_HOST 数据库host
DB_PORT 数据库port
DB_USERNAME 数据库用户名
DB_PASSWORD 数据库密码
DB_NAME 数据库DB名
``` 

数据库使用postgresql，相关配置文件参考项目根目录下的.env文件，默认开发环境使用测试服务器的数据库，如需使用本地数据库，可以新建一个.env.local文件，并修改相关配置

```
CLONE_PATH 克隆项目的目录
```
旧版本构建会clone项目到该目录下

```
DOCKER_SOCK_PATH Docker sock文件路径，用于在流水线容器内调用本机Docker服务
SSH_PATH ssh文件路径，用于流水线容器使用SSH拉取项目仓库
LOG_PATH 构建日志输出目录，用于流水执行构建时进行日志输出
```
新版本流水线的配置，如需本地调试，请将相关配置修改为本地路径


### 开发环境运行
```bash
$ pnpm install
$ npm run start
```

### 开发环境调试
使用vscode或者其他编辑器，执行`package.json`中的`start:debug`运行启动项目即可实现代码热更新和断点调试

### API接口文档

本地启动后访问`http://localhost:8088/api`即可查看API文档


### 本地调试飞书卡片相关功能

飞书卡片交互会使用webhook的方式发送回调请求到后端服务，在用户点击飞书卡片后，发送请求到jarvis后端服务，本地调试需要使用测试机器人并配置本机内网穿透

### 内网穿透


1. 使用向日葵或`https://www.cpolar.com/`等工具配置内网穿透，获得域名。
2. 启动本地服务，使用配置的域名访问后端服务检查是否可以正常访问
3. 进入飞书开放平台,`https://open.feishu.cn/app/cli_9fc22bccfab0100e`，此机器人为开发测试使用的机器人，在`事件与回调 - 回调配置`中配置本地的域名，如没有权限设置请联系`李辉`添加机器人管理员
4. 飞书回调接口在`publisher/publisher-callback.controller.ts`中，飞书的请求会访问到此接口并执行后续逻辑，可配合断点调试功能进行调试

### 流水线本地调试

1. 本地启动Docker服务，并将在.env.local中进行相关配置
2. 本地拉取安装基础node镜像


## 部署测试环境

ssh 登录到 253 服务器上，并 cd 到项目的目录下`/home/<USER>/projects/developer-helper/jarvis-test-backend`

```bash
$ pnpm install
$ npm run deploy:test
```

### 测试环境域名

`https://jarvis-test-api.yc345.tv`

## 部署线上环境

ssh 登录到 253 服务器上，并 cd 到项目的目录下`/home/<USER>/projects/developer-helper/jarvis-prod-backend`

```bash
$ pnpm install
$ npm run deploy:prod
```

### 线上环境域名

`https://jarvis-api.yc345.tv`

## 备注

服务使用 pm2 方式部署在 253 服务器上；测试环境与线上环境是通过端口号区分：

1. 测试环境端口号：8088
2. 线上环境端口号：8899
