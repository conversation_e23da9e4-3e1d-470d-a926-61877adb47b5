{"name": "jarvis", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "rm -rf dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_ENV=development nest start --watch", "start:debug": "NODE_ENV=development PIPE_LINE=true nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "deploy:prod": "npm run build && NODE_ENV=production pm2 restart ./ecosystem.config.js", "deploy:test": "npm run build && NODE_ENV=development pm2 restart ./ecosystem.test.config.js", "deploy:test-pipeline": "npm run build && NODE_ENV=development PIPE_LINE=true pm2 restart ./ecosystem.pipeline.test.config.js", "deploy:prod-pipeline": "npm run build && NODE_ENV=production PIPE_LINE=true pm2 restart ./ecosystem.pipeline.config.js"}, "dependencies": {"@kubernetes/client-node": "^0.17.1", "@nestjs/common": "^8.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^8.0.0", "@nestjs/event-emitter": "^2.0.3", "@nestjs/jwt": "^9.0.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/platform-socket.io": "^9.1.4", "@nestjs/schedule": "^2.2.1", "@nestjs/swagger": "^6.1.1", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^8.4.7", "@types/dockerode": "^3.3.23", "@types/lodash": "^4.14.186", "@types/shelljs": "^0.8.11", "axios": "^0.27.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "dayjs": "^1.11.5", "dockerode": "^4.0.0", "lodash": "^4.17.21", "nest-winston": "^1.9.2", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "pg": "^8.8.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "shelljs": "^0.8.5", "socket.io": "^4.5.2", "swagger-ui-express": "^4.5.0", "type-fest": "^4.8.3", "typeorm": "^0.3.9", "winston": "^3.8.2"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/express": "^4.17.13", "@types/jest": "27.4.1", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "git-cz": "^4.9.0", "jest": "^27.2.5", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "config": {"commitizen": {"path": "./node_modules/git-cz"}}}