{"name": "jarvis-fe", "type": "module", "version": "6.0.0", "private": true, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b", "scripts": {"build:test": "cross-env NODE_ENV=production vite build --mode test", "build:prod": "cross-env NODE_ENV=production vite build --mode production", "dev": "vite --port 3333 --open --mode development", "lint": "eslint .", "fix": "eslint --fix", "preview": "vite preview", "preview-https": "serve dist", "test": "vitest", "test:e2e": "cypress open", "test:unit": "vitest", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "prebuild": "pnpm lint && pnpm typecheck", "up": "taze major -I", "postinstall": "npx simple-git-hooks", "sizecheck": "npx vite-bundle-visualizer"}, "dependencies": {"@css-render/vue3-ssr": "^0.15.14", "@guanghe-pub/auto-native-query": "^0.0.2", "@unhead/vue": "^1.11.20", "@unocss/reset": "66.1.0-beta.3", "@vueuse/core": "^12.7.0", "@vueuse/head": "^2.0.0", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "axios": "^1.8.2", "beasties": "^0.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "2.9.5", "nprogress": "^0.2.0", "pinia": "^3.0.1", "sass": "^1.89.2", "socket.io-client": "^4.8.1", "vue": "^3.5.13", "vue-demi": "^0.14.10", "vue-router": "^4.5.0", "workbox-window": "^7.3.0"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@iconify-json/akar-icons": "^1.2.2", "@iconify-json/carbon": "^1.2.8", "@iconify/json": "^2.2.311", "@shikijs/markdown-it": "^3.1.0", "@types/markdown-it-link-attributes": "^3.0.5", "@types/nprogress": "^0.2.3", "@unocss/eslint-config": "66.1.0-beta.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue-macros/volar": "^0.30.15", "@vue/test-utils": "^2.4.6", "cross-env": "^7.0.3", "cypress": "^14.1.0", "cypress-vite": "^1.6.0", "eslint": "^9.21.0", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-format": "^1.0.1", "https-localhost": "^4.7.1", "lint-staged": "^15.4.3", "markdown-it-link-attributes": "^4.0.1", "pnpm": "^10.5.2", "rollup": "^4.34.8", "shiki": "^3.1.0", "simple-git-hooks": "^2.11.1", "taze": "^18.6.0", "typescript": "^5.8.2", "unocss": "66.1.0-beta.3", "unocss-preset-better-breakpoints": "^0.0.1", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-macros": "^2.14.5", "unplugin-vue-markdown": "^28.3.1", "unplugin-vue-router": "^0.11.2", "vite": "^6.2.0", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-inspect": "^11.0.0", "vite-plugin-pwa": "^0.21.1", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-vue-layouts": "^0.11.0", "vite-ssg": "^25.2.0", "vite-ssg-sitemap": "^0.8.1", "vitest": "^3.0.7", "vue-tsc": "^2.2.4"}, "resolutions": {"unplugin": "^2.2.0", "vite": "^6.2.0", "vite-plugin-inspect": "^11.0.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged && pnpm prebuild"}, "lint-staged": {"*": "eslint --fix"}}