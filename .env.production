
#飞书应用配置
appId=cli_a379a9767ce1100e
appSecret=DRIIy52rgXgjJinTpRgQ0fHjuLm14iXp
messageChatId=oc_a16029e91a60786e5f7cb60961aeb047
gitLabAccessToken=********************
#过期时间 2025-07-14
#值班运维顺序
dutyOrder=0

#是否正处于寒暑假窗口时段
holidayLimit=yes

# 请注意不要在开发环境链接生产环境数据库！！！！！
# 数据库配置
DB_HOST=**********
DB_PORT=5412
DB_USERNAME=postgres
DB_PASSWORD=7to12pg12
DB_NAME=jarvis_prod

# git clone 目录
CLONE_PATH=/home/<USER>/jarvisProjects/

# docker.sock
DOCKER_SOCK_PATH=/var/run/docker.sock

# git ssh
SSH_PATH=/home/<USER>/.ssh

# 日志文件

LOG_PATH=/home/<USER>/pipeline_logs

# 异常上线多维表格
EXCEPTION_BITABLE_APPTOKEN = R10PbaGn9a5DiWsVctocSk67nc9
EXCEPTION_BITABLE_TABLEID = tblMzLBtpRUuud4X
