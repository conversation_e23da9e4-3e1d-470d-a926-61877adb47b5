FROM node:22.12.0-alpine
ARG CACHEBUST=1
WORKDIR /app

RUN apk add curl bash python3 make git openssh-client tzdata docker \
        && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
        && echo "Asia/Shanghai" > /etc/timezone \
        && rm -rf /var/cache/apk/*

COPY .npmrc /home/<USER>

RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g pnpm
# 设置腾讯镜像源1
ENV COREPACK_NPM_REGISTRY=https://mirrors.cloud.tencent.com/npm
RUN npm install -g corepack@latest
# 启用 corepack 支持
RUN --mount=type=cache,id=corepack-cache,target=/root/.cache/node/corepack \
  corepack enable


