#服务端口
SERVER_PORT=5001

#Timezone
TZ=Asia/Shanghai

#大模型供应商配置,支持 zhipuai , openai , deepseek or ollama, doubao
LLM_PROVIDER=doubao

#ZhipuAI settings
ZHIPUAI_API_KEY=xxxx
ZHIPUAI_API_MODEL=GLM-4-Flash

#OpenAI settings
OPENAI_API_KEY=xxxx
OPENAI_API_BASE_URL=https://api.openai.com
OPENAI_API_MODEL=gpt-4o-mini

#DeepSeek settings
DEEPSEEK_API_KEY=
DEEPSEEK_API_BASE_URL=https://api.deepseek.com
DEEPSEEK_API_MODEL=deepseek-chat

#DOUBAO settings
DOUBAO_API_KEY=xxxx
DOUBAO_API_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DOUBAO_API_MODEL=deepseek-r1-distill-qwen-32b-250120

#OllaMA settings; 注意: 如果使用 Docker 部署，127.0.0.1 指向的是容器内部的地址。请将其替换为实际的 Ollama服务器IP地址。
#OLLAMA_API_BASE_URL=http://127.0.0.1:11434
OLLAMA_API_BASE_URL=http://host.docker.internal:11434
OLLAMA_API_MODEL=deepseek-r1:latest

#支持review的文件类型
SUPPORTED_EXTENSIONS=.java,.py,.php,.yml,.vue,.go,.c,.cpp,.h,.js,.css,.md,.sql
REVIEW_MAX_TOKENS=20000
#Review风格，支持professional|sarcastic|gentle|humorous
REVIEW_STYLE=professional

#钉钉配置
DINGTALK_ENABLED=0
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx

#企业微信配置
WECOM_ENABLED=0
WECOM_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx

#飞书配置
FEISHU_ENABLED=0
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx

#日志配置
LOG_FILE=log/app.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=3
LOG_LEVEL=DEBUG

#工作日报发送时间
REPORT_CRONTAB_EXPRESSION=0 18 * * 1-5

#Gitlab配置
#GITLAB_URL={YOUR_GITLAB_URL} #部分老版本Gitlab webhook不传递URL，需要开启此配置，示例：https://gitlab.example.com
#GITLAB_ACCESS_TOKEN={YOUR_GITLAB_ACCESS_TOKEN} #系统会优先使用此GITLAB_ACCESS_TOKEN，如果未配置，则使用Webhook 传递的Secret Token

#Github配置(如果使用 Github 作为代码托管平台，需要配置此项)
#GITHUB_ACCESS_TOKEN={YOUR_GITHUB_ACCESS_TOKEN}

# 开启Push Review功能(如果不需要push事件触发Code Review，设置为0)
PUSH_REVIEW_ENABLED=1

# Dashboard登录用户名和密码
DASHBOARD_USER=admin
DASHBOARD_PASSWORD=admin

# queue (async, rq)
QUEUE_DRIVER=async
REDIS_HOST=redis
# REDIS_HOST=127.0.0.1
# REDIS_PORT=6379

# gitlab domain slugged
WORKER_QUEUE=git_test_com
