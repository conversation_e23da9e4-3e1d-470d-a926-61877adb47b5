#服务端口
SERVER_PORT=5001

#Timezone
TZ=Asia/Shanghai

#大模型供应商配置,支持 deepseek, openai,zhip<PERSON><PERSON>,qwen 和 ollama
LLM_PROVIDER=deepseek

#DeepSeek settings
DEEPSEEK_API_KEY=2e570823-aab3-44cf-9025-fcac1fcbaf60
DEEPSEEK_API_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DEEPSEEK_API_MODEL=ep-20250422165922-qn72v

#DOUBAO settings
#DOUBAO_API_KEY=826b69f6-b306-40db-9857-7d9da4162a70
#DOUBAO_API_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
#DOUBAO_API_MODEL=deepseek-r1-distill-qwen-32b-250120

#支持review的文件类型
SUPPORTED_EXTENSIONS=.js,.jsx,.ts,.tsx,.vue,.html,.css,.scss,.less,.json,.yaml,.yml,.md,.java,.py,.go,.php,.cs,.rs,.rb,.sql,.sh,.dockerfile
#每次 Review 的最大 Token 限制（超出部分自动截断）
REVIEW_MAX_TOKENS=20000
#Review 风格选项：professional（专业） | sarcastic（毒舌） | gentle（温和） | humorous（幽默）
REVIEW_STYLE=gentle

#钉钉配置
DINGTALK_ENABLED=0
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx

#企业微信配置
WECOM_ENABLED=0
WECOM_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx

#飞书配置
FEISHU_ENABLED=1
FEISHU_WEBHOOK_URL=https://www.feishu.cn/flow/api/trigger-webhook/8191d7a173cb73189828ed1280196f04

#日志配置
LOG_FILE=log/app.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=3
LOG_LEVEL=DEBUG

#工作日报发送时间
#REPORT_CRONTAB_EXPRESSION=0 18 * * 1-5

#Gitlab配置
#GITLAB_URL={YOUR_GITLAB_URL} #部分老版本Gitlab webhook不传递URL，需要开启此配置，示例：https://gitlab.example.com
#系统会优先使用此GITLAB_ACCESS_TOKEN，如果未配置，则使用Webhook 传递的Secret Token
GITLAB_ACCESS_TOKEN=********************

# 开启Push Review功能(如果不需要push事件触发Code Review，设置为0)
PUSH_REVIEW_ENABLED=1
# Dashboard登录用户名和密码
DASHBOARD_USER=admin
DASHBOARD_PASSWORD=admin

# queue (async, rq)
QUEUE_DRIVER=async
REDIS_HOST=redis
# REDIS_HOST=127.0.0.1
# REDIS_PORT=6379

# gitlab domain slugged
WORKER_QUEUE=git_test_com
