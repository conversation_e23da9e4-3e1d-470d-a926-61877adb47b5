# 后端代码规范（精简版）

## 1. 命名规范

- **核心原则**: 清晰、简洁、避免冗余。
- **包/模块**: `lowercase` 或 `snake_case` (例: `userservice`)，避免使用 `util`, `common` 等通用名称。
- **变量/函数**: `camelCase` (Go/Java) 或 `snake_case` (Python)。
- **常量**: `UPPER_SNAKE_CASE` (例: `MAX_RETRY_COUNT`)。
- **类/接口**: `PascalCase` (例: `User`, `UserService`)。
- **布尔值**: 使用 `is`, `has`, `can` 开头 (例: `isValid`)。
- **【强制】** 函数返回两个或多个相同类型的参数时，必须为返回值命名以作区分。

## 2. 代码格式

- **格式化**: **【强制】** 使用自动化格式工具 (如 `gofmt`, `black`)。
- **行长**: 单行代码不超过 120 字符。
- **【强制】** **减少嵌套**: 优先处理错误和特殊情况，尽早返回 (Guard Clauses)。
- **【强制】** **变量作用域**: 尽可能缩小变量的作用域。
- **【强制】** **禁止裸返回**: 不要在中等长度的函数中使用裸返回 (`return`)，应明确返回具体变量。
- **【强制】** **初始化**: 
    - `map` 在写入前必须初始化 (`make`)。
    - 使用 `:=` 初始化非零值变量。
    - 结构体字面量必须指定字段名。
- **【强制】** **切片初始化**: 推荐使用 `var s []int` (初始化为nil切片) 而非 `s := []int{}`。

## 3. 注释规范

- **核心**: 注释应解释"为什么"而不是"做什么"。
- **【强制】** **文档注释**: 所有导出的类型、函数、方法都必须有文档注释。
- **同步更新**: 保持注释与代码同步。

## 4. 错误处理

- **【强制】** **禁止 `panic`**: 正常流程中禁止使用 `panic`，必须返回 `error`。
- **【强制】** **错误位置**: `error` 必须是函数最后一个返回参数。
- **【强制】** **错误类型**: 返回的错误类型必须是 `error` 接口。
- **只处理一次**: 不要在日志记录后再次返回 `error`，应选择一种方式处理。
- **【强制】** **错误判断**: 必须使用 `errors.Is` 和 `errors.As` 进行错误类型判断。

## 5. 并发编程 (以Go为例)

- **【强制】** **goroutine管理**: 启动 goroutine 时必须明确其退出机制，防止泄露。
- **【强制】** **Context**: 异步或可能阻塞的函数，第一个参数必须是 `context.Context`。
- **【强制】** **Channel大小**: Channel 的 `size` 必须为 0 (无缓冲) 或 1。
- **同步原语**: 优先使用高级同步原语 (如 `sync.Mutex`)。

## 6. 数据库规范

- **连接池**: 合理配置和使用连接池。
- **【强制】** **建表**: 
    - 必须有主键，字符集使用 `UTF8`。
    - 查询和排序字段必须创建索引。
    - 非空字段使用 `NOT NULL` 约束，唯一约束由数据库保证。
- **【强制】** **SQL**: 
    - 事务中避免长耗时操作，并尽快提交或回滚。
    - 禁止在线查询进行全表扫描。
    - 优先使用 `RETURNING` 子句减少数据库交互。

## 7. 缓存规范 (以Redis为例)

- **Key设计**: `业务名:表名:id` 格式，简洁且有语义。**【强制】** Key不包含特殊字符。
- **【强制】** **拒绝Big Key**: `string` < 10KB，集合类型元素 < 5000。
- **【强制】** **过期时间**: 必须为Key设置合理的过期时间。
- **缓存策略**: 推荐"先操作数据库，再删除缓存"。

## 8. 工程规范

- **项目结构**: 遵循团队约定的分层架构 (如 `cmd`, `internal/biz`, `internal/service`, `internal/data`)。
- **日志**: **【强制】** 使用统一的结构化日志中间件，并关联链路ID。
- **链路追踪**: **【强制】** 新服务必须接入链路追踪系统 (如 OpenTelemetry)。
- **版本管理**: **【强制】** 遵循语义化版本控制，统一管理关键依赖版本。

### 8.1 接口规范 (Interface)
- **【强制】** **归属**: 接口定义应归属于使用者，而非实现者。
- **【强制】** **指针**: 禁止使用指向接口类型的指针。
- **【强制】** **验证**: 使用编译期断言验证接口实现 (`var _ http.Handler = (*Handler)(nil)`)。

### 8.2 导入规范 (Imports)
- **【强制】** **分组**: 导入应分为三组：标准库、第三方库、项目内库，并使用`goimports`格式化。
- **【强制】** **别名**: 仅在为避免命名冲突时才使用别名。
