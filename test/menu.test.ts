import { describe, expect, it } from 'vitest'
import { transformMenus } from '../src/helpers/menu'

describe('helpers/menu', () => {
  it('常规转换测试', () => {
    expect(transformMenus([
      { name: 'a', sort: 1 },
      { name: 'a1', sort: 3 },
      { name: 'a2', sort: 4 },
      { name: 'b', parentMenu: { name: 'a' }, sort: 2 },
      { name: 'c', parentMenu: { name: 'b' }, sort: 3 },
      { name: 'd', parentMenu: { name: 'a' }, sort: 4 },
      { name: 'e', parentMenu: { name: 'd' }, sort: 5 },
    ])).toEqual([
      {
        name: 'a',
        children: [
          {
            name: 'b',
            parentMenu: { name: 'a' },
            sort: 2,
            children: [
              {
                name: 'c',
                parentMenu: { name: 'b' },
                sort: 3,
              },
            ],
          },
          {
            name: 'd',
            parentMenu: { name: 'a' },
            sort: 4,
            children: [
              {
                name: 'e',
                parentMenu: { name: 'd' },
                sort: 5,
              },
            ],
          },
        ],
      },
      {
        name: 'a1',
        sort: 3,
      },
      {
        name: 'a2',
        sort: 4,
      },
    ])
  })
})
