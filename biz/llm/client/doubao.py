import os
from typing import Dict, List, Optional

from openai import OpenAI

from biz.llm.client.base import BaseClient
from biz.llm.types import NotGiven, NOT_GIVEN
from biz.utils.log import logger


class DoubaoClient(BaseClient):
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("DOUBAO_API_KEY")
        self.base_url = os.getenv("DOUBAO_API_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
        if not self.api_key:
            raise ValueError("API key is required. Please provide it or set it in the environment variables.")

        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url) # DeepSeek supports OpenAI API SDK
        self.default_model = os.getenv("DOUBAO_API_MODEL", "deepseek-r1-distill-qwen-32b-250120")

    def completions(self,
                    messages: List[Dict[str, str]],
                    model: Optional[str] | NotGiven = NOT_GIVEN,
                    ) -> str:
        try:
            model = model or self.default_model
            logger.debug(f"Sending request to Doubao API. Model: {model}, Messages: {messages}")
            
            completion = self.client.chat.completions.create(
                model=model,
                messages=messages
            )
            
            if not completion or not completion.choices:
                logger.error("Empty response from DeepSeek API")
                return "AI服务返回为空，请稍后重试"
                
            return completion.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Doubao API error: {str(e)}")
            # 检查是否是认证错误
            if "401" in str(e):
                return "Doubao API认证失败，请检查API密钥是否正确"
            elif "404" in str(e):
                return "Doubao API接口未找到，请检查API地址是否正确"
            else:
                return f"调用Doubao API时出错: {str(e)}"
