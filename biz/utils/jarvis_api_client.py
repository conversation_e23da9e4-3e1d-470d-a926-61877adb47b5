import os
import requests
from typing import Dict, List, Optional
from biz.utils.log import logger


class JarvisApiClient:
    """JARVIS API客户端，用于获取AI CodeReview群配置"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or os.getenv('JARVIS_API_URL', '')
        self.internal_token = os.getenv('JARVIS_INTERNAL_TOKEN', '')
        
    def get_user_codereview_groups(self, gitlab_username: str, project_id: str = None, author_email: str = None) -> Dict:
        """
        获取用户的CodeReview飞书群配置
        
        Args:
            gitlab_username: GitLab用户名
            project_id: 项目ID（可选）
            author_email: 作者邮箱（可选，作为备选映射方式）
            
        Returns:
            Dict: 包含groups、mentionUsers、fallbackToDefault的字典
        """
        if not self.base_url:
            logger.warning("JARVIS_API_URL未配置，无法获取群配置")
            return {'groups': [], 'mentionUsers': [], 'fallbackToDefault': True}
            
        try:
            url = f"{self.base_url}/ai-codereview/user-groups/{gitlab_username}"
            
            params = {}
            if project_id:
                params['projectId'] = project_id
            if author_email:
                params['authorEmail'] = author_email
                
            headers = {
                'X-Internal-Service': 'ai-codereview',
                'Content-Type': 'application/json'
            }
            
            if self.internal_token:
                headers['Authorization'] = f'Bearer {self.internal_token}'
            
            logger.info(f"调用JARVIS API获取用户群配置: {url}, params: {params}")
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"成功获取用户群配置: {result}")
                return result
            else:
                logger.error(f"获取用户群配置失败: {response.status_code}, {response.text}")
                return {'groups': [], 'mentionUsers': [], 'fallbackToDefault': True}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"调用JARVIS API异常: {e}")
            return {'groups': [], 'mentionUsers': [], 'fallbackToDefault': True}
        except Exception as e:
            logger.error(f"获取用户群配置时发生未知错误: {e}")
            return {'groups': [], 'mentionUsers': [], 'fallbackToDefault': True}
    
    def send_to_feishu_group_with_mention(self, webhook_url: str, content: str, mention_users: List[str] = None) -> bool:
        """
        发送带@功能的飞书消息
        
        Args:
            webhook_url: 飞书机器人webhook地址
            content: 消息内容
            mention_users: 需要@的用户飞书ID列表
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构造带@的消息内容
            mention_text = ""
            if mention_users:
                for user_id in mention_users:
                    mention_text += f'<at user_id="{user_id}"></at> '
            
            full_content = f"{mention_text}\n{content}" if mention_text else content
            
            data = {
                "msg_type": "interactive",
                "card": {
                    "schema": "2.0",
                    "body": {
                        "direction": "vertical",
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": full_content
                            }
                        ]
                    },
                    "header": {
                        "title": {
                            "tag": "plain_text",
                            "content": "AI Code Review"
                        },
                        "template": "blue"
                    }
                }
            }
            
            response = requests.post(webhook_url, json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('msg') == 'success':
                    logger.info(f"飞书群消息发送成功: {webhook_url}")
                    return True
                else:
                    logger.error(f"飞书群消息发送失败: {result}")
                    return False
            else:
                logger.error(f"飞书群消息发送失败: {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送飞书群消息时发生错误: {e}")
            return False
