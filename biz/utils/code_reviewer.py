import abc
import os
import re
from typing import Dict, Any, List

import yaml
from jinja2 import Template

from biz.llm.factory import Factory
from biz.utils.log import logger
from biz.utils.token_util import count_tokens, truncate_text_by_tokens


# 前端文件扩展名
FRONTEND_EXTENSIONS = {'.js', '.jsx', '.ts', '.tsx', '.vue', '.html', '.css', '.scss', '.less'}

# 后端文件扩展名
BACKEND_EXTENSIONS = {'.java', '.py', '.go', '.php', '.cs', '.rs', '.rb', '.sql', '.sh', '.dockerfile', '.yml'}


def identify_project_type(file_paths: List[str]) -> str:
    """
    根据文件路径列表识别项目类型
    优先级规则：
    1. 如果存在.go文件，返回backend
    2. 如果存在.vue文件，返回frontend
    3. 否则按文件数量统计判断
    :param file_paths: 文件路径列表
    :return: 'frontend', 'backend', 或 'mixed'
    """
    if not file_paths:
        return 'mixed'

    has_go = False
    has_vue = False
    frontend_count = 0
    backend_count = 0

    for file_path in file_paths:
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path.lower())

        # 优先级判断
        if ext == '.go':
            has_go = True
        elif ext == '.vue':
            has_vue = True

        # 统计文件数量
        if ext in FRONTEND_EXTENSIONS:
            frontend_count += 1
        elif ext in BACKEND_EXTENSIONS:
            backend_count += 1

    # 优先级规则
    if has_go:
        return 'backend'
    elif has_vue:
        return 'frontend'

    # 如果前端文件数量占主导，返回frontend
    if frontend_count > backend_count:
        return 'frontend'
    # 如果后端文件数量占主导，返回backend
    elif backend_count > frontend_count:
        return 'backend'
    # 如果数量相等或都为0，返回mixed
    else:
        return 'mixed'


def load_team_standards(project_type: str) -> str:
    """
    根据项目类型加载相应的团队代码规范
    :param project_type: 项目类型 ('frontend', 'backend', 'mixed')
    :return: 团队代码规范内容
    """
    standards_content = ""

    try:
        if project_type == 'frontend':
            with open("conf/frontend_code_standard_zh.md", "r", encoding="utf-8") as f:
                standards_content = f.read()
                logger.info("已加载前端代码规范")
        elif project_type == 'backend':
            with open("conf/backend_code_standard_zh.md", "r", encoding="utf-8") as f:
                standards_content = f.read()
                logger.info("已加载后端代码规范")
        elif project_type == 'mixed':
            # 混合项目不加载团队规范
            logger.info("混合项目类型，不加载团队代码规范")
            standards_content = ""
    except FileNotFoundError as e:
        logger.warning(f"无法加载团队代码规范文件: {e}")
        standards_content = ""
    except Exception as e:
        logger.error(f"加载团队代码规范时发生错误: {e}")
        standards_content = ""

    return standards_content


class BaseReviewer(abc.ABC):
    """代码审查基类"""

    def __init__(self, prompt_key: str):
        self.client = Factory().getClient()
        self.prompts = self._load_prompts(prompt_key, os.getenv("REVIEW_STYLE", "professional"))

    def _load_prompts(self, prompt_key: str, style="professional", team_standards="") -> Dict[str, Any]:
        """加载提示词配置"""
        prompt_templates_file = "conf/prompt_templates.yml"
        try:
            # 在打开 YAML 文件时显式指定编码为 UTF-8，避免使用系统默认的 GBK 编码。
            with open(prompt_templates_file, "r", encoding="utf-8") as file:
                prompts = yaml.safe_load(file).get(prompt_key, {})

                # 使用Jinja2渲染模板
                def render_template(template_str: str) -> str:
                    return Template(template_str).render(style=style, team_standards=team_standards)

                system_prompt = render_template(prompts["system_prompt"])
                user_prompt = render_template(prompts["user_prompt"])

                return {
                    "system_message": {"role": "system", "content": system_prompt},
                    "user_message": {"role": "user", "content": user_prompt},
                }
        except (FileNotFoundError, KeyError, yaml.YAMLError) as e:
            logger.error(f"加载提示词配置失败: {e}")
            raise Exception(f"提示词配置加载失败: {e}")

    def call_llm(self, messages: List[Dict[str, Any]]) -> str:
        """调用 LLM 进行代码审核"""
        logger.info(f"向 AI 发送代码 Review 请求, messages: {messages}")
        review_result = self.client.completions(messages=messages)
        logger.info(f"收到 AI 返回结果: {review_result}")
        return review_result

    @abc.abstractmethod
    def review_code(self, *args, **kwargs) -> str:
        """抽象方法，子类必须实现"""
        pass


class CodeReviewer(BaseReviewer):
    """代码 Diff 级别的审查"""

    def __init__(self):
        super().__init__("code_review_prompt")
        self.team_standards = ""  # 初始化团队规范为空

    def review_and_strip_code(self, changes_text: str, commits_text: str = "", file_paths: List[str] = None) -> str:
        """
        Review判断changes_text超出取前REVIEW_MAX_TOKENS个token，超出则截断changes_text，
        调用review_code方法，返回review_result，如果review_result是markdown格式，则去掉头尾的```
        :param changes_text:
        :param commits_text:
        :param file_paths: 文件路径列表，用于识别项目类型
        :return:
        """
        # 根据文件路径识别项目类型并加载团队规范
        if file_paths:
            project_type = identify_project_type(file_paths)
            self.team_standards = load_team_standards(project_type)
            logger.info(f"识别项目类型为: {project_type}")

            # 重新加载带有团队规范的提示词
            self.prompts = self._load_prompts("code_review_prompt",
                                            os.getenv("REVIEW_STYLE", "professional"),
                                            self.team_standards)

        # 如果超长，取前REVIEW_MAX_TOKENS个token
        review_max_tokens = int(os.getenv("REVIEW_MAX_TOKENS", 10000))
        # 如果changes为空,打印日志
        if not changes_text:
            logger.info("代码为空, diffs_text = %", str(changes_text))
            return "代码为空"

        # 计算tokens数量，如果超过REVIEW_MAX_TOKENS，截断changes_text
        tokens_count = count_tokens(changes_text)
        if tokens_count > review_max_tokens:
            changes_text = truncate_text_by_tokens(changes_text, review_max_tokens)

        review_result = self.review_code(changes_text, commits_text).strip()
        if review_result.startswith("```markdown") and review_result.endswith("```"):
            return review_result[11:-3].strip()
        return review_result

    def review_code(self, diffs_text: str, commits_text: str = "") -> str:
        """Review 代码并返回结果"""
        messages = [
            self.prompts["system_message"],
            {
                "role": "user",
                "content": self.prompts["user_message"]["content"].format(
                    diffs_text=diffs_text, commits_text=commits_text
                ),
            },
        ]
        return self.call_llm(messages)

    @staticmethod
    def parse_review_score(review_text: str) -> int:
        """解析 AI 返回的 Review 结果，返回评分"""
        if not review_text:
            return 0
        match = re.search(r"总分[:：]\s*(\d+)分?", review_text)
        return int(match.group(1)) if match else 0

