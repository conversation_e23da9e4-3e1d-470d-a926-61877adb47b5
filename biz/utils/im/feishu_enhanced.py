import os
from typing import Dict, List
from biz.utils.log import logger
from biz.utils.jarvis_api_client import JarvisApiClient
from biz.utils.im import notifier


class FeishuEnhancedNotifier:
    """增强的飞书通知器，支持JARVIS群配置和@功能"""
    
    def __init__(self):
        self.jarvis_client = JarvisApiClient()
        self.enabled = os.environ.get('FEISHU_ENABLED', '0') == '1'
    
    def send_codereview_notification(self, content: str, webhook_data: Dict) -> bool:
        """
        发送CodeReview通知，支持JARVIS群配置和兜底策略
        
        Args:
            content: 消息内容
            webhook_data: GitLab webhook数据
            
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            logger.info("飞书推送未启用")
            return False
            
        try:
            # 提取用户和项目信息
            gitlab_username = webhook_data.get('user', {}).get('username')
            project_id = str(webhook_data.get('project', {}).get('id', ''))
            project_name = webhook_data.get('project', {}).get('name', '')
            
            # 尝试从commit中获取作者邮箱作为备选
            author_email = None
            commits = webhook_data.get('commits', [])
            if commits:
                author_email = commits[0].get('author', {}).get('email')
            
            logger.info(f"准备发送CodeReview通知: gitlab_username={gitlab_username}, project_id={project_id}, author_email={author_email}")
            
            # 调用JARVIS API获取群配置
            group_config = self.jarvis_client.get_user_codereview_groups(
                gitlab_username=gitlab_username,
                project_id=project_id,
                author_email=author_email
            )
            
            # 如果找到了配置的群，发送到配置的群
            if group_config.get('groups') and not group_config.get('fallbackToDefault'):
                success_count = 0
                for group in group_config['groups']:
                    webhook_url = group.get('feishu_bot_webhook')
                    if webhook_url:
                        success = self.jarvis_client.send_to_feishu_group_with_mention(
                            webhook_url=webhook_url,
                            content=content,
                            mention_users=group_config.get('mentionUsers', [])
                        )
                        if success:
                            success_count += 1
                
                if success_count > 0:
                    logger.info(f"成功发送到 {success_count} 个配置的飞书群")
                    return True
                else:
                    logger.warning("所有配置的飞书群发送失败，使用兜底策略")
            else:
                logger.info(f"未找到用户 {gitlab_username} 的专用飞书群配置，使用兜底策略")
            
            # 兜底策略：使用现有的飞书通知逻辑
            return self._send_fallback_notification(content, project_name, webhook_data)
            
        except Exception as e:
            logger.error(f"发送CodeReview通知时发生错误: {e}")
            # 发生异常时也使用兜底策略
            return self._send_fallback_notification(content, webhook_data.get('project', {}).get('name', ''), webhook_data)
    
    def _send_fallback_notification(self, content: str, project_name: str, webhook_data: Dict) -> bool:
        """
        兜底策略：使用现有的飞书通知逻辑
        
        Args:
            content: 消息内容
            project_name: 项目名称
            webhook_data: webhook数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            logger.info("使用兜底策略发送飞书通知")
            
            # 获取URL slug用于匹配特定项目的webhook
            url_slug = None
            repository = webhook_data.get('repository')
            if repository:
                homepage = repository.get('homepage')
                if homepage:
                    from biz.gitlab.webhook_handler import slugify_url
                    url_slug = slugify_url(homepage)
            
            # 使用现有的通知器发送消息
            notifier.send_notification(
                content=content,
                msg_type='markdown',
                title='AI Code Review',
                project_name=project_name,
                url_slug=url_slug
            )
            
            logger.info("兜底策略发送完成")
            return True
            
        except Exception as e:
            logger.error(f"兜底策略发送失败: {e}")
            return False
