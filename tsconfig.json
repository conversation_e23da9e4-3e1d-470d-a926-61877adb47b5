{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"~/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vitest", "vite/client", "element-plus/global", "vite-plugin-vue-layouts/client", "vite-plugin-pwa/client", "unplugin-vue-macros/macros-global", "unplugin-vue-router/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@vue-macros/volar/define-models", "@vue-macros/volar/define-slots"]}, "exclude": ["dist", "node_modules", "cypress"]}