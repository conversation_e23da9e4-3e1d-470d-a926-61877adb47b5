# 这个是使用了Redis Queue的版本
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: app
    image: ghcr.io/sunmh207/ai-codereview-gitlab:1.3.7
    ports:
      - "5001:5001"
      - "5002:5002"
    volumes:
      - data:/app/data
      - log:/app/log
    env_file:
      - ./conf/.env
    depends_on:
      redis:
        condition: service_started
    restart: unless-stopped

  worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: worker
    image: ghcr.io/sunmh207/ai-codereview-gitlab:1.3.7-worker
    volumes_from:
      - app
    env_file:
      - ./conf/.env
    # environment:
    #   - WORKER_QUEUE=git_test_com
      # - OLLAMA_API_BASE_URL=https://ollama.test1.com:11434
    depends_on:
      redis:
        condition: service_started
    restart: unless-stopped

#  worker2:
#    build:
#      context: .
#      dockerfile: Dockerfile
#      target: worker
#    image: ghcr.io/sunmh207/ai-codereview-gitlab:1.3.7-worker
#    volumes_from:
#      - app
#    env_file:
#      - .env
#    environment:
#      - WORKER_QUEUE=git_test2_com
#      - OLLAMA_API_BASE_URL=https://ollama.test2.com:11434
#    depends_on:
#      redis:
#        condition: service_started
#    restart: unless-stopped

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  data:
  log:
  redis_data:
