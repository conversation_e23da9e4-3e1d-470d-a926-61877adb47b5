export interface IMenu {
  name: string
  path?: string
  sort?: number
  icon?: string
  parentMenu?: IMenu
  children?: IMenu[]
}

export function transformMenus(menus: IMenu[]): IMenu[] {
  const nodeMap = new Map<string, IMenu>()
  const rootNames = new Set<string>()

  const processNode = (current: IMenu, parent?: IMenu) => {
    const existing = nodeMap.get(current.name)

    if (existing) {
      const merged = Object.assign({}, current, {
        children: existing.children,
      })
      nodeMap.set(current.name, merged)
    }
    else {
      nodeMap.set(current.name, { ...current })
      rootNames.add(current.name)
    }

    if (parent) {
      const parentNode = nodeMap.get(parent.name) || processNode(parent)
      if (!parentNode.children) {
        parentNode.children = []
      }
      parentNode.children.push(nodeMap.get(current.name)!)
      rootNames.delete(current.name)
    }

    return nodeMap.get(current.name)!
  }

  menus.forEach((menu) => {
    let current: IMenu | undefined = menu
    while (current) {
      const parent: IMenu | undefined = current.parentMenu
      processNode({ ...current }, parent)
      current = parent
    }
  })

  const removeEmptyChildren = (nodes: IMenu[]) => {
    nodes.forEach((node) => {
      if (node.children) {
        if (node.children.length === 0) {
          delete node.children
        }
        else {
          removeEmptyChildren(node.children)
        }
      }
    })
  }

  const result = Array.from(nodeMap.values())
    .filter(node => rootNames.has(node.name))
    .sort((a, b) => (a?.sort || 0) - (b?.sort || 0))

  removeEmptyChildren(result)

  const sortChildren = (nodes: IMenu[]) => {
    nodes.forEach((node) => {
      if (node.children) {
        node.children.sort((a, b) => (a?.sort || 0) - (b?.sort || 0))
        sortChildren(node.children)
      }
    })
  }

  sortChildren(result)
  return result
}
