import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '~/stores/user'
import { getApiDomain } from '~/utils/env'

const noAuthUrlString = ['data-inspector']

const service = axios.create({
  baseURL: getApiDomain(),
})

service.interceptors.request.use(
  (config) => {
    const isNoAuthUrl = noAuthUrlString.some(urlStr => config.url?.includes(urlStr))
    if (isNoAuthUrl) {
      return config
    }
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = userStore.token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

service.interceptors.response.use(
  (response) => {
    const res = response.data
    if (res.code === 401) {
      ElMessageBox.confirm('登录已失效，请重新登录', '提示', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        const userStore = useUserStore()
        userStore.logout()
        location.reload()
      })
      return Promise.reject(new Error(res.message || 'Error'))
    }
    if (res.code >= 300) {
      ElMessage({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000,
      })
      return Promise.reject(new Error(res.message || 'Error'))
    }
    if (res.data) {
      return res.data
    }
    return res
  },
  (error) => {
    ElMessage({
      message: error.message,
      type: 'error',
      duration: 5 * 1000,
    })
    return Promise.reject(error)
  },
)

export default service
