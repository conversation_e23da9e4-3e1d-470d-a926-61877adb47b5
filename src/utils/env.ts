/**
 * 环境配置工具函数
 */

// 获取当前环境
export const getAppEnv = () => import.meta.env.VITE_APP_ENV

// 环境判断
export const isDev = () => getAppEnv() === 'development'
export const isTest = () => getAppEnv() === 'test'
export const isProd = () => getAppEnv() === 'production'

// 获取环境名称
export const getEnvName = () => import.meta.env.VITE_ENV_NAME || '未知环境'

// 获取API域名
export const getApiDomain = () => import.meta.env.VITE_DOMAIN

// 获取飞书应用ID
export const getFeishuAppId = () => import.meta.env.VITE_FEISHU_APPID

// 环境配置对象
export const envConfig = {
  env: getAppEnv(),
  envName: getEnvName(),
  apiDomain: getApiDomain(),
  feishuAppId: getFeishuAppId(),
  isDev: isDev(),
  isTest: isTest(),
  isProd: isProd(),
}
