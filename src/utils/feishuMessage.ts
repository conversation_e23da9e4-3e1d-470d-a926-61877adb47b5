interface MessageContent {
  publisherId: string
  name: string
  tag: string
  content: string
  developerFeishuId: string
  developerName: string
  testerFeishuId: string
  testerName: string
  duty: string
  deployerName?: string
  deployerFeishuId?: string
}

interface ConfirmMessageContent {
  name: string
  tag: string
  content: string
  developerFeishuId: string
  developerName: string
  testerFeishuId: string
  testerName: string
  deployerName: string
  publisherId: string
}

interface PassTestMessageContent {
  name: string
  content: string
  testerFeishuId: string
  tag: string
  developerName: string
  testerName: string
}

export const createConfrimMessage = (
  messageType: 'deployed' | 'rolledBack',
  {
    name,
    tag,
    developerName,
    testerName,
    content,
    developerFeishuId,
    testerFeishuId,
    deployerName,
    publisherId
  }: ConfirmMessageContent
) => {
  if (messageType === 'deployed') {
    return {
      header: {
        title: {
          tag: 'plain_text',
          content: `上线完成—${name}`
        },
        template: 'green'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${developerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${developerFeishuId}></at>&#45;&#45;&#45;&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `上线人员: ${deployerName}`
          }
        },
        {
          tag: 'action',
          layout: 'flow',
          actions: [
            {
              tag: 'button',
              text: {
                tag: 'lark_md',
                content: '回滚'
              },
              type: 'primary',
              value: {
                action_type: 'button',
                publisher_data: {
                  publisher_id: publisherId,
                  status: 'rolledBack'
                }
              },
              confirm: {
                title: {
                  tag: 'plain_text',
                  content: '回滚'
                },
                text: {
                  tag: 'plain_text',
                  content: '确认操作?'
                }
              }
            }
          ]
        }
      ]
    }
  }
  if (messageType === 'rolledBack') {
    return {
      header: {
        title: {
          tag: 'plain_text',
          content: `上线已回滚—${name}`
        },
        template: 'grey'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${developerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;&#45;&#45;&#45;<at id=${developerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `操作人员: ${deployerName}`
          }
        }
      ]
    }
  }
}

export const createPassTestMessage = ({
  name,
  content,
  testerFeishuId,
  tag,
  developerName,
  testerName
}: PassTestMessageContent) => {
  return {
    config: {
      update_multi: true
    },
    header: {
      title: {
        tag: 'plain_text',
        content: `上线申请${name}`
      },
      template: 'grey'
    },
    elements: [
      {
        tag: 'div',
        fields: [
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `服务: **${name}**`
            }
          },
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `版本: **${tag}**`
            }
          },
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `开发: **${developerName}**`
            }
          },
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `测试: **${testerName}**`
            }
          }
        ]
      },
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: `上线内容: **${content}**`
        }
      },
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;`
        }
      },
      {
        tag: 'div',
        text: {
          tag: 'lark_md',
          content: '已发送上线申请'
        }
      }
    ]
  }
}

export const createMessage = (
  messageType: string,
  {
    name,
    content,
    testerFeishuId,
    publisherId,
    duty,
    tag,
    developerName,
    developerFeishuId,
    testerName,
    deployerName,
    deployerFeishuId
  }: MessageContent
) => {
  if (messageType === 'pending') {
    return {
      header: {
        title: {
          tag: 'plain_text',
          content: `上线申请${name}`
        },
        template: 'blue'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${developerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'action',
          layout: 'flow',
          actions: [
            {
              tag: 'button',
              type: 'primary',
              text: {
                tag: 'plain_text',
                content: '通知运维上线'
              },
              value: {
                action_type: 'button',
                publisher_data: {
                  publisher_id: publisherId,
                  status: 'passTest'
                }
              }
            }
          ]
        }
      ]
    }
  }
  if (messageType === 'passTest') {
    return {
      config: {
        update_multi: true
      },
      header: {
        title: {
          tag: 'plain_text',
          content: `上线通知—${name}`
        },
        template: 'indigo'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${developerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'action',
          layout: 'flow',
          actions: [
            {
              tag: 'button',
              text: {
                tag: 'lark_md',
                content: '自动上线'
              },
              type: 'primary',
              value: {
                action_type: 'button',
                publisher_data: {
                  publisher_id: publisherId,
                  status: 'deployed'
                }
              },
              confirm: {
                title: {
                  tag: 'plain_text',
                  content: '自动上线'
                },
                text: {
                  tag: 'plain_text',
                  content: '确认操作?'
                }
              }
            },
            {
              tag: 'button',
              text: {
                tag: 'lark_md',
                content: '上线完成'
              },
              type: 'primary',
              value: {
                action_type: 'button',
                publisher_data: {
                  publisher_id: publisherId,
                  status: 'deployed'
                }
              },
              confirm: {
                title: {
                  tag: 'plain_text',
                  content: '上线完成'
                },
                text: {
                  tag: 'plain_text',
                  content: '确认操作?'
                }
              }
            },
            {
              tag: 'button',
              text: {
                tag: 'lark_md',
                content: '废弃'
              },
              type: 'primary',
              value: {
                action_type: 'button',
                publisher_data: {
                  publisher_id: publisherId,
                  status: 'abandoned'
                }
              },
              confirm: {
                title: {
                  tag: 'plain_text',
                  content: '废弃'
                },
                text: {
                  tag: 'plain_text',
                  content: '确认操作?'
                }
              }
            }
          ]
        }
      ]
    }
  }
  if (messageType === 'deployed') {
    return {
      config: {
        update_multi: true
      },
      header: {
        title: {
          tag: 'plain_text',
          content: `(已完成)上线通知-${name}`
        },
        template: 'yellow'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${deployerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;&#45;&#45;&#45;<at id=${developerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `上线人员: ${deployerName}`
          }
        }
      ]
    }
  }
  if (messageType === 'abandoned') {
    return {
      config: {
        update_multi: true
      },
      header: {
        title: {
          tag: 'plain_text',
          content: `上线已废弃—${name}`
        },
        template: 'orange'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${developerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;&#45;&#45;&#45;<at id=${developerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `上线人员: ${deployerName}`
          }
        }
      ]
    }
  }
  if (messageType === 'rolledBack') {
    return {
      config: {
        update_multi: true
      },
      header: {
        title: {
          tag: 'plain_text',
          content: `（已完成）回滚通知—${name}`
        },
        template: 'yellow'
      },
      elements: [
        {
          tag: 'div',
          fields: [
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `服务: **${name}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `版本: **${tag}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `开发: **${developerName}**`
              }
            },
            {
              is_short: true,
              text: {
                tag: 'lark_md',
                content: `测试: **${testerName}**`
              }
            }
          ]
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `上线内容: **${content}**`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'lark_md',
            content: `&#45;&#45;&#45;<at id=${testerFeishuId}></at>&#45;&#45;&#45;&#45;&#45;&#45;<at id=${developerFeishuId}></at>&#45;&#45;&#45;`
          }
        },
        {
          tag: 'div',
          text: {
            tag: 'plain_text',
            content: `操作人员: ${deployerName}`
          }
        }
      ]
    }
  }
}
