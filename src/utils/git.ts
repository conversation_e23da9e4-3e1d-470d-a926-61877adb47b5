import { spawn } from 'child_process'
import { exception } from './exception'
import * as shell from 'shelljs'
import * as path from 'path'

const DOCKER_CONFIG = {
  CLIENT: {
    IMAGE_NAME: 'ycshadow', // 基础镜像名称
    IMAGE_TAG: '14.21.1' // 0.4tag--node:12.13(原253使用,废弃); tag 14.21.XX--node:14.21
  },
  CLIENT16: {
    IMAGE_NAME: 'docker.yc345.tv/7to12/ycshadow', // 基础镜像名称
    IMAGE_TAG: '16.15.2' // tag 16.15.XX --node:16.15
  },
  CLIENT18: {
    IMAGE_NAME: 'docker.yc345.tv/7to12/ycshadow', // 基础镜像名称
    IMAGE_TAG: '18.15.3' // tag 18.15.XX--node:18.15
  }
}

// 根据项目node版本匹配对应的Docker基础镜像
const getDockerBaseImage = (node) => {
  if (!node) {
    return DOCKER_CONFIG.CLIENT
  }
  let mainVersion = '14'
  const versionSplit = node.split('.')
  if (versionSplit.length > 0) {
    mainVersion = versionSplit[0]
  }
  if (mainVersion === '16') {
    return DOCKER_CONFIG.CLIENT16
  }
  if (mainVersion === '18') {
    return DOCKER_CONFIG.CLIENT18
  }
  return DOCKER_CONFIG.CLIENT
}

interface GitOperationResult {
  ok: boolean
  payload: string
}

interface RunShellResult {
  code: number
  stderr: string
  stdout: string
}
export async function gitClone(
  url: string,
  outPath: string
): Promise<GitOperationResult> {
  const args = ['clone', url, outPath]
  try {
    await runSpawn('git', args, {})
    return { ok: true, payload: '' }
  } catch (err) {
    return { ok: false, payload: err.message }
  }
}

export async function gitPull(
  url: string,
  outPath: string
): Promise<GitOperationResult> {
  const args = ['pull']
  try {
    await runSpawn('git', args, { cwd: outPath })
    return { ok: true, payload: '' }
  } catch (err) {
    return { ok: false, payload: err.message }
  }
}

export async function rmRepository(path: string): Promise<GitOperationResult> {
  try {
    await runSpawn('rm', ['-rf', path], {})
    return { ok: true, payload: '' }
  } catch (err) {
    return { ok: false, payload: err.message }
  }
}

export async function cdAndGitCheckout(path, address) {
  const projectPath = `${process.env.CLONE_PATH}${path}`
  let cmd = `git checkout .`
  // const cmd = `git checkout -- ${projectPath}`;
  let { code, stderr } = await runShell(cmd, path)
  if (
    stderr.indexOf('No such file or directory') !== -1 ||
    stderr.indexOf('没有那个文件或目录') !== -1
  ) {
    const cloneResult = await gitClone(address, projectPath)
    if (cloneResult.ok) {
      code = 0
    } else {
      cmd = 'git clone'
      stderr = 'git clone error'
    }
  }
  const status = 'init'
  if (code !== 0) {
    throw exception(status, `init error :${stderr}`, cmd)
  } else {
    return resJson(1, cmd, status)
  }
}

export async function runGitPull(path) {
  const { code, stderr } = await runShell(`git pull`, path)
  const status = 'pull'
  if (code !== 0) {
    throw exception(status, `pull error: ${stderr}`, 'git pull')
  } else {
    return resJson(1, 'git pull', status)
  }
}
export async function runGitDeleteBranch(branch, path) {
  const args = `git branch -D ${branch}`
  const { code, stderr } = await runShell(args, path)
  const status = 'delete'
  if (code !== 0) {
    throw exception(status, `delete error :${stderr}`, args)
  } else {
    return resJson(1, args, status)
  }
}
export async function runGitCheckout(branch, path) {
  const args = `git checkout -f ${branch}`
  const { code, stderr } = await runShell(args, path)
  const status = 'checkout'
  if (code !== 0) {
    throw exception(status, `checkout error :${stderr}`, args)
  } else {
    return resJson(1, args, status)
  }
}

export async function runShell(command, path?) {
  const projectPath = `${process.env.CLONE_PATH}${path}`

  if (path) {
    command = `cd ${projectPath} && ${command}`
  }
  const shellRes = await reunShellasync(command)
  return shellRes
}
//  docker镜像命令操作 run build ||  install
export async function runShellDocker(projectPath, commandList, nodeVersion) {
  const baseDockerConfig = getDockerBaseImage(nodeVersion)
  const { IMAGE_NAME, IMAGE_TAG } = baseDockerConfig
  console.log('IMAGE_TAG>>>>', IMAGE_TAG)
  projectPath = `${process.env.CLONE_PATH}${projectPath}`
  const projectName = path.basename(projectPath)
  const commandAGX = commandList.join(' ')
  const timestamp = new Date().getTime()
  const dockerName = `${projectName}${timestamp}`
  const command = `docker run -i -u 1000 -v ${projectPath}:/${projectName} --name ${dockerName} -w /${projectName} ${IMAGE_NAME}:${IMAGE_TAG} ${commandAGX}`
  // console.log('command>>>>', command)
  const shellRes = await reunShellasync(command)
  return { ...shellRes, dockerName }
}

function runSpawn(command, args, opts) {
  opts.stdio = 'ignore'
  const child = spawn(command, args, opts)
  return new Promise((resolve, reject) => {
    child.on('error', function (error) {
      reject(error)
    })
    child.on('close', function (code) {
      if (code !== 0) {
        reject(new Error('exit code: ' + code))
      }
      resolve(null)
    })
  })
}

function reunShellasync(command): Promise<RunShellResult> {
  return new Promise((resolve) => {
    shell.exec(
      command,
      { async: true, silent: false },
      (code, stdout, stderr) => {
        resolve({
          code,
          stdout,
          stderr
        })
      }
    )
  })
}

export const runShellGetDockerLog = (docker_name: string) => {
  const r = shell.exec(`docker container logs ${docker_name}`, {
    silent: false
  })
  // 将正常日志与错误日志结合起来，返回给前端展示
  return r.stdout + r.stderr
}

function resJson(code, payload, status) {
  return {
    code,
    payload,
    status
  }
}
