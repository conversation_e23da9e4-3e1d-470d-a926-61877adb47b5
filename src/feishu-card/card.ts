/**飞书卡片变量类型 */
export interface UpdateTemplateVars {
  publisherId: string
  name: string
  tag: string
  content: string
  developerFeishuId: string
  developerName: string
  testerFeishuId: string
  testerName: string
  duty?: string
  deployerName?: string
  deployerFeishuId?: string
  rollbackTag?: string
  belong?: string
  remark?: string
  sql?: string
  link?: string
  rollBackVersion?: {
    text: string
    value: string
  }[]
}

const white_list_footer = [
  {
    tag: 'hr'
  },
  {
    tag: 'note',
    elements: [
      {
        tag: 'img',
        img_key: 'img_v2_041b28e3-5680-48c2-9af2-497ace79333g',
        alt: {
          tag: 'plain_text',
          content: ''
        }
      },
      {
        tag: 'plain_text',
        content:
          '绿色通道项目不受上线时间窗口限制，可自行上线。符合的项目可联系运维人员配置绿色通道属性'
      }
    ]
  }
]

/**
 * 发起上线
 * @param param0
 * @returns
 */
export const deployment_start = (
  {
    publisherId,
    name,
    tag,
    content,
    developerName,
    testerFeishuId,
    testerName,
    remark = '无备注',
    sql = '无SQL',
    link
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线申请 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'violet',
        title: {
          content: `🔔 上线通知 ${name}`,
          tag: 'plain_text'
        }
      }
  const template: Record<string, any> = {
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'markdown',
        content: `上线内容：**${content}**`
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'markdown',
        content: `上线备注：**${remark || '无备注'}**`
      },
      {
        tag: 'markdown',
        content: `👉  <at id=${testerFeishuId}></at>  👈`
      },
      {
        tag: 'action',
        actions: [
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '自动上线'
            },
            type: 'primary',
            confirm: {
              title: {
                tag: 'plain_text',
                content: '自动上线'
              },
              text: {
                tag: 'plain_text',
                content: '确认操作?'
              }
            },
            value: {
              publisher_id: `${publisherId}`,
              status: 'deployed'
            }
          },
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '上线完成'
            },
            type: 'default',
            value: {
              publisher_id: `${publisherId}`,
              status: 'deployed_done'
            },
            confirm: {
              title: {
                tag: 'plain_text',
                content: '手动上线'
              },
              text: {
                tag: 'plain_text',
                content: '确认手动上线完成?'
              }
            }
          },
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '废弃'
            },
            type: 'danger',
            value: {
              publisher_id: `${publisherId}`,
              status: 'abandoned'
            },
            confirm: {
              title: {
                tag: 'plain_text',
                content: '废弃'
              },
              text: {
                tag: 'plain_text',
                content: '确认废弃本次上线吗?'
              }
            }
          }
        ]
      },
      {
        tag: 'action',
        actions: [
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '编辑'
            },
            type: 'default',
            multi_url: {
              url: `${link}`,
              pc_url: '',
              android_url: '',
              ios_url: ''
            }
          }
        ]
      }
    ],
    header,
    config: {
      update_multi: true
    }
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}

/**
 * 发起检测有相同tag
 * @param param0
 * @returns
 *
 */
export const deployment_repeat_tag = (
  {
    publisherId,
    name,
    tag,
    content,
    developerName,
    testerFeishuId,
    testerName,
    remark = '无备注',
    sql = '无SQL',
    link
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'violet',
        title: {
          content: `🟢(绿色通道)上线申请🔔检测到 ${name}上线版本异常【存在更高或者相同的上线Tag版本】请再次确认是否要上线?`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'violet',
        title: {
          content: `🔔检测到 ${name}上线版本异常【存在更高或者相同的上线Tag版本】请再次确认是否要上线?`,
          tag: 'plain_text'
        }
      }
  const template: Record<string, any> = {
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'markdown',
        content: `上线内容：**${content}**`
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'markdown',
        content: `上线备注：**${remark || '无备注'}**`
      },
      {
        tag: 'markdown',
        content: `👉  <at id=${testerFeishuId}></at>  👈`
      },
      {
        tag: 'action',
        actions: [
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '自动上线'
            },
            type: 'primary',
            confirm: {
              title: {
                tag: 'plain_text',
                content: '检测到当前项目上线版本低于【线上版本/待上线版本】'
              },
              text: {
                tag: 'plain_text',
                content: `请联系开发：${developerName}；确认当前项目的上线Tag版本；如果确认要上线当前Tag版本：${tag}，请点击下方“确定”按钮继续上线。`
              }
            },
            value: {
              publisher_id: `${publisherId}`,
              status: 'repeat_check_deployed'
            }
          },
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '上线完成'
            },
            type: 'default',
            value: {
              publisher_id: `${publisherId}`,
              status: 'deployed_done'
            },
            confirm: {
              title: {
                tag: 'plain_text',
                content: '手动上线'
              },
              text: {
                tag: 'plain_text',
                content: '确认手动上线完成?'
              }
            }
          },
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '废弃'
            },
            type: 'danger',
            value: {
              publisher_id: `${publisherId}`,
              status: 'abandoned'
            },
            confirm: {
              title: {
                tag: 'plain_text',
                content: '废弃'
              },
              text: {
                tag: 'plain_text',
                content: '确认废弃本次上线吗?'
              }
            }
          }
        ]
      },
      {
        tag: 'action',
        actions: [
          {
            tag: 'button',
            text: {
              tag: 'plain_text',
              content: '编辑'
            },
            type: 'default',
            multi_url: {
              url: `${link}`,
              pc_url: '',
              android_url: '',
              ios_url: ''
            }
          }
        ]
      }
    ],
    header,
    config: {
      update_multi: true
    }
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}

/**
 * 上线完成
 * @param param0
 * @returns
 */
export const deploy_done = (
  {
    name,
    tag,
    content,
    developerFeishuId,
    developerName,
    testerFeishuId,
    testerName,
    deployerName,
    belong,
    remark = '无备注',
    sql = '无SQL'
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线通知 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'green',
        title: {
          content: `上线通知 ${name}`,
          tag: 'plain_text'
        }
      }

  const template: Record<string, any> = {
    config: {
      update_multi: true
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'markdown',
        content: `上线内容：**${content}**`
      },
      {
        tag: 'markdown',
        content: `上线备注：**${remark}**`
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'markdown',
        content: `👉  <at id=${testerFeishuId}></at>  <at id=${developerFeishuId}></at>  👈`
      },
      {
        tag: 'markdown',
        content: `上线人员：**${deployerName}** \n业务归属：**${belong}**`
      }
    ],
    header
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}

/**
 * 上线废弃
 */

export const deploy_abandoned = (
  {
    name,
    tag,
    content,
    developerName,
    testerFeishuId,
    testerName,
    remark = '无备注',
    sql = '无SQL'
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线已废弃 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'carmine',
        title: {
          content: `✔️ 上线已废弃 ${name}`,
          tag: 'plain_text'
        }
      }
  const template: Record<string, any> = {
    config: {
      update_multi: true
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'markdown',
        content: `上线内容：**${content}**`
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'markdown',
        content: `上线备注：**${remark}**`
      },
      {
        tag: 'markdown',
        content: `👉  <at id=${testerFeishuId}></at>  👈`
      },
      {
        tag: 'markdown',
        content: `操作人员：**${developerName}**`
      }
    ],
    header
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}

/**
 * 上线已成功
 */
export const deploy_success = (
  {
    publisherId,
    name,
    tag,
    content,
    developerFeishuId,
    developerName,
    testerFeishuId,
    testerName,
    rollBackVersion,
    sql = '无SQL'
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线已完成 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'green',
        title: {
          content: `✅ 上线已完成 ${name}`,
          tag: 'plain_text'
        }
      }

  const template: Record<string, any> = {
    config: {
      update_multi: true
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `上线内容：**${content}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `上线人员：**${developerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `👉  <at id=${testerFeishuId}></at>  <at id=${developerFeishuId}></at> 👈`
              }
            ]
          }
        ]
      },
      rollBackVersion && rollBackVersion.length
        ? {
            tag: 'div',
            text: {
              tag: 'lark_md',
              content: '👉 **回滚版本选择**'
            },
            extra: {
              tag: 'select_static',
              placeholder: {
                tag: 'plain_text',
                content: '选择回滚版本'
              },
              options: rollBackVersion.map((version) => {
                return {
                  text: {
                    tag: 'plain_text',
                    content: version.text
                  },
                  value: version.value
                }
              }),
              confirm: {
                title: {
                  tag: 'plain_text',
                  content: '确认回滚到该版本吗？'
                },
                text: {
                  tag: 'plain_text',
                  content: '确认操作？'
                }
              },
              value: {
                publisher_id: `${publisherId}`,
                status: 'rolledBack'
              }
            }
          }
        : {
            tag: 'div',
            text: {
              tag: 'lark_md',
              content: '👉 **未获取到历史Tag版本**'
            }
          }
    ],
    header
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}

/**
 * 上线完成 - 大群通知
 */

export const deploy_success_group = (
  {
    name,
    tag,
    content,
    developerFeishuId,
    developerName,
    testerFeishuId,
    testerName,
    deployerName,
    belong,
    remark = '无备注',
    sql = '无SQL'
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线已完成 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'green',
        title: {
          content: `✅ 上线已完成 ${name}`,
          tag: 'plain_text'
        }
      }
  const template: Record<string, any> = {
    config: {
      update_multi: true
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'markdown',
        content: `上线内容：**${content}**`
      },
      {
        tag: 'markdown',
        content: `上线备注：**${remark}**`
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL: ${sql || '无SQL'}`,
          tag: 'lark_md'
        }
      },
      {
        tag: 'markdown',
        content: `👉  <at id=${testerFeishuId}></at>  <at id=${developerFeishuId}></at>  👈`
      },
      {
        tag: 'markdown',
        content: `上线人员：**${deployerName}** \n业务归属：**${belong}**`
      }
    ],
    header
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}

/**
 * 上线回滚
 */

export const deploy_rollback = (
  {
    name,
    tag,
    content,
    developerName,
    testerFeishuId,
    testerName,
    rollbackTag,
    sql = '无SQL'
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线回滚 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'grey',
        title: {
          content: `↪️ 上线回滚 ${name}`,
          tag: 'plain_text'
        }
      }
  const template: Record<string, any> = {
    config: {
      update_multi: true
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'div',
        fields: [
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `回滚版本：**${rollbackTag}**`
            }
          },
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `操作人：**${developerName}**`
            }
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'div',
        fields: [
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `上线内容：**${content}**`
            }
          }
        ]
      },
      {
        tag: 'markdown',
        content: `--------   <at id=${testerFeishuId}></at>   --------`
      }
    ],
    header
  }

  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }

  return template
}

/**
 * 上线回滚完成
 */

export const deploy_rollback_success = (
  {
    name,
    tag,
    content,
    developerFeishuId,
    developerName,
    testerFeishuId,
    testerName,
    deployerName,
    rollbackTag,
    belong,
    remark = '无备注',
    sql = '无SQL'
  }: UpdateTemplateVars,
  is_white_list = false
) => {
  const header = is_white_list
    ? {
        template: 'purple',
        title: {
          content: `🟢(绿色通道)上线回滚已完成 ${name}`,
          tag: 'plain_text'
        }
      }
    : {
        template: 'indigo',
        title: {
          content: `✅ 上线回滚已完成 ${name}`,
          tag: 'plain_text'
        }
      }
  const template: Record<string, any> = {
    config: {
      update_multi: true
    },
    elements: [
      {
        tag: 'column_set',
        flex_mode: 'stretch',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'auto',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `服务：**${name}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `版本：**${tag}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'div',
        fields: [
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `回滚版本：**${rollbackTag}**`
            }
          },
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `操作人：**${developerName}**`
            }
          }
        ]
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `开发：**${developerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `测试：**${testerName}**`
              }
            ]
          }
        ]
      },
      {
        tag: 'div',
        fields: [
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `上线内容：**${content}**`
            }
          },
          {
            is_short: true,
            text: {
              tag: 'lark_md',
              content: `上线备注：**${remark}**`
            }
          }
        ]
      },
      {
        tag: 'div',
        text: {
          content: `上线SQL：${sql || '无SQL'}`,
          tag: 'plain_text'
        }
      },
      {
        tag: 'markdown',
        content: `--------   <at id=${testerFeishuId}></at>   <at id=${developerFeishuId}></at>--------`
      },
      {
        tag: 'column_set',
        flex_mode: 'none',
        background_style: 'default',
        columns: [
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `回滚人: **${deployerName}**`
              }
            ]
          },
          {
            tag: 'column',
            width: 'weighted',
            weight: 1,
            vertical_align: 'top',
            elements: [
              {
                tag: 'markdown',
                content: `业务归属：**${belong}**`
              }
            ]
          }
        ]
      }
    ],
    header
  }
  if (is_white_list) {
    template.elements.push(...white_list_footer)
  }
  return template
}
