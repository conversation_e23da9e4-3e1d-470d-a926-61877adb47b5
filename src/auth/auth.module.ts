import { Module, forwardRef } from '@nestjs/common'
import { AuthService } from './auth.service'
import { PassportModule } from '@nestjs/passport'
import { JwtModule } from '@nestjs/jwt'
import { JwtStrategy } from './jwt-strategy'
import { UserModule } from 'src/user/user.module'
import { FeishuApiService } from 'src/feishu-api/fetshu-service'

@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: 'JarvisSecret',
      signOptions: {
        expiresIn: '24h'
      }
    }),
    forwardRef(() => UserModule)
  ],
  providers: [AuthService, JwtStrategy, FeishuApiService],
  exports: [AuthService]
})
export class AuthModule {}
