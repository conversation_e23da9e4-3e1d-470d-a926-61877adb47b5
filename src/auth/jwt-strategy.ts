import { Injectable } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { ExtractJwt, Strategy } from 'passport-jwt'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: '<PERSON><PERSON><PERSON><PERSON>'
    })
  }
  async validate(payload: any) {
    return {
      id: payload.id,
      feishu_id: payload.feishu_id,
      name: payload.name
    }
  }
}
