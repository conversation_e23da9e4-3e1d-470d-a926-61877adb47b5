import { Injectable } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { UserService } from 'src/user/user.service'
import { FeishuApiService } from 'src/feishu-api/fetshu-service'

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private readonly userService: UserService,
    private readonly feishuApiService: FeishuApiService
  ) {}
  async login(code: string) {
    const user = await this.userLoginHandler(code)
    if (user) {
      const token = this.jwtService.sign({
        id: user.id,
        feishu_id: user.feishu_id,
        name: user.name
      })
      return {
        token: `Bearer ${token}`,
        name: user.name,
        avatar_url: user.avatar_url
      }
    }
  }
  async userLoginHandler(code: string) {
    const res = await this.feishuApiService.getFeishuUserInfoFromCode(code)
    if (res) {
      const { open_id, name, avatar_url } = res
      let user = await this.userService.findUserByFeishuId(open_id)
      if (!user) {
        user = await this.userService.createNewUser(name, open_id, avatar_url)
      }
      return user
    }
  }
}
