import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ConfigService, ConfigModule } from '@nestjs/config'
import { ProjectModule } from './project/project.module'
import { ToolModule } from './tool/tool.module'
import { CommonConfigModule } from './common/common.module'
import { LoggerModule } from './logger/logger.module'
import { UserModule } from './user/user.module'
import { AuthModule } from './auth/auth.module'
import { APP_GUARD } from '@nestjs/core'
import { JwtAuthGuard } from './auth/jwt-auth.guard'
import { NoticeModule } from './notice/notice.module'
import { PublisherModule } from './publisher/publisher.module'
import { ProcessModule } from './process/process.module'
import { DocumentsModule } from './documents/documents.module'
import { ScheduleModule } from '@nestjs/schedule'
import { WinstonModule } from 'nest-winston'
import { SonicModule } from './sonic/sonic.module'
import { DockerModule } from './docker/docker.module'
import { PipelineModule } from './pipeline/pipeline.module'
import * as winston from 'winston'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { GitlabModule } from './gitlab/gitlab.module'

@Module({
  imports: [
    WinstonModule.forRoot({
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp({
              format: 'YYYY-MM-DD HH:mm:ss'
            }),
            winston.format.ms()
          )
        }),
        new winston.transports.File({
          filename: 'logs/javrias.log',
          level: 'info',
          format: winston.format.combine(
            winston.format.timestamp({ format: 'YYYY年-MM月-DD日 HH:mm:ss' }),
            winston.format.align(),
            winston.format.printf(
              (info) => `${[info.timestamp]}: ${info.message}`
            )
          )
        })
      ]
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', `.env.${process.env.NODE_ENV}`]
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config) => {
        return {
          type: 'postgres',
          host: config.get('DB_HOST'),
          port: parseInt(config.get('DB_PORT'), 10),
          username: config.get('DB_USERNAME'),
          password: config.get('DB_PASSWORD'),
          database: config.get('DB_NAME'),
          autoLoadEntities: true,
          synchronize: process.env.NODE_ENV === 'production' ? false : true,
          logger: 'advanced-console',
          logging: ['error']
        }
      }
    }),
    ProjectModule,
    CommonConfigModule,
    LoggerModule,
    UserModule,
    AuthModule,
    NoticeModule,
    PublisherModule,
    ProcessModule,
    DocumentsModule,
    ToolModule,
    ScheduleModule.forRoot(),
    SonicModule,
    DockerModule,
    PipelineModule,
    EventEmitterModule.forRoot({
      maxListeners: 50
    }),
    GitlabModule
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard
    }
  ]
})
export class AppModule {}
