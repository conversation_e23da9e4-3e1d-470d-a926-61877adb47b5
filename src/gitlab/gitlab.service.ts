import { Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios from 'axios'
import { orderBy } from 'lodash'

type GitlabTags = {
  name: string
  message: string
  protected: boolean
  commit: {
    created_at: string
  }
}

@Injectable()
export class GitlabService {
  constructor(@Inject(ConfigService) private readonly config: ConfigService) {}
  async getGitlabBuildTag(gitlab_id: string, branch: string) {
    try {
      const res = await axios.get<GitlabTags[]>(
        `https://gitlab.yc345.tv/api/v4/projects/${gitlab_id}/repository/tags?per_page=100`,
        {
          headers: {
            Authorization: `Bearer ${this.config.get('gitLabAccessToken')}`
          },
          params: {
            per_page: 10,
            search: `jarvis_${branch}$`
          }
        }
      )
      if (res.status === 200 && res.data) {
        const arr = res.data.filter((el) => {
          const version_value = el.name.match(/v(\d+.\d+.\d+)/)?.[1]
          const value = version_value?.split('.')[0] || '0'
          return parseInt(value) < 1000 && el.name.includes('_jarvis_')
        })
        const sort_arr = orderBy(
          arr,
          [
            (el) => {
              const version_value = el.name.match(/v(\d+.\d+.\d+)/)?.[1]
              const value = version_value?.split('.')[0] || '0'
              return parseInt(value)
            },
            (el) => {
              const version_value = el.name.match(/v(\d+.\d+.\d+)/)?.[1]
              const value = version_value?.split('.')[1] || '0'
              return parseInt(value)
            },
            (el) => {
              const version_value = el.name.match(/v(\d+.\d+.\d+)/)?.[1]
              const value = version_value?.split('.')[2] || '0'
              return parseInt(value)
            }
          ],
          ['desc', 'desc', 'desc']
        )
        return sort_arr?.[0].name
      }
    } catch (e) {
      return ''
    }
  }
}
