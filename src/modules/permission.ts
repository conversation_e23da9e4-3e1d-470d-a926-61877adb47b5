import type { UserModule } from '~/types'

export const install: UserModule = ({ isClient, router }) => {
  if (isClient) {
    router.beforeEach(async (to, from, next) => {
      const userStore = useUserStore()

      if (userStore.token) {
        if (to.path === '/login') {
          next({ path: '/' })
        }
        else {
          if (!userStore.userInfo.id) {
            userStore.getUserInfo()
          }
          // 处理 /dashboard 重定向到首页
          if (to.path === '/dashboard') {
            next({ path: '/', replace: true })
          }
          next()
        }
      }
      else {
        if (to.path === '/login') {
          next()
        }
        else {
          next(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
        }
      }
    })
  }
}
