import { ConfigService } from '@nestjs/config'
import { Injectable } from '@nestjs/common'
import Docker from 'dockerode'

const docker = new Docker()

@Injectable()
export class DockerService {
  containers: Map<string, Docker.Container>
  constructor(private readonly configService: ConfigService) {
    this.containers = new Map()
  }
  async getContainer(container_id: string) {
    if (this.containers.get(container_id)) {
      return this.containers.get(container_id)
    } else {
      const container = docker.getContainer(container_id)
      try {
        await container.stats()
        this.containers.set(container_id, container)
        return container
      } catch (e) {
        return
      }
    }
  }
  async createNewPipeContainer(image: string) {
    const container = await docker.createContainer({
      Image: image,
      Tty: true,
      AttachStderr: true,
      AttachStdout: true,
      User: 'root',
      HostConfig: {
        Binds: [
          `${this.configService.get<string>('SSH_PATH')!}:/root/.ssh`,
          `${this.configService.get<string>(
            'DOCKER_SOCK_PATH'
          )!}:/var/run/docker.sock`,
          `${this.configService.get<string>('LOG_PATH')!}:/app/log`,
          '/home/<USER>/.docker:/root/.docker'
        ]
      }
    })
    await container.start()
    this.containers.set(container.id, container)
    return container
  }
  async getExecResult(exec_id: string) {
    const exec = docker.getExec(exec_id)
    try {
      const result = await exec.inspect()
      return result
    } catch (e) {
      return
    }
  }

  async removeContainer(container_id: string) {
    const container = await this.getContainer(container_id)
    if (container) {
      try {
        await container.stop()
        await container.remove()
      } catch (e) {}
    }
  }

  async waitForStream(stream: NodeJS.ReadableStream) {
    const process_res: any[] = []
    stream.on('data', (chunk) => {
      try {
        process_res.push(JSON.parse(chunk.toString('utf-8') as string))
      } catch (e) {}
    })
    return new Promise<any[]>((reslove, reject) => {
      docker.modem.followProgress(stream, (err) => {
        if (err) {
          reject(err)
        } else {
          reslove(process_res)
        }
      })
    })
  }
  async pushImage(image_name: string) {
    const image = docker.getImage(image_name)
    try {
      const stream = await image.push({
        authconfig: {
          serveraddress: 'docker.yc345.tv',
          username: 'robot',
          password: 'W1u9cVom@owj'
        }
      })
      const res = await this.waitForStream(stream)
      const push_error = res.find((el) => el.error)
      if (push_error) {
        return push_error.error as string
      }
      // 镜像推送成功后添加自动移除镜像逻辑
      try {
        await image.remove()
      } catch (err) {
        console.log('err', err)
      }
      return true
    } catch (e) {
      console.log('e', e)
      return 'docker push失败'
    }
  }
}
