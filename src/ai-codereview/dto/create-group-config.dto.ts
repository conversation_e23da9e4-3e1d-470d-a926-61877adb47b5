import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsEnum, IsArray, IsOptional, IsBoolean, IsNumber } from 'class-validator'

export class CreateAiCodeReviewGroupConfigDto {
  @ApiProperty({
    description: '配置名称'
  })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({
    description: '配置类型',
    enum: ['normal', 'project']
  })
  @IsEnum(['normal', 'project'])
  type: 'normal' | 'project'

  @ApiProperty({
    description: '飞书机器人webhook地址'
  })
  @IsString()
  @IsNotEmpty()
  feishu_bot_webhook: string

  @ApiProperty({
    description: '关联用户ID列表（普通群使用）',
    type: [String],
    required: false
  })
  @IsArray()
  @IsOptional()
  user_ids?: string[]

  @ApiProperty({
    description: '关联项目ID列表（项目专用群使用）',
    type: [String],
    required: false
  })
  @IsArray()
  @IsOptional()
  project_ids?: string[]

  @ApiProperty({
    description: '优先级',
    required: false,
    default: 1
  })
  @IsNumber()
  @IsOptional()
  priority?: number

  @ApiProperty({
    description: '是否启用',
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean
}
