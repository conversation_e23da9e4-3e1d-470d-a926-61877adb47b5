import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString, IsEnum, IsNumberString } from 'class-validator'

export class FindGroupConfigDto {
  @ApiProperty({
    description: '页码',
    required: false,
    default: 1
  })
  @IsNumberString()
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    description: '每页数量',
    required: false,
    default: 20
  })
  @IsNumberString()
  @IsOptional()
  pageSize?: number = 20

  @ApiProperty({
    description: '配置名称（模糊搜索）',
    required: false
  })
  @IsString()
  @IsOptional()
  name?: string

  @ApiProperty({
    description: '配置类型',
    enum: ['normal', 'project'],
    required: false
  })
  @IsEnum(['normal', 'project'])
  @IsOptional()
  type?: 'normal' | 'project'
}
