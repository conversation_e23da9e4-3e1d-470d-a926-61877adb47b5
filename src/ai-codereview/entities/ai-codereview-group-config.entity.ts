import {
  Column,
  CreateDateColumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm'

@Entity()
export class AiCodeReviewGroupConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'varchar',
    length: 100
  })
  name: string

  @Column({
    type: 'enum',
    enum: ['normal', 'project'],
    default: 'normal'
  })
  type: 'normal' | 'project'

  @Column({
    type: 'varchar',
    length: 500
  })
  feishu_bot_webhook: string

  @Column({
    type: 'simple-array',
    nullable: true
  })
  user_ids: string[]

  @Column({
    type: 'simple-array',
    nullable: true
  })
  project_ids: string[]

  @Column({
    type: 'int',
    default: 1
  })
  priority: number

  @Column({
    type: 'boolean',
    default: true
  })
  is_active: boolean

  @Column({
    type: 'varchar',
    default: ''
  })
  creator: string

  @Column({
    type: 'varchar',
    default: ''
  })
  update_user: string

  @CreateDateColumn()
  create_time: Date

  @UpdateDateColumn()
  update_time: Date
}
