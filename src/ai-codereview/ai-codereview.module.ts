import { Modu<PERSON> } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AiCodeReviewController } from './ai-codereview.controller'
import { AiCodeReviewService } from './ai-codereview.service'
import { AiCodeReviewGroupConfig } from './entities/ai-codereview-group-config.entity'
import { AdminUser } from '../user/entities/user.entity'
import { LoggerModule } from '../logger/logger.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([AiCodeReviewGroupConfig, AdminUser]),
    LoggerModule
  ],
  controllers: [AiCodeReviewController],
  providers: [AiCodeReviewService],
  exports: [AiCodeReviewService]
})
export class AiCodeReviewModule {}
