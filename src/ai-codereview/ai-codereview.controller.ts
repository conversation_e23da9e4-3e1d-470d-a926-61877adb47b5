import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Request,
  Headers
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger'
import { AiCodeReviewService } from './ai-codereview.service'
import { CreateAiCodeReviewGroupConfigDto } from './dto/create-group-config.dto'
import { UpdateAiCodeReviewGroupConfigDto } from './dto/update-group-config.dto'
import { FindGroupConfigDto } from './dto/find-group-config.dto'
import { Public } from '../auth/jwt-meta'

@ApiTags('AI CodeReview')
@Controller('ai-codereview')
export class AiCodeReviewController {
  constructor(private readonly aiCodeReviewService: AiCodeReviewService) {}

  @ApiOperation({
    summary: '创建AI CodeReview群配置'
  })
  @Post('/groups')
  async createGroupConfig(
    @Request() req: any,
    @Body() dto: CreateAiCodeReviewGroupConfigDto
  ) {
    if (req.user.id) {
      return this.aiCodeReviewService.createGroupConfig(dto, req.user.id)
    }
  }

  @ApiOperation({
    summary: '获取AI CodeReview群配置列表'
  })
  @Get('/groups')
  async getGroupConfigs(@Query() query: FindGroupConfigDto) {
    return this.aiCodeReviewService.findGroupConfigs(query)
  }

  @ApiOperation({
    summary: '根据ID获取AI CodeReview群配置详情'
  })
  @ApiParam({ name: 'id', description: '群配置ID' })
  @Get('/groups/:id')
  async getGroupConfigById(@Param('id') id: string) {
    return this.aiCodeReviewService.findGroupConfigById(id)
  }

  @ApiOperation({
    summary: '更新AI CodeReview群配置'
  })
  @ApiParam({ name: 'id', description: '群配置ID' })
  @Put('/groups/:id')
  async updateGroupConfig(
    @Param('id') id: string,
    @Request() req: any,
    @Body() dto: UpdateAiCodeReviewGroupConfigDto
  ) {
    if (req.user.id) {
      return this.aiCodeReviewService.updateGroupConfig(id, dto, req.user.id)
    }
  }

  @ApiOperation({
    summary: '删除AI CodeReview群配置'
  })
  @ApiParam({ name: 'id', description: '群配置ID' })
  @Delete('/groups/:id')
  async deleteGroupConfig(@Param('id') id: string, @Request() req: any) {
    if (req.user.id) {
      await this.aiCodeReviewService.deleteGroupConfig(id, req.user.id)
      return { message: '删除成功' }
    }
  }

  @ApiOperation({
    summary: '获取用户的CodeReview群配置（供AI-CodeReview服务调用）'
  })
  @ApiParam({ name: 'gitlabUsername', description: 'GitLab用户名' })
  @ApiQuery({ name: 'projectId', description: '项目ID', required: false })
  @ApiQuery({ name: 'authorEmail', description: '作者邮箱', required: false })
  @Public()
  @Get('/user-groups/:gitlabUsername')
  async getUserGroups(
    @Param('gitlabUsername') gitlabUsername: string,
    @Query('projectId') projectId?: string,
    @Query('authorEmail') authorEmail?: string,
    @Headers('x-internal-service') internalService?: string
  ) {
    // 验证内部服务调用
    if (internalService !== 'ai-codereview') {
      return { error: 'Unauthorized internal service call' }
    }

    return this.aiCodeReviewService.getUserCodeReviewGroups(
      gitlabUsername,
      projectId,
      authorEmail
    )
  }

  @ApiOperation({
    summary: '获取所有启用的群配置'
  })
  @Get('/groups/active/all')
  async getAllActiveGroupConfigs() {
    return this.aiCodeReviewService.findAllActiveGroupConfigs()
  }
}
