import { Injectable, Inject } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, Like, FindOptionsWhere } from 'typeorm'
import { AiCodeReviewGroupConfig } from './entities/ai-codereview-group-config.entity'
import { AdminUser } from '../user/entities/user.entity'
import { CreateAiCodeReviewGroupConfigDto } from './dto/create-group-config.dto'
import { UpdateAiCodeReviewGroupConfigDto } from './dto/update-group-config.dto'
import { FindGroupConfigDto } from './dto/find-group-config.dto'
import { LoggerService } from '../logger/logger.service'

@Injectable()
export class AiCodeReviewService {
  constructor(
    @InjectRepository(AiCodeReviewGroupConfig)
    private readonly groupConfigRepository: Repository<AiCodeReviewGroupConfig>,
    @InjectRepository(AdminUser)
    private readonly userRepository: Repository<AdminUser>,
    private readonly logger: LoggerService
  ) {}

  /**
   * 创建群配置
   */
  async createGroupConfig(dto: CreateAiCodeReviewGroupConfigDto, userId: string) {
    const config = this.groupConfigRepository.create({
      ...dto,
      creator: userId,
      update_user: userId
    })
    
    const result = await this.groupConfigRepository.save(config)
    this.logger.append(result.id, '创建AI CodeReview群配置', '创建群配置', userId)
    return result
  }

  /**
   * 更新群配置
   */
  async updateGroupConfig(id: string, dto: UpdateAiCodeReviewGroupConfigDto, userId: string) {
    await this.groupConfigRepository.update(id, {
      ...dto,
      update_user: userId
    })
    
    this.logger.append(id, '更新AI CodeReview群配置', '更新群配置', userId)
    return this.findGroupConfigById(id)
  }

  /**
   * 删除群配置
   */
  async deleteGroupConfig(id: string, userId: string) {
    await this.groupConfigRepository.delete(id)
    this.logger.append(id, '删除AI CodeReview群配置', '删除群配置', userId)
  }

  /**
   * 根据ID查找群配置
   */
  async findGroupConfigById(id: string) {
    return this.groupConfigRepository.findOne({
      where: { id }
    })
  }

  /**
   * 分页查询群配置
   */
  async findGroupConfigs(query: FindGroupConfigDto) {
    const findQuery: FindOptionsWhere<AiCodeReviewGroupConfig> = {}
    
    if (query.name) {
      findQuery.name = Like(`%${query.name}%`)
    }
    
    if (query.type) {
      findQuery.type = query.type
    }

    const [list, total] = await this.groupConfigRepository.findAndCount({
      where: findQuery,
      take: query.pageSize,
      skip: (query.page - 1) * query.pageSize,
      order: { priority: 'DESC', create_time: 'DESC' }
    })

    return {
      page: query.page,
      pageSize: query.pageSize,
      list,
      total
    }
  }

  /**
   * 获取所有启用的群配置
   */
  async findAllActiveGroupConfigs() {
    return this.groupConfigRepository.find({
      where: { is_active: true },
      order: { priority: 'DESC', create_time: 'DESC' }
    })
  }

  /**
   * 根据GitLab用户名获取用户的CodeReview群配置
   */
  async getUserCodeReviewGroups(gitlabUsername: string, projectId?: string, authorEmail?: string) {
    let user = null

    // 1. 优先通过gitlab_username查找用户
    if (gitlabUsername) {
      user = await this.userRepository.findOne({
        where: { gitlab_username: gitlabUsername }
      })
    }

    // 2. 如果通过gitlab_username没找到，尝试通过邮箱查找
    if (!user && authorEmail) {
      user = await this.userRepository.findOne({
        where: { email: authorEmail }
      })
    }

    if (!user) {
      return { groups: [], mentionUsers: [], fallbackToDefault: true }
    }

    // 3. 查找项目专用群（优先级高）
    if (projectId) {
      const projectGroups = await this.groupConfigRepository.find({
        where: {
          type: 'project',
          is_active: true
        },
        order: { priority: 'DESC' }
      })

      // 过滤包含指定项目ID的群配置
      const matchedProjectGroups = projectGroups.filter(group =>
        group.project_ids && group.project_ids.includes(projectId)
      )

      if (matchedProjectGroups.length > 0) {
        return {
          groups: matchedProjectGroups,
          mentionUsers: [user.feishu_id],
          fallbackToDefault: false
        }
      }
    }

    // 4. 查找普通群
    const normalGroups = await this.groupConfigRepository.find({
      where: {
        type: 'normal',
        is_active: true
      },
      order: { priority: 'DESC' }
    })

    // 过滤包含指定用户ID的群配置
    const matchedNormalGroups = normalGroups.filter(group =>
      group.user_ids && group.user_ids.includes(user.id)
    )

    return {
      groups: matchedNormalGroups,
      mentionUsers: [user.feishu_id],
      fallbackToDefault: matchedNormalGroups.length === 0
    }
  }
}
