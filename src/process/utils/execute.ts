import { runShell, runShellDocker } from '../../utils/git'
import { exception } from '../../utils/exception'
import { curry } from 'lodash/fp'
import { isEmpty, compactArray } from './check'
export async function resJson(code, msg, data) {
  const res = {
    status: {
      code: code,
      msg
    },
    data
  }
  await runShell(
    `curl -X POST 'https://www.feishu.cn/flow/api/trigger-webhook/17cafeec652056accb9190345095d338' -H 'cache-control: no-cache' -H 'content-type: application/json' -d '${JSON.stringify(
      res
    )}'`
  )
}

export const branchEnv = (branch) => {
  let env
  const br = branch || 'test'
  let isProd = false
  switch (br) {
    case 'master':
      env = 'master'
      isProd = true
      break
    case 'release':
      env = 'release'
      isProd = true
      break
    default:
      env = 'develop'
  }
  return { env, isProd }
}

// 创建tag
export const runCreateTag = async (branch, type, project) => {
  const tagName = await runVersion(type, branch, project)
  const status = 'createTag'
  const { code } = await runShell(`git tag ${tagName}`, project)
  return { code: code, status, tagName }
}

export const runGitTagName = async (tagName, project) => {
  const { code } = await runShell(`git tag ${tagName}`, project)
  return code
}

// 构建
export const runBuild = async (branch, command, project, nodeVersion) => {
  if (isEmpty(command) === true) {
    return { code: 1, status: 'build', payload: command }
  }
  const cuttingCmd = compactArray(command)
  const { code, stderr, dockerName } = await runShellDocker(
    project,
    cuttingCmd,
    nodeVersion
  )
  const status = 'build'
  if (code !== 0) {
    throw exception(status, `build error :${stderr}`, command, dockerName)
  } else {
    return { code: 1, status: 'build', payload: command, dockerName }
  }
}

// docker PUSH
const curryDocker = async (executeBody, tagName, statusCmd) => {
  const { project, prejectDetail, branch } = executeBody
  const { config } = prejectDetail
  const dockerPath = config.name_space || '7to12'

  let cmd
  if (statusCmd === 'build') {
    cmd = `docker build . -t docker.yc345.tv/${dockerPath}/${project}:${tagName}`
  }
  if (statusCmd === 'push') {
    cmd = `docker push docker.yc345.tv/${dockerPath}/${project}:${tagName}`
  }
  if (statusCmd === 'switchContext') {
    cmd = `kubectl config use-context ${
      branch === 'release' ? 'stagenv' : 'testenv'
    }`
  }
  if (statusCmd === 'replaceDocer') {
    cmd = `kubectl -n ${dockerPath} set image deployment ${project} ${project}=docker.yc345.tv/${dockerPath}/${project}:${tagName} --record=true`
  }
  if (statusCmd === 'dockerStatus') {
    cmd = `kubectl -n ${dockerPath} rollout status deployment ${project} --timeout=5m`
  }
  const { code, stderr, stdout } = await runShell(cmd, project)
  return {
    code,
    stderr,
    cmd,
    stdout
  }
}

const runDocker = curry(curryDocker)

export const runDockerBuild = async (executeBody, tagName) => {
  const dockerCmd = runDocker(executeBody)(tagName)
  const { code, stderr, cmd } = await dockerCmd('build')
  const status = 'dockerBuild'
  if (code !== 0) {
    throw exception(status, `dockerBuild error:${stderr}`, cmd)
  } else {
    return { code: 1, status: status, payload: cmd }
  }
}

export const runDockerPush = async (executeBody, tagName) => {
  const dockerCmd = runDocker(executeBody)(tagName)
  const { code, stderr, cmd } = await dockerCmd('push')
  const status = 'dockerPush'
  if (code !== 0) {
    throw exception(status, `dockerPush error :${stderr}`, cmd)
  } else {
    return { code: 1, status: status, payload: cmd }
  }
}

// 替换docker
// 监听错误场景-超时场景
// Waiting for deployment "onion-flow" rollout to finish: 1 old replicas are pending termination...
// error: timed out waiting for the condition

// 成功场景 deployment "onion-flow" successfully rolled out

// kubectl -n 7to12 rollout status deployment onion-flow --timeout=5m 5分钟后自动退出（超时的情况）
export const runReplaceDocer = async (executeBody, tagName) => {
  const dockerCmd = runDocker(executeBody)(tagName)
  await dockerCmd('switchContext')
  const { code, stderr, cmd } = await dockerCmd('replaceDocer')
  const status = 'replaceDocer'
  if (code !== 0) {
    throw exception(status, `replaceDocer error:${stderr}`, cmd)
  } else {
    return { code: 1, status: status, payload: cmd }
  }
}

// 增加docker替换status
export const runReplaceStatus = async (executeBody) => {
  await curryDocker(executeBody, '', 'switchContext')
  const status = 'dockerStatus'
  const { code, stderr, cmd, stdout } = await curryDocker(
    executeBody,
    '',
    status
  )
  if (code !== 0) {
    throw exception(status, `dockerStatus error:${stderr}`, cmd)
  } else {
    return { code: 1, status: status, payload: cmd, message: stdout }
  }
}
// push tag
export async function runGitPushTag(project, branch, tagName) {
  const { code, stderr } = await runShell(`git push origin ${tagName}`, project)
  const status = 'pushTag'
  if (code !== 0) {
    throw exception(
      status,
      `pushTag error:${stderr}`,
      `git push origin ${tagName}`
    )
  } else {
    const log = `${tagName}:打包上传完成`
    if (process.env.NODE_ENV === 'production') {
      await resJson(200, log, {
        project,
        env: branch,
        branch: branch,
        tagName,
        version: tagName,
        stage: 'end'
      })
    }
    return { code: 1, status: status, payload: `git push origin ${tagName}` }
  }
}

const getVersion = async (project, branch) => {
  const version = await runShell('git describe --abbrev=0 --tags', project)
  if (version.code !== 0) {
    return `v0.0.1_jarvis_${branch}`
  }
  return version.stdout.replace('\n', '')
}

export const runVersion = async (type, branch, project, version?) => {
  if (!version) {
    version = await getVersion(project, branch)
  }
  const versionArr: any = version.split('.')

  if (type === 'bug') {
    versionArr[2] = parseInt(versionArr[2]) + 1
  } else {
    if (parseInt(versionArr[1]) >= 300) {
      const mainVersion = versionArr[0].replace(/[^\d]/g, '')
      versionArr[0] = `v${parseInt(mainVersion) + 1}`
      versionArr[1] = 0
      versionArr[2] = 0
    } else {
      versionArr[1] = parseInt(versionArr[1]) + 1
      versionArr[2] = 0
    }
  }
  version = versionArr.join('.')
  const jarvis =
    process.env.NODE_ENV === 'production' ? '_jarvis_' : '_debugging_'
  const tagName = `${version}${jarvis}${branch}`
  return tagName
}

interface ExecuteBodyFace {
  command: string
  project: string
  npmTab: string
  hasModule: boolean
  prejectDetail: {
    id: string
    name: string
    belong: string
    describe: string
    config: {
      nodeVesion: string
    }
  }
}

export const runInstall = async (executeBody: ExecuteBodyFace) => {
  const { command, project, npmTab, hasModule, prejectDetail } = executeBody
  const nodeVersion = prejectDetail?.config?.nodeVesion || '14.21'
  if (hasModule === true) {
    await runShell(`rm -rf node_modules`, project)
  }
  if (isEmpty(command) === true) {
    return { code: 1, status: 'build', payload: command }
  }
  const cuttingCmd = compactArray(command)
  const { code, stderr, stdout, dockerName } = await runShellDocker(
    project,
    cuttingCmd,
    nodeVersion
  )
  if (code !== 0) {
    throw exception(
      `${npmTab} install `,
      `yarn error :${stderr}`,
      `${npmTab} install`,
      dockerName
    )
  } else {
    return {
      code: 1,
      payload: `${npmTab} install --production=false`,
      status: 'install'
    }
  }
}
