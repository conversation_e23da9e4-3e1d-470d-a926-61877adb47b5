// 存储数据功能
/**
 * @param {string} pg pg实例
 * @param {object} data 存储数据
 *
 */
import { Between } from 'typeorm'

export const SaveLogsFile = async (pg, data) => {
  console.log('SaveLogsFile')
  return await pg.save(data)
}

interface BuildData {
  createTime: string
  endTime: string
}
interface WhereFindFace {
  projectId: number
  created_at?
}
export const FindsBuildLogs = async (pg: any, id: number, data?: BuildData) => {
  const where: WhereFindFace = {
    projectId: id
  }
  if (data && data.createTime) {
    const { createTime, endTime } = data
    where.created_at = Between(createTime, endTime)
  }
  return await pg.find({
    where,
    order: {
      created_at: 'DESC'
    }
  })
}

export const getIOStatusAsync = (getIOStatusAsync: string) => {
  return new Promise((resolve, reject) => {
    try {
      global[getIOStatusAsync].push((status) => resolve(status))
      setTimeout(() => resolve(false), 2000)
    } catch (e) {
      resolve(false)
    }
  })
}
