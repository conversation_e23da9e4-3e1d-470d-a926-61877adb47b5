import { Test, TestingModule } from '@nestjs/testing';
import { NewProcessController } from './new-process.controller';

describe('NewProcessController', () => {
  let controller: NewProcessController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NewProcessController],
    }).compile();

    controller = module.get<NewProcessController>(NewProcessController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
