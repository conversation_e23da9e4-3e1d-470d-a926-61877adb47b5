import { HttpException, HttpStatus, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import {
  NewProcess,
  ProcessStatus
} from 'src/process/entities/newProcess.entity'
import { UserService } from 'src/user/user.service'
import { FindOptionsWhere, Repository } from 'typeorm'

@Injectable()
export class NewProcessService {
  constructor(
    @InjectRepository(NewProcess)
    public readonly processRepository: Repository<NewProcess>,
    private readonly userService: UserService
  ) {}

  async createNewProcess(user_id: string, project_id: string) {
    const processing = await this.processRepository.findOneBy({
      project_id,
      status: 0
    })
    if (processing) {
      throw new HttpException('存在正在进行的构建!', HttpStatus.BAD_REQUEST)
    }
    const user = await this.userService.findUserById(user_id)
    const new_process = this.processRepository.create({
      project_id,
      operator: user_id,
      operator_name: user!.name
    })
  }

  async createProcessItem(user_id: string, project_id: string) {
    const user = await this.userService.findUserById(user_id)
    const new_process = this.processRepository.create({
      project_id,
      operator: user_id,
      operator_name: user!.name
    })
    await this.processRepository.save(new_process)
    return new_process
  }

  async updateProcessTag(id: string, tag: string) {
    await this.processRepository.update(id, {
      tag
    })
  }

  async updateProcessStatus(id: string, status: ProcessStatus) {
    await this.processRepository.update(id, {
      status
    })
  }

  async getProcessPage(project_id: string) {
    return this.processRepository.find({
      take: 100,
      where: {
        project_id
      },
      order: {
        create_time: 'DESC'
      }
    })
  }
}
