import { Body, Controller, Get, Post, Query, Request } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { NewCreateProcessDto } from 'src/process/dto/create-process.dto'
import { NewProcessService } from 'src/process/new-process/new-process.service'

@ApiTags('构建相关接口')
@Controller('new-process')
export class NewProcessController {
  constructor(private readonly newProcessService: NewProcessService) {}
  // @Post('/create')
  // async createProcess(@Request() req: any, @Body() body: NewCreateProcessDto) {
  //   if (req.user) {
  //   }
  // }
  @ApiOperation({
    summary: '根据项目ID获取构建记录'
  })
  @Get('/list')
  async getProcessHistroy(@Query('project_id') project_id: string) {
    return this.newProcessService.getProcessPage(project_id)
  }
}
