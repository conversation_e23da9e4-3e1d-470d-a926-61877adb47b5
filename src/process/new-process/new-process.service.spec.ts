import { Test, TestingModule } from '@nestjs/testing';
import { NewProcessService } from './new-process.service';

describe('NewProcessService', () => {
  let service: NewProcessService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NewProcessService],
    }).compile();

    service = module.get<NewProcessService>(NewProcessService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
