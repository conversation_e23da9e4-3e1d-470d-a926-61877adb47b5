import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Param,
  Query,
  Request
} from '@nestjs/common'
import {
  SubscribeMessage,
  WebSocketGateway,
  MessageBody,
  ConnectedSocket,
  WebSocketServer
} from '@nestjs/websockets'
import { Socket } from 'socket.io'
import { ProcessService } from './process.service'
import { ExecuteProcess, BuildConfigParam } from './dto/create-process.dto'
import { ProjectServer } from 'src/project/project.service'
import { EventsService } from './utils/events.service'
import { getIOStatusAsync } from './utils/process.logs'

@Controller('process')
export class ProcessController {
  constructor(
    private processService: ProcessService,
    private projectsService: ProjectServer
  ) {}

  @Post()
  async create(@Body() executeProcess: ExecuteProcess, @Request() req: any) {
    const { id, project, branch } = executeProcess
    const findOneProject = await this.projectsService.findOne(id)
    if (!findOneProject) {
      throw new HttpException('Not found project or id', HttpStatus.BAD_REQUEST)
    }
    const processDetail = await this.processService.findOne(findOneProject.id)
    executeProcess = Object.assign(executeProcess, {
      prejectDetail: findOneProject,
      processDetail: processDetail
    })
    if (processDetail && processDetail.is_process === true) {
      return { ok: false, message: '当前项目处于构建中' }
    }

    if (global[`IoList-${id}-${project}-${branch}`]) {
      const openStatus = await getIOStatusAsync(
        `executeList-${id}-${project}-${branch}`
      )
      if (openStatus) {
        return { ok: false, message: '当前项目处于构建中' }
      }
    } else {
      global[`executeList-${id}-${project}-${branch}`] = []
      global[`IoList-${id}-${project}-${branch}`] = true
      global[`executeList-${id}-${project}-${branch}`].forEach((listener) =>
        listener(true)
      )
      global[`IoList-${id}-${project}-${branch}`] = false
      global[`executeList-${id}-${project}-${branch}`] = []

      return this.processService.execute(executeProcess, req.user.id)
    }
  }

  /**
   * 获取docker容器日志
   * @param process_id
   * @returns
   */
  @Get('dockerLog')
  async getProcessDockerLogs(@Query('process_id') process_id: string) {
    return this.processService.getDockerLogsFromId(process_id)
  }

  //获取最后更新接口
  @Get('lasttime')
  async findAll() {
    const dataRes = await this.processService.findAll()
    if (!dataRes.length) {
      return []
    }
    const dataArr: any[] = []
    try {
      for (const i in dataRes) {
        const item = dataRes[i].config.log
        if (item) {
          const lastItem = item[item.length - 1]
          const status = lastItem.status == 'pushTag' ? 'successed' : 'falied'
          const newdata = {
            id: dataRes[i].project.id,
            status: lastItem.completeTime && status,
            lastTime: lastItem.completeTime || ''
          }
          dataArr.push(newdata)
        }
      }
      return dataArr
    } catch (e) {
      console.log(e)
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return this.processService.findOne(id)
  }

  @Post('/reset')
  async resetConfig(@Body('id') id: string) {
    return this.processService.updateConfigReset(id)
  }
  // 构建日志批量返回
  @Post('/builds')
  async buildlogs(@Body() buildsConfig: BuildConfigParam) {
    const { projectId, createTime, endTime } = buildsConfig
    let timeData = {}
    if (createTime && endTime) {
      timeData = { createTime, endTime }
    }

    return this.processService.findAllBuildLogs(projectId, timeData)
  }
}

@WebSocketGateway({
  namespace: 'progress',
  allowEIO3: true,
  cors: { origin: '*' }
})
export class EventsGateway {
  constructor(private eventsService: EventsService) {}
  @WebSocketServer()
  private server: Socket

  @SubscribeMessage('progress')
  async handleEvent(
    @MessageBody() executeProcess: any,
    @ConnectedSocket() client: Socket
  ) {
    const { id } = executeProcess
    const myEmitter: any = this.eventsService.eventsEmitter()
    myEmitter.on(`progress`, (res) => {
      if (res.id == id) {
        client.emit(`progressBuild`, res)
      }
    })
  }
}
