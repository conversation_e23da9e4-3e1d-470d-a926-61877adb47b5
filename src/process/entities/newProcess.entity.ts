import {
  Column,
  CreateDateColumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

export type ProcessStatus = 0 | 1 | 2 //正在构建 | 构建成功 | 构建失败

@Entity()
export class NewProcess {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'varchar'
  })
  operator: string

  @Column({
    type: 'varchar'
  })
  operator_name: string

  @Column({
    type: 'uuid'
  })
  project_id: string

  @Column({
    type: 'int',
    default: 0
  })
  status: ProcessStatus

  @Column({
    type: 'text',
    nullable: true
  })
  log: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  tag: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  docker_name: string

  @CreateDateColumn({
    type: 'timestamp',
    nullable: true
  })
  create_time: Date

  @UpdateDateColumn({
    type: 'timestamp',
    nullable: true
  })
  update_time: Date
}
