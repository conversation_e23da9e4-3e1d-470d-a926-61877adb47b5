import { IsString, IsUUID } from 'class-validator'
import { PageBaseDto } from 'src/utils/pageBaseDto'

export interface CreateProcess {
  name: string
  age: number
  breed: string
}

interface ExecuteConfig {
  buildScript: unknown
}
export interface ExecuteProcess {
  project: string
  branch: string
  type: string
  hasInstall: string
  id: string
  config: ExecuteConfig //build config
  processDetail: any
  prejectDetail: any
  npmTab: string
  retryStatus: string //重新执行状态码
  hasModule: boolean
}

export interface BuildConfigParam {
  projectId: number
  createTime: string
  endTime: string
}

export class NewCreateProcessDto {
  @IsString()
  project_id: string
}
