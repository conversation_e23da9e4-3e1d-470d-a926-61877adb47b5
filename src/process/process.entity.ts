import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  OneToOne,
  JoinColumn
} from 'typeorm'

import { ProjectEntity } from 'src/project/entities/project.entity'
@Entity()
export class Process {
  @PrimaryGeneratedColumn()
  id: number

  @Column({
    nullable: false,
    default: ''
  })
  operator: string

  @Column({
    type: 'json',
    default: {}
  })
  config: any

  @Column({
    nullable: false
  })
  is_process: boolean

  @Column({
    default: false
  })
  status: boolean

  @OneToOne((type) => ProjectEntity)
  @JoinColumn({ name: 'project_id' })
  project: ProjectEntity

  @Column({
    type: 'json',
    default: {}
  })
  tag: unknown

  @Column({
    type: 'varchar',
    nullable: true
  })
  docker_name: string

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  created_at: Date

  @CreateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date
}

@Entity()
export class Building {
  @PrimaryGeneratedColumn()
  id: number

  @Column({
    nullable: false,
    name: 'project_id'
  })
  projectId: string

  @Column({
    type: 'json',
    default: {}
  })
  log: unknown

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  created_at: Date

  @CreateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updatedAt: Date
}
