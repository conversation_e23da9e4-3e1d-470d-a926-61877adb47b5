import { Module } from '@nestjs/common'
import { ProcessController, EventsGateway } from './process.controller'
import { ProcessService } from './process.service'
import { ProjectEntity } from 'src/project/entities/project.entity'
import { Process, Building } from './process.entity'
import { TypeOrmModule } from '@nestjs/typeorm'
import { EventsService } from './utils/events.service'
import { LoggerService } from 'src/logger/logger.service'
import { Logger } from 'src/logger/entities/logger.entitity'
import { ProjectModule } from 'src/project/project.module'
import { NewProcessService } from './new-process/new-process.service'
import { NewProcessController } from './new-process/new-process.controller'
import { NewProcess } from 'src/process/entities/newProcess.entity'
import { UserModule } from 'src/user/user.module'
import { GitlabModule } from '../gitlab/gitlab.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ProjectEntity,
      Process,
      Building,
      Logger,
      NewProcess
    ]),
    ProjectModule,
    UserModule,
    GitlabModule
  ],
  providers: [
    ProcessService,
    EventsGateway,
    EventsService,
    LoggerService,
    NewProcessService
  ],
  controllers: [ProcessController, NewProcessController],
  exports: [ProcessService]
})
export class ProcessModule {}
