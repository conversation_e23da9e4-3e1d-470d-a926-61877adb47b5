import * as shell from 'shelljs'

const checkOutShell = (path: string) => {
  return shell.cd(path)
}

const shellAsync = (cmd: string, silent = true) => {
  return new Promise((resolve, reject) => {
    resolve(undefined)
  })
}

const gitClone = (gitlab_address: string, project_save_path: string) => {
  return shell.exec(`git clone ${gitlab_address} ${project_save_path}`, {
    async: true,
    silent: true
  })
}

export async function cdAndGitCheckout(path: string, gitlab_address?: string) {
  const projectPath = `${process.env.CLONE_PATH}${path}`
  const cdRes = checkOutShell(projectPath)
  if (cdRes.stderr) {
    if (
      cdRes.stderr.indexOf('No such file or directory') !== -1 ||
      cdRes.stderr.indexOf('no such file or directory') !== -1 ||
      cdRes.stderr.indexOf('没有那个文件或目录') !== -1
    ) {
      const cloneRes = gitClone(gitlab_address as string, projectPath)
      if (cloneRes.stderr) {
        return {
          status: false,
          error: cloneRes.stderr
        }
      }
    }
  }
  console.log(cdRes)
  // let cmd = `git checkout .`
  // // const cmd = `git checkout -- ${projectPath}`;
  // let { code, stderr } = await runShell(cmd, path)
  // if (
  //   stderr.indexOf('No such file or directory') !== -1 ||
  //   stderr.indexOf('没有那个文件或目录') !== -1
  // ) {
  //   const cloneResult = await gitClone(address, projectPath)
  //   if (cloneResult.ok) {
  //     code = 0
  //   } else {
  //     cmd = 'git clone'
  //     stderr = 'git clone error'
  //   }
  // }
  // const status = 'init'
  // if (code !== 0) {
  //   throw exception(status, `init error :${stderr}`, cmd)
  // } else {
  //   return resJson(1, cmd, status)
  // }
}
