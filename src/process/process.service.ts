import { HttpException, HttpStatus, Injectable } from '@nestjs/common'
import { ExecuteProcess } from './dto/create-process.dto'
import { resJson } from './utils/execute'

import {
  cdAndGitCheckout,
  runGitCheckout,
  runGitPull,
  runShellGetDockerLog
} from 'src/utils/git'
import {
  branchEnv,
  runInstall,
  runBuild,
  runDockerBuild,
  runDockerPush,
  runGitPushTag,
  runReplaceDocer,
  runReplaceStatus,
  runGitTagName
} from './utils/execute'
import { tagNameStatus } from './process.config'
import { InjectRepository } from '@nestjs/typeorm'
import { EventsService } from './utils/events.service'
import { Repository } from 'typeorm'
import { Process, Building } from './process.entity'
import { FindsBuildLogs, SaveLogsFile } from './utils/process.logs'
import { ProjectServer } from '../project/project.service'
import { NewProcessService } from 'src/process/new-process/new-process.service'
import { getNewVersionTag } from '../pipeline/utils'
import { GitlabService } from '../gitlab/gitlab.service'

@Injectable()
export class ProcessService {
  constructor(
    @InjectRepository(Building)
    private readonly buildingRepository: Repository<Building>,
    @InjectRepository(Process)
    private readonly processRepository: Repository<Process>,
    private eventsService: EventsService,
    private readonly projectService: ProjectServer,
    private readonly newProcessService: NewProcessService,
    private readonly gitlabService: GitlabService
  ) {}
  // 构建方法
  async execute(executeBody, user_id: string) {
    const { operator, processDetail, prejectDetail } = executeBody
    if (!processDetail) {
      // 没有的创建数据
      const process = new Process()
      process.operator = operator
      process.config = {}
      process.is_process = true
      process.status = false
      process.project = prejectDetail
      process.tag = {}
      await this.processRepository.save(process)
      this.executeEach(executeBody, user_id)
    } else {
      // 执行构建
      this.executeEach(executeBody, user_id)
    }
    return {
      ok: true,
      message: '执行构建'
    }
  }
  private async executeEach(executeBody: ExecuteProcess, user_id: string) {
    const myEmitter: any = this.eventsService.eventsEmitter()
    const {
      id,
      hasInstall,
      retryStatus,
      processDetail,
      project,
      branch,
      prejectDetail
    } = executeBody
    const projectConfig = await this.projectService.getConfig(id)
    prejectDetail.config = {
      ...prejectDetail.config,
      ...projectConfig
    }
    await this.updateProcssDockerName(id, '')
    const new_process = await this.newProcessService.createProcessItem(
      user_id,
      id
    )
    const config: any = {}
    config.log = []
    config.id = id

    const executsMethod = [
      this.cdAndCheckout,
      this.branch,
      this.pull,
      this.install,
      this.build,
      this.createVresion,
      this.dockerBuild, // 6
      this.dockerPush,
      this.replaceDocker,
      this.ReplaceStatus,
      this.pushTag
    ]
    let executeRes
    // 跳转Npm intsall 流程
    if (hasInstall == 'no') {
      const tagIndex = executsMethod.indexOf(this.install)
      executsMethod.splice(tagIndex, 1)
    }

    // 重试方法
    let i = 0
    if (retryStatus === 'dockerBuild') {
      // 从第几个执行
      i = 6
      config.log = processDetail.config.log
    }
    if (retryStatus === 'dockerPush') {
      // 从第几个执行
      i = 7
      config.log = processDetail.config.log
    }
    if (retryStatus === 'replaceDocker') {
      // 从第几个执行
      i = 8
      config.log = processDetail.config.log
    }

    // 循环执行方法
    for (; i < executsMethod.length; i++) {
      const tagName = tagNameStatus(executsMethod[i].name)
      const palyTime = new Date().getTime()
      config.log[i] = {
        code: 2, // pending
        status: tagName
      }
      await this.upDateConfig(id, config.log)
      await myEmitter.emit('progress', config)
      try {
        // 执行构建方法
        executeRes = await executsMethod[i](executeBody, this)
        if (executeRes.tagName) {
          await this.newProcessService.updateProcessTag(
            new_process.id,
            executeRes.tagName
          )
        }
        config.log[i] = executeRes
        config.log[i].allTime = new Date().getTime() - palyTime
        if (tagName == 'pushTag') {
          config.log[i].completeTime = new Date().toJSON()
        }
        if (executeRes.dockerName) {
          await this.updateProcssDockerName(id, executeRes.dockerName)
        }
        await myEmitter.emit('progress', config)
      } catch (err) {
        executeRes = err
        config.log[i] = executeRes
        config.log[i].allTime = new Date().getTime() - palyTime
        config.log[i].completeTime = new Date().toJSON()
        //@ts-ignore
        const { tag } = await this.findOne(id)
        const { env } = branchEnv(branch)
        if (err.dockerName) {
          await this.updateProcssDockerName(id, err.dockerName)
        }
        if (process.env.NODE_ENV === 'production') {
          await resJson(405, `${tagName} ERROR 请到开发者平台查看详情`, {
            project,
            env: branch,
            branch: branch,
            tagName: tag[env],
            version: tag[env],
            stage: 'end'
          })
        }
        await myEmitter.emit('progress', config)
        break
      }
    }
    // 更新方法
    await this.upDateConfig(id, config.log, true)
    await this.upDateLock(id)
    if (config.log.length && config.log[config.log.length - 1]?.code !== 0) {
      await this.newProcessService.updateProcessStatus(new_process.id, 1)
    } else {
      await this.newProcessService.updateProcessStatus(new_process.id, 2)
    }
  }
  // cd 与 初始化
  private async cdAndCheckout(executeBody: ExecuteProcess) {
    const { project } = executeBody
    // 业务名称字段必传
    if (!project) {
      throw new HttpException(
        'Not found project or branch',
        HttpStatus.BAD_REQUEST
      )
    }
    // let env;
    // const br = branch || 'test';
    // // switch (br) {
    // //   case 'master':
    // //     env = 'production';
    // //     break;
    // //   case 'release':
    // //     env = 'stage';
    // //     break;
    // //   default:
    // //     env = 'development';
    // // }
    const address = executeBody.prejectDetail.address
    return await cdAndGitCheckout(project, address)
  }
  // 切换分支
  private async branch(executeBody: ExecuteProcess) {
    const { branch, project } = executeBody
    // const br = branch || 'test';
    // let tempBr = '';
    // let isProd = false;
    // switch (br) {
    //   case 'master':
    //     tempBr = 'release';
    //     isProd = true;
    //     break;
    //   case 'release':
    //     tempBr = 'master';
    //     break;
    //   default:
    //     tempBr = 'master';
    // }
    // await runGitCheckout(tempBr, project);
    // await runGitDeleteBranch(branch, project);
    return await runGitCheckout(branch, project)
  }
  // Git pull
  private async pull(executeBody: ExecuteProcess) {
    const { project } = executeBody
    return await runGitPull(project)
  }
  // npm 安装
  private async install(executeBody: ExecuteProcess) {
    const { npmTab } = executeBody
    const command = `${npmTab} install --production=false`
    return await runInstall(Object.assign(executeBody, { command }))
  }
  // build
  private async build(executeBody: ExecuteProcess) {
    const { branch, project, prejectDetail } = executeBody
    const { env } = branchEnv(branch)
    const command = prejectDetail.config[env + 'Build']
    const nodeVersion = prejectDetail?.config?.nodeVesion || '14.21'
    return await runBuild(branch, command, project, nodeVersion)
  }
  // 创建tag
  private async createVresion(executeBody: ExecuteProcess, self) {
    const { id, project, type, branch, processDetail, prejectDetail } =
      executeBody
    const tags = processDetail.tag || {}
    const { env } = branchEnv(branch)
    let version = await self.gitlabService.getGitlabBuildTag(
      prejectDetail.gitlab_id,
      branch
    )
    if (!version) {
      version = `v0.0.1_jarvis_${branch}`
    }
    const tagName = getNewVersionTag(version, type, branch)
    await runGitTagName(tagName, project)
    tags[env] = tagName
    await self.upDateTags(id, tags)
    return {
      code: 1,
      status: 'createTag',
      payload: '创建tag',
      tagName
    }
  }
  private async dockerBuild(executeBody: ExecuteProcess, self) {
    const { branch, id } = executeBody
    const { env } = branchEnv(branch)
    const { tag } = await self.findOne(id)

    return await runDockerBuild(executeBody, tag[env])
  }
  private async dockerPush(executeBody: ExecuteProcess, self) {
    const { branch, id } = executeBody
    const { env } = branchEnv(branch)
    const { tag } = await self.findOne(id)
    return await runDockerPush(executeBody, tag[env])
  }
  // pushTag
  private async pushTag(executeBody: ExecuteProcess, self) {
    const { id, project, branch } = executeBody
    const { env } = branchEnv(branch)
    const { tag } = await self.findOne(id)
    return runGitPushTag(project, branch, tag[env])
  }
  // 替换docker
  private async replaceDocker(executeBody: ExecuteProcess, self) {
    const { id, branch } = executeBody
    const { env } = branchEnv(branch)
    const { tag } = await self.findOne(id)
    if (branch !== 'master') {
      return runReplaceDocer(executeBody, tag[env])
    }
    return {
      code: 1,
      status: 'replaceDocer',
      payload: 'master不触发自动替换docker,交予运维执行后续策略'
    }
  }
  // 查看docker替换状态
  private async ReplaceStatus(executeBody: ExecuteProcess) {
    const { branch } = executeBody
    if (branch !== 'master') {
      return runReplaceStatus(executeBody)
    }
    return {
      code: 1,
      status: 'dockerStatus',
      payload: 'master不触发自动替换docker,交予运维执行后续策略'
    }
  }

  private async upDateConfig(
    process_id,
    config: { [key: string]: any },
    isSadaLog?
  ): Promise<void> {
    const processUpdate = await this.findOne(process_id)
    if (!processUpdate) {
      return
    }
    processUpdate.is_process = true
    processUpdate.config = {
      log: config
    }
    await this.processRepository.save(processUpdate)
    if (isSadaLog === true) {
      const buildinglogs = {
        projectId: process_id,
        log: config
      }
      SaveLogsFile(this.buildingRepository, buildinglogs)
    }
  }

  async updateProcssDockerName(process_id: string, docker_name: string) {
    const process = await this.findOne(process_id)
    if (process) {
      process.docker_name = docker_name
      await this.processRepository.save(process)
    }
  }

  async getDockerLogsFromId(id: string) {
    const process = await this.findOne(id)
    if (process && process.docker_name) {
      return runShellGetDockerLog(process.docker_name)
    } else {
      return ''
    }
  }

  private async upDateLock(process_id): Promise<void> {
    const processUpdate = await this.findOne(process_id)
    if (!processUpdate) {
      return
    }
    processUpdate.is_process = false
    await this.processRepository.save(processUpdate)
  }
  private async upDateTags(process_id, tags): Promise<void> {
    const processUpdate = await this.findOne(process_id)
    if (!processUpdate) {
      return
    }
    processUpdate.tag = tags
    await this.processRepository.save(processUpdate)
  }
  async findOne(projectId) {
    return await this.processRepository.findOne({
      where: { project: { id: projectId } },
      relations: ['project']
    })
  }
  //获取所有process的log
  async findAll() {
    return await this.processRepository.find({ relations: ['project'] })
  }
  // 解锁操作
  async updateConfigReset(process_id) {
    const processUpdate = await this.findOne(process_id)
    processUpdate!.is_process = false
    processUpdate!.config = {
      log: {}
    }
    return await this.processRepository.save(processUpdate!)
  }
  async findAllBuildLogs(projectId, data?) {
    return FindsBuildLogs(this.buildingRepository, projectId, data)
  }
}
