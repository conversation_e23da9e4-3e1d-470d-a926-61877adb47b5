<script lang="ts" setup>
import type { FormItemRule } from 'element-plus'
import type { Belong, Label, User } from '~/apis/enums'
import type { Project } from '~/apis/project'
import { ElMessage } from 'element-plus'
import { nextTick } from 'vue'
import { updateProject<PERSON>pi } from '~/apis/project'
import { INSTALL_CMDS, NAME_SPACE_CONFIG, NODE_VERSION_CONFIG } from '~/helpers/enum.util'
import { useUserStore } from '~/stores/user'

const props = defineProps<{
  show: boolean
  belongs: Belong[]
  labels: Label[]
  users: User[]
  project?: Project
}>()
const emits = defineEmits<{
  (e: 'update:show', value: boolean): void
  (e: 'done'): void
}>()

const dialogShow = computed({
  get() {
    return props.show
  },
  set(val) {
    emits('update:show', val)
  },
})
const userStore = useUserStore()
const projectForm = ref<HTMLFormElement>()
const modelForm = ref<Project & { deploymentsConfig: { name: string }[] }>({
  id: '',
  name: '',
  belong: '',
  developer: '',
  describe: '',
  label: [],
  address: '',
  gitlab_id: '',
  name_space: '',
  config: {
    nodeVesion: '',
    installCmd: '',
    masterBuild: '',
    releaseBuild: '',
    developBuild: '',
    delNodeModule: '',
  },
  is_white_list: false,
  use_docker: true,
  use_deployments: false,
  deploymentsConfig: [],
})

function labelVilidator(rule: any, value: any, callback: any) {
  if (value.some((el: string) => el === '1' || el === '2' || el === '3')) {
    callback()
  }
  else {
    callback(new Error('必选前端、后端或Native之一'))
  }
}

const rules: Record<string, FormItemRule[]> = {
  name: [
    {
      required: true,
      message: '请输入项目名称',
      trigger: 'blur',
    },
  ],
  belong: [
    {
      required: true,
      message: '请选择业务归属',
      trigger: 'change',
    },
  ],
  developer: [
    {
      required: true,
      message: '请选择负责人',
      trigger: 'change',
    },
  ],
  label: [
    {
      required: true,
      type: 'array' as const,
      min: 1,
      message: '请选择标签',
      trigger: 'change',
    },
    {
      validator: labelVilidator,
      trigger: 'change',

    },
  ],
  address: [
    {
      required: true,
      message: '请输入仓库SSH地址',
      trigger: 'blur',
    },
  ],
  describe: [
    {
      required: true,
      message: '请输入描述',
      trigger: 'blur',
    },
  ],
  gitlab_id: [
    {
      required: true,
      message: '请输入gitlab仓库中的项目ID',
      trigger: 'blur',
    },
  ],
  is_white_list: [
    {
      required: true,
      message: '请选择是否可以绿色通道上线',
      trigger: 'blur',
    },
  ],
  name_space: [
    {
      required: true,
      message: '请选择容器集群命名空间',
      trigger: 'change',
    },
  ],
}

// 处理动态新增deployments配置
function removeDeployments(index: number) {
  if (index !== -1 && modelForm.value.deploymentsConfig.length > 1) {
    modelForm.value.deploymentsConfig.splice(index, 1)
    // 清除相关的验证状态
    nextTick(() => {
      clearAllValidation()
    })
    return
  }
  if (modelForm.value.deploymentsConfig.length === 1) {
    modelForm.value.use_deployments = false
    modelForm.value.deploymentsConfig = []
    // 清除验证状态
    nextTick(() => {
      clearAllValidation()
    })
  }
}

function addDeployments() {
  if (modelForm.value.deploymentsConfig.length < 10) {
    modelForm.value.deploymentsConfig.push({
      name: '',
    })
    // 清除验证状态，避免新增项显示错误信息
    nextTick(() => {
      clearAllValidation()
    })
  }
  else {
    ElMessage({
      message: 'deployments最多支持10个',
      type: 'warning',
    })
  }
}

function changeUseDeployments(val: string | number | boolean | undefined) {
  const boolVal = Boolean(val)
  if (boolVal) {
    addDeployments()
  }
  else {
    modelForm.value.deploymentsConfig = []
  }
}

// 清除特定字段的验证状态
function clearFieldValidation(prop: string) {
  if (projectForm.value) {
    projectForm.value.clearValidate(prop)
  }
}

// 清除所有验证状态
function clearAllValidation() {
  if (projectForm.value) {
    projectForm.value.clearValidate()
  }
}

function reset() {
  projectForm.value?.resetFields()
  modelForm.value = {
    id: '',
    name: '',
    belong: '',
    developer: '',
    describe: '',
    label: [],
    address: '',
    gitlab_id: '',
    name_space: '',
    config: {
      nodeVesion: '',
      installCmd: '',
      masterBuild: '',
      releaseBuild: '',
      developBuild: '',
      delNodeModule: '',
    },
    is_white_list: false,
    use_docker: true,
    use_deployments: false,
    deploymentsConfig: [],
  }
}
// 校验构建信息模块：需要走构建流程的项目【前端项目】必填
function checkConfigForm() {
  if (!modelForm.value.config.nodeVesion) {
    ElMessage({
      message: '请选择依赖node版本~',
      type: 'warning',
    })
    return false
  }
  if (!modelForm.value.config.installCmd) {
    ElMessage({
      message: '请选择构建工具~',
      type: 'warning',
    })
    return false
  }
  if (!modelForm.value.config.masterBuild || !modelForm.value.config.releaseBuild || !modelForm.value.config.developBuild) {
    ElMessage({
      message: '请补齐各分支构建命令~',
      type: 'warning',
    })
    return false
  }
  return true
}
function submit() {
  projectForm.value?.validate(async (valid: boolean) => {
    if (!valid) {
      return
    }
    const checkRes = modelForm.value.label.includes('1') ? checkConfigForm() : true
    if (!checkRes) {
      return
    }
    if (modelForm.value.use_deployments && modelForm.value.deploymentsConfig.length === 0) {
      ElMessage({
        message: '请检查deployments配置~',
        type: 'warning',
      })
      return
    }
    const body: Project = {
      ...modelForm.value,
    }
    // 移除 deploymentsConfig 字段，因为它不属于 Project 类型
    delete (body as any).deploymentsConfig
    if (modelForm.value.use_deployments) {
      body.deployments = modelForm.value.deploymentsConfig.map(i => i.name)
    }
    updateProjectApi(body).then(() => {
      ElMessage({
        message: '配置成功！',
        type: 'success',
      })
      emits('done')
      dialogShow.value = false
    })
  })
}

watch(dialogShow, (val) => {
  if (!val) {
    reset()
  }
  else {
    if (props.project) {
      modelForm.value = { ...props.project, deploymentsConfig: [] }
      if (modelForm.value.deployments) {
        delete modelForm.value.deployments
      }
      if (!props.project.is_white_list) {
        modelForm.value.is_white_list = false
      }
      if (props.project.use_deployments && props.project.deployments && props.project.deployments?.length > 0) {
        modelForm.value.use_deployments = true
        modelForm.value.deploymentsConfig = props.project.deployments?.map(i => ({
          name: i,
        })) || []
      }
      else {
        modelForm.value.use_deployments = false
        modelForm.value.deploymentsConfig = []
      }
    }
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogShow"
    title="服务信息编辑"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    z-index="2000"
  >
    <el-form ref="projectForm" label-position="right" label-width="200px" :model="modelForm" :rules="rules">
      <h3 class="text-16px font-600">
        基础信息
      </h3>
      <el-form-item label="服务名称:" prop="name">
        <el-input v-model="modelForm.name" placeholder="请输入服务名称" />
      </el-form-item>
      <el-form-item label="项目业务归属:" prop="belong">
        <el-select v-model="modelForm.belong" placeholder="选择业务归属">
          <el-option v-for="option of belongs" :key="option.value" :label="option.label" :value="option.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人:" prop="developer">
        <el-select v-model="modelForm.developer" filterable placeholder="选择负责人">
          <el-option v-for="item of users" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="描述:" prop="describe">
        <el-input v-model="modelForm.describe" placeholder="请输入项目描述" />
      </el-form-item>
      <el-form-item label="标签:" prop="label">
        <el-select v-model="modelForm.label" multiple placeholder="请选择标签">
          <el-option v-for="(option, index) of labels" :key="`${option.value}-${index}`" :value="option.value" :label="option.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="仓库SSH地址:" prop="address">
        <el-input v-model="modelForm.address" placeholder="请输入仓库SSH地址" />
      </el-form-item>
      <el-form-item label="Gitlab仓库ID" prop="gitlab_id">
        <el-input v-model="modelForm.gitlab_id" placeholder="gitlab中项目ID" />
      </el-form-item>

      <h3 class="text-16px font-600">
        构建信息
      </h3>
      <div class="mb-20px color-red">
        注意：需要走构建流程的项目必填
      </div>
      <el-form-item label="依赖node版本" prop="config.nodeVesion">
        <el-select v-model="modelForm.config.nodeVesion" placeholder="请选择依赖的node版本基础镜像" style="width: 300px">
          <el-option v-for="(option, index) of NODE_VERSION_CONFIG" :key="`${option.value}-${index}`" :value="option.value" :label="option.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="构建工具" prop="config.installCmd">
        <el-select v-model="modelForm.config.installCmd" placeholder="请选择构建工具" style="width: 300px">
          <el-option v-for="(option, index) of INSTALL_CMDS" :key="`${option}-${index}`" :value="option" :label="option" />
        </el-select>
      </el-form-item>
      <el-form-item label="master构建" prop="config.masterBuild">
        <el-input v-model="modelForm.config.masterBuild" placeholder="请输入，格式示例：npm run build" />
      </el-form-item>
      <el-form-item label="release构建" prop="config.releaseBuild">
        <el-input v-model="modelForm.config.releaseBuild" placeholder="请输入，格式示例：npm run build" />
      </el-form-item>
      <el-form-item label="develop构建" prop="config.developBuild">
        <el-input v-model="modelForm.config.developBuild" placeholder="请输入，格式示例：npm run build" />
      </el-form-item>
      <el-form-item v-if="modelForm.config.installCmd === 'bun'" label="删除node_modules命令" prop="config.delNodeModule">
        <el-input v-model="modelForm.config.delNodeModule" placeholder="请输入，格式示例：rm -rf node_modules" />
      </el-form-item>

      <h3 class="text-16px font-600">
        部署配置
      </h3>
      <div class="mb-20px color-red">
        注意：需要部署及上线的项目必填
      </div>
      <el-form-item label="是否绿色通道上线：" prop="is_white_list">
        <el-radio-group v-model="modelForm.is_white_list">
          <el-radio :label="true" :disabled="userStore.user?.role !== '6'">
            是
          </el-radio>
          <el-radio :label="false">
            否
          </el-radio>
        </el-radio-group>
        <div class="ml-20px text-12px color-gray">
          绿色通道仅运维角色可更新
        </div>
      </el-form-item>
      <el-form-item label="是否docker构建：" prop="use_docker">
        <el-radio-group v-model="modelForm.use_docker">
          <el-radio :label="true">
            是
          </el-radio>
          <el-radio :label="false" :disabled="true">
            否
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="容器集群命名空间：" prop="name_space">
        <el-select v-model="modelForm.name_space" placeholder="请选择">
          <el-option v-for="option of NAME_SPACE_CONFIG" :key="option.label" :label="option.label" :value="option.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否更新多个deployment：" prop="use_deployments">
        <el-radio-group v-model="modelForm.use_deployments" @change="changeUseDeployments">
          <el-radio :label="true">
            是
          </el-radio>
          <el-radio :label="false">
            否
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="modelForm.use_deployments">
        <el-form-item
          v-for="(d, index) in modelForm.deploymentsConfig"
          :key="`deployment${index}`"
          :label="index === 0 ? 'deployments配置：' : ''"
          :prop="`deploymentsConfig.${index}.name`"
          :rules="[
            { required: true, message: '请输入', trigger: 'blur' },
          ]"
        >
          <div class="flex">
            <el-input
              v-model="d.name"
              style="margin-right: 10px; width: 200px;"
              placeholder="请输入deployment name"
              @input="() => clearFieldValidation(`deploymentsConfig.${index}.name`)"
            />
            <el-button @click.prevent="removeDeployments(index)">
              删除
            </el-button>
            <el-button type="primary" @click.prevent="addDeployments">
              添加
            </el-button>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="dialogShow = false">
        取消
      </el-button>
      <el-button type="primary" @click="submit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
