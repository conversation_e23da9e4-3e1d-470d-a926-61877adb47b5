<script lang="ts" setup>
import type { Belong, Label, User } from '~/apis/enums'
import type { Project } from '~/apis/project'
import { fetchAllUsers, fetchBelongs, fetchLabels } from '~/apis/enums'
import { deleteProjectApi, fetchProjectDetail } from '~/apis/project'
import ProjectFrom from '~/pages/projects/components/ProjectForm.vue'

const belongs = ref<Belong[]>([])
const users = ref<User[]>([])
const labels = ref<Label[]>([])
const projectInfo = ref<Project>()
const formShow = ref(false)

const route = useRoute()
const router = useRouter()
function fetch() {
  const id = route.query.id as string
  if (!id)
    return
  fetchProjectDetail(id).then((res) => {
    projectInfo.value = res
  }).catch(() => {
    ElMessage({
      type: 'error',
      message: '获取项目详情失败',
    })
  })
}
function init() {
  fetchAllUsers().then((res) => {
    users.value = res
  })
  fetchBelongs().then((res) => {
    belongs.value = res
  })
  fetchLabels().then((res) => {
    labels.value = res
  })
  fetch()
}

onMounted(() => {
  init()
})

function belongTransform(value: string | undefined) {
  if (!value)
    return
  return belongs.value.find(el => el.value === value)?.label
}
function userTransform(value: string | undefined) {
  if (!value)
    return
  return users.value.find(el => el.value === value)?.label
}

function confirmDel() {
  ElMessageBox.confirm('是否确认删除此服务？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    if (!projectInfo.value || !projectInfo.value.id) {
      return
    }
    deleteProjectApi(projectInfo.value.id).then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功!',
      })
      router.back()
    })
  })
}
</script>

<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/projects' }">
        项目列表
      </el-breadcrumb-item>
      <el-breadcrumb-item>
        <a href="/">项目详情</a>
      </el-breadcrumb-item>
    </el-breadcrumb>

    <div class="mb-40px flex items-center justify-end">
      <el-button type="primary" @click="formShow = true">
        编辑
      </el-button>
      <el-button
        type="danger"
        @click="confirmDel"
      >
        删除
      </el-button>
    </div>

    <el-descriptions title="基础信息" :column="2" border>
      <el-descriptions-item :span="1" label="服务名称">
        {{ projectInfo?.name }}
      </el-descriptions-item>
      <el-descriptions-item label="项目业务归属">
        {{ belongTransform(projectInfo?.belong) }}
      </el-descriptions-item>
      <el-descriptions-item label="负责人">
        {{ userTransform(projectInfo?.developer) }}
      </el-descriptions-item>
      <el-descriptions-item label="描述">
        {{ projectInfo?.describe }}
      </el-descriptions-item>
      <el-descriptions-item label="仓库地址">
        {{ projectInfo?.address }}
      </el-descriptions-item>
      <el-descriptions-item label="Gitlab仓库ID">
        {{ projectInfo?.gitlab_id }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      style="margin-top: 20px"
      title="构建信息"
      :column="2"
      border
    >
      <el-descriptions-item label="构建工具">
        {{ projectInfo?.config?.installCmd }}
      </el-descriptions-item>
      <el-descriptions-item label="master构建">
        {{ projectInfo?.config?.masterBuild }}
      </el-descriptions-item>
      <el-descriptions-item label="release构建">
        {{ projectInfo?.config?.releaseBuild }}
      </el-descriptions-item>
      <el-descriptions-item label="develop构建">
        {{ projectInfo?.config?.developBuild }}
      </el-descriptions-item>
      <el-descriptions-item label="强依赖node版本">
        {{ projectInfo?.config?.nodeVesion }}
      </el-descriptions-item>
      <el-descriptions-item v-if="projectInfo?.config?.delNodeModule" label="删除node_modules命令">
        {{ projectInfo?.config?.delNodeModule }}
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      style="margin-top: 20px"
      title="部署信息"
      :column="2"
      border
    >
      <el-descriptions-item label="是否绿色通道上线">
        {{ projectInfo?.is_white_list ? '是' : '否' }}
      </el-descriptions-item>
      <el-descriptions-item label="是否docker构建">
        {{ projectInfo?.use_docker ? '是' : '否' }}
      </el-descriptions-item>
      <el-descriptions-item label="容器集群命名空间">
        {{ projectInfo?.name_space }}
      </el-descriptions-item>
      <el-descriptions-item label="是否更新多个deployment">
        {{ projectInfo?.deployments && projectInfo?.deployments?.length > 0 ? '是' : '否' }}
      </el-descriptions-item>
      <el-descriptions-item v-if="projectInfo?.deployments && projectInfo?.deployments.length > 0" label="deployment配置">
        <span v-for="(item, index) in projectInfo.deployments" :key="`deploymentname${index}`">【{{ item }}】</span>
      </el-descriptions-item>
    </el-descriptions>

    <ProjectFrom v-model:show="formShow" :project="projectInfo" :belongs="belongs" :labels="labels" :users="users" @done="fetch" />
  </div>
</template>
