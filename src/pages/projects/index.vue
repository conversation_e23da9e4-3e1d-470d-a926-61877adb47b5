<script setup lang="ts">
import type { Belong, Label, User } from '~/apis/enums'
import type { Project } from '~/apis/project'
import { fetchAllUsers, fetchBelongs, fetchLabels } from '~/apis/enums'
import { fetchProjectList } from '~/apis/project'
import ProjectFrom from '~/pages/projects/components/ProjectForm.vue'
import { useUserStore } from '~/stores/user'

const belongs = ref<Belong[]>([])
const labels = ref<Label[]>([])
const users = ref<User[]>([])
const userStore = useUserStore()
function belongTransform(value: string) {
  return belongs.value.find(el => el.value === value)?.label
}

function labelTransform(value: string) {
  return labels.value.find(el => el.value === value)?.label
}
function userTransform(value: string) {
  return users.value.find(el => el.value === value)?.label
}

const filters = reactive({
  name: '',
  belong: '',
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
})

const list = ref<Project[]>([])
const total = ref(0)
const formDialogShow = ref(false)

const query = computed(() => {
  return {
    ...filters,
    page: pagination.page,
    pageSize: pagination.pageSize,
  }
})
function fetch() {
  fetchProjectList(query.value).then((res) => {
    list.value = res.list
    total.value = res.total
  })
}

function search() {
  pagination.page = 1
  fetch()
}

function reset() {
  pagination.page = 1
  filters.name = ''
  filters.belong = ''
  fetch()
}

function handleSizeChange(val: number) {
  pagination.pageSize = val
  pagination.page = 1
  fetch()
}

function handleCurrentChange(val: number) {
  pagination.page = val
  fetch()
}

const router = useRouter()
function routeToDetail(row: Project) {
  router.push({
    path: '/projects/detail',
    query: {
      id: row.id,
    },
  })
}

function goDeploy(row: Project) {
  router.push({
    path: '/build',
    query: {
      projectId: row.id,
    },
  })
}

function addNewProject() {
  formDialogShow.value = true
}

function init() {
  fetchAllUsers().then((res) => {
    users.value = res
  })
  fetchBelongs().then((res) => {
    belongs.value = res
  })
  fetchLabels().then((res) => {
    labels.value = res
  })
  if (userStore.user && userStore.user.belong) {
    filters.belong = userStore.user.belong
  }
  fetch()
}

onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <div class="mb-20px flex items-start justify-start">
      <el-form label-position="left" inline>
        <el-form-item label="服务名称:">
          <el-input v-model="filters.name" placeholder="请输入服务名称" />
        </el-form-item>
        <el-form-item label="项目业务归属:">
          <el-select v-model="filters.belong" placeholder="请选择业务归属" style="width: 150px" filterable>
            <el-option
              v-for="option of belongs"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="ml-20px flex flex-1 justify-between">
        <div>
          <el-button type="primary" @click="search">
            查询
          </el-button>
          <el-button @click="reset">
            重置
          </el-button>
        </div>
        <el-button
          type="primary"
          @click="addNewProject"
        >
          新增
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <el-table :data="list" border>
        <el-table-column label="序号" type="index" align="center" />
        <el-table-column label="名称" prop="name" align="center" />
        <el-table-column label="项目业务归属" prop="belong" align="center">
          <template #default="scope">
            <el-tag type="primary">
              {{
                belongTransform(scope.row.belong)
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="负责人" prop="developer" align="center">
          <template #default="scope">
            {{ userTransform(scope.row.developer) }}
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="describe" align="center" />
        <el-table-column label="标签" prop="label" align="center">
          <template #default="scope">
            <div class="flex flex-col items-center justify-center">
              <el-tag v-for="label of scope.row.label" :key="label" size="small">
                {{ labelTransform(label) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="routeToDetail(scope.row)">
              详情
            </el-button>
            <el-button type="text" @click="goDeploy(scope.row)">
              构建部署
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="p-20px text-align-center">
        <el-pagination
          v-model:current-page="pagination.page"
          background
          :page-sizes="[20, 40, 80, 100]"
          :page-size="20"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <ProjectFrom v-model:show="formDialogShow" :belongs="belongs" :labels="labels" :users="users" @done="fetch" />
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    name: 项目
    sort: 2
    path: /projects
    icon: i-material-symbols-format-list-bulleted-rounded
</route>
