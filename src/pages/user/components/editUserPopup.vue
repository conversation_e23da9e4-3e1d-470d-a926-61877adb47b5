<script setup lang="ts">
import type { UserInfoType } from '~/apis/user'
import { ElMessage } from 'element-plus'
import { setUpdateUserApi } from '~/apis/user'
import { belongList, roleList } from '~/pages/user/utils'

const props = defineProps<{
  manuallyClose: boolean
  dialogForm: boolean
  pageType: boolean
  rawData?: UserInfoType
}>()

const emits = defineEmits(['closeDialog'])
const visible = ref(false)
const title = ref('编辑个人信息')
const showH5Style = ref(false)
const ruleForm = ref()
function closeDialog() {
  emits('closeDialog')
  ruleForm.value.resetFields()
}
const form = ref({
  name: '',
  role: '',
  belong: '',
  phone: '',
})

const rules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' },
  ],
  belong: [
    { required: true, message: '请选择业务归属', trigger: 'change' },
  ],
  phone: [
    { required: true, message: '请输入手机', trigger: 'blur' },
  ],
}

function cancelClick() {
  if (props.manuallyClose) {
    visible.value = false
  }
  else {
    ElMessage({
      message: '请完善信息并确认',
      type: 'warning',
    })
  }
}
function successClick() {
  ruleForm.value.validate(async (valid: boolean) => {
    if (valid) {
      const data = props.rawData
        ? {
            ...props.rawData,
            ...form.value,
          }
        : form.value
      await setUpdateUserApi(data)
      ElMessage({
        message: '编辑成功',
        type: 'success',
      })
      visible.value = false
    }
    else {
      return false
    }
  })
}

const route = useRoute()
watch(() => props.dialogForm, (val) => {
  visible.value = val
  if (val) {
    if (props.rawData) {
      Object.assign(form.value, props.rawData)
    }
    if (route.path.includes('projectEdition') && !showH5Style.value) {
      showH5Style.value = true
      return
    }
    if (!props.pageType) {
      title.value = '哦豁！个人信息不全请补全后再使用'
    }
  }
}, {
  immediate: true,
})
</script>

<template>
  <el-dialog v-model="visible" :title="title" :show-close="manuallyClose" :close-on-click-modal="manuallyClose" :close-on-press-escape="manuallyClose" :class="{ mobile: showH5Style }" @close="closeDialog">
    <el-form ref="ruleForm" :model="form" label-width="160px" :rules="rules">
      <el-form-item label="姓名：" prop="name">
        <el-input v-model="form.name" maxlength="20" />
      </el-form-item>
      <el-form-item label="角色：" prop="role">
        <el-select v-model="form.role" placeholder="请选择角色">
          <el-option v-for="(item, index) in roleList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="人员业务归属：" prop="belong">
        <el-select v-model="form.belong" placeholder="请选择业务归属">
          <el-option v-for="(item, index) in belongList" :key="index" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号：" prop="phone">
        <el-input v-model="form.phone" maxlength="11" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelClick">
          取 消
        </el-button>
        <el-button type="primary" @click="successClick">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
