<script lang="ts" setup>
import type { User } from '~/apis/enums'
import type { Project } from '~/apis/project'
import type { FetchReleaseListReq, Release, SonicItem } from '~/apis/release'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { onBeforeMount, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { fetchAllUsers } from '~/apis/enums'
import { fetchAllProjectList } from '~/apis/project'
import { fetchReleaseList, fetchReleaseNotice } from '~/apis/release'
import ReleasePopup from '~/components/ReleasePopup.vue'

// 上线申请基础结构
const RELEASE: Partial<Release> = {
  project_id: '',
  tag: '',
  publish_content: '',
  sql: '',
  remark: '',
  depend_project: '',
  notify_immediately: false,
  developer: '',
  tester: '',
  sonic: [] as SonicItem[],
}

const router = useRouter()
const route = useRoute()

// 响应式状态
const currentPage = ref(1) // 当前页数
const pageSize = ref(100) // 每页的条数
const total = ref(0) // 总条数
const projectList = ref<Release[]>([]) // 上线列表
const release = ref<Partial<Release>>({ ...RELEASE })
const dialogVisible = ref(false) // 上线弹窗
const isUpdateMode = ref(false)
const sendMsgLoading = ref(false)
const searchForm = reactive({
  createTime: [] as string[],
  status: '',
  developer: '',
  tester: '',
  projectId: '',
})
const testerList = ref<User[]>([])
const developerList = ref<User[]>([])
const serviceList = ref<Project[]>([])

// 方法
function formatTime(time: string) {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--'
}

function formatSonic(row: Release) {
  if (row.sonic && row.sonic.length > 0) {
    const sonicItem = row.sonic[0]
    return `${sonicItem.projectName}-${sonicItem.caseSuiteName}`
  }
  else {
    return '--'
  }
}

function getReleaseList() {
  const param: FetchReleaseListReq = {
    page: currentPage.value,
    pageSize: pageSize.value,
    ...searchForm,
  }
  if (param.createTime) {
    param.createTimeStart = param.createTime[0]
    param.createTimeEnd = param.createTime[1]
    delete param.createTime
  }
  fetchReleaseList(param).then((res) => {
    projectList.value = res.list
    total.value = res.total
    sendMsgLoading.value = false
  })
}

function getAllUsers() {
  fetchAllUsers().then((res) => {
    testerList.value = res.filter(item => item.role === '5')
    developerList.value = res.filter(item => ['1', '2', '3', '4'].includes(item.role))
  })
}

function getAllProjectList() {
  fetchAllProjectList().then((res) => {
    serviceList.value = res
  })
}

function handleSearch() {
  currentPage.value = 1
  getReleaseList()
}

function resetForm() {
  Object.assign(searchForm, {
    createTime: [],
    status: '',
    developer: '',
    tester: '',
    projectId: '',
  })
  handleSearch()
}

function handleSizeChange(val: number) {
  pageSize.value = val
  currentPage.value = 1
  getReleaseList()
}

function handleCurrentChange(val: number) {
  currentPage.value = val
  pageSize.value = 100
  getReleaseList()
}

function openDialog() {
  isUpdateMode.value = false
  dialogVisible.value = true
  release.value = { ...RELEASE }
}

function handleSuccess() {
  getReleaseList()
}

function editRelease(row: Release) {
  release.value = {
    ...row,
  }
  isUpdateMode.value = true
  dialogVisible.value = true
}

function notifyTester(row: Release) {
  const { id } = row
  sendMsgLoading.value = true
  fetchReleaseNotice(id as string).then(() => {
    ElMessage({
      showClose: true,
      message: '已经通知',
      type: 'success',
      duration: 5000,
    })
    getReleaseList()
  }).catch(() => {
    ElMessage({
      showClose: true,
      message: '创建失败',
      type: 'error',
      duration: 5000,
    })
  })
}

// 是否显示通知测试按钮
function isShowPublisherNoticeToTester(row: Release) {
  return row.status_text?.includes('测试') && !row.notify_immediately
}

function openStatistic() {
  window.open(router.resolve({
    path: '/release/statistic',
  }).href, '_blank')
}

// 生命周期钩子
onBeforeMount(() => {
  const { status, startTime, endTime, projectId, developer, tester } = route.query
  if (status) {
    searchForm.status = status as string
  }
  if (startTime && endTime) {
    searchForm.createTime = [startTime, endTime] as string[]
  }
  if (projectId) {
    searchForm.projectId = projectId as string
  }
  if (developer) {
    searchForm.developer = developer as string
  }
  if (tester) {
    searchForm.tester = tester as string
  }
  getReleaseList()
  getAllUsers()
  getAllProjectList()
})
</script>

<template>
  <div class="relative">
    <el-card class="mb-6 w-2/5 text-sm text-gray-600">
      <div class="mb-2 text-lg font-bold">
        上线窗口期说明
      </div>
      <p>
        <el-tag effect="dark" size="large">
          下午窗口期
        </el-tag>
        <span class="ml-2 inline-block w-40">下午02:00 - 下午05:00</span>
        <span>开启规则：工作日（非寒暑假期间）</span>
      </p>
      <p class="mt-2">
        <el-tag effect="dark" type="success" size="large">
          晚上窗口期
        </el-tag>
        <span class="ml-2 inline-block w-40">夜间23:30 - 次日05:00</span>
        <span>开启规则：每天都可以，无限制</span>
      </p>
      <p class="mt-2">
        Tips：绿色通道项目不受上线窗口期限制，绿色通道标签需要找运维大佬申请
      </p>
    </el-card>
    <div class="absolute right-0 top-0">
      <el-button type="primary" link @click="openStatistic()">
        查看上线数据
      </el-button>
      <el-button type="primary" @click="openDialog()">
        新建上线
      </el-button>
    </div>
    <el-form label-width="80px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="上线时间">
            <el-date-picker
              v-model="searchForm.createTime"
              style="width: 100%"
              type="datetimerange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="服务名称">
            <el-select v-model="searchForm.projectId" style="width: 100%" placeholder="请选择服务" clearable filterable>
              <el-option v-for="item in serviceList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="开发">
            <el-select v-model="searchForm.developer" style="width: 100%" placeholder="请选择开发" clearable filterable>
              <el-option v-for="item in developerList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="测试">
            <el-select v-model="searchForm.tester" style="width: 100%" placeholder="请选择测试" clearable filterable>
              <el-option v-for="item in testerList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" style="width: 100%" placeholder="请选择状态" clearable>
              <el-option label="待测试" value="pending" />
              <el-option label="已部署" value="deployed" />
              <el-option label="已废弃" value="abandoned" />
              <el-option label="已回滚" value="rolledBack" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              查询
            </el-button>
            <el-button @click="resetForm">
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="projectList" border>
      <el-table-column fixed align="center" label="服务名称" width="220" prop="name" />
      <el-table-column align="center" label="上线 tag " width="220" prop="tag" />
      <el-table-column align="center" label="状态" prop="status_text" />
      <el-table-column align="center" label="开发" prop="developer_name" />
      <el-table-column align="center" label="测试" prop="tester_name" />
      <el-table-column align="center" label="上线内容" prop="publish_content" />
      <el-table-column align="center" label="依赖" prop="depend_project" />
      <el-table-column align="center" label="SQL" prop="sql" />
      <el-table-column align="center" label="备注" prop="remark" />
      <el-table-column align="center" label="UI测试用例">
        <template #default="scope">
          {{ formatSonic(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间">
        <template #default="scope">
          {{ formatTime(scope.row.create_time) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="更新时间">
        <template #default="scope">
          {{ formatTime(scope.row.update_time) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="上线时间">
        <template #default="scope">
          {{ formatTime(scope.row.deployed_time) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="废弃时间">
        <template #default="scope">
          {{ formatTime(scope.row.abandoned_time) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" align="center" label="操作" width="220">
        <template #default="scope">
          <el-button
            v-if="isShowPublisherNoticeToTester(scope.row)"
            type="primary"
            size="small"
            :loading="sendMsgLoading"
            @click="notifyTester(scope.row)"
          >
            通知测试
          </el-button>
          <el-button
            v-if="scope.row.status === 'pending'"
            type="primary"
            size="small"
            plain
            @click="editRelease(scope.row)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        class="mt-4"
        background
        layout="->, total, sizes, prev, pager, next, jumper"
        :page-sizes="[100, 200, 300, 400]"
        :page-size="pageSize"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <ReleasePopup
      v-if="dialogVisible"
      v-model="dialogVisible"
      :release-info="release"
      :is-update="isUpdateMode"
      @refresh="handleSuccess"
    />
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    name: 上线
    sort: 3
    path: /release
    icon: i-solar-flag-linear
</route>
