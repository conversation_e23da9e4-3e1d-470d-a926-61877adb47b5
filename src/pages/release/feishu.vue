<script lang="ts" setup>
import { computed, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import ReleasePopup from '~/components/ReleasePopup.vue'

const route = useRoute()
const show = ref(true)
const releaseInfo = reactive({
  project_id: '',
  tag: '',
  publish_content: '',
  sql: '',
  remark: '',
  depend_project: '',
  notify_immediately: false,
})

const isUpdateMode = computed(() => !!route.query.id)
</script>

<template>
  <ReleasePopup v-model="show" :h5style="true" :release-info="releaseInfo" :is-update="isUpdateMode" />
  <h4>上线申请已提交，Jarvis上线管理可查看更详细信息哦～</h4>
</template>
