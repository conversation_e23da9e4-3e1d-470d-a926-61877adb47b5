<script lang="ts" setup>
import type { User } from '~/apis/enums'
import type { Project } from '~/apis/project'
import type { FetchReleaseStatisticsResp } from '~/apis/release'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { ElLoading, ElMessage } from 'element-plus'
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { fetchAllUsers } from '~/apis/enums'
import { fetchAllProjectList } from '~/apis/project'
import { fetchReleaseStatistics } from '~/apis/release'

const router = useRouter()

const form = ref({
  date: undefined,
  startTime: '',
  endTime: '',
  userId: undefined,
  projectId: undefined,
})

const userList = ref<User[]>([])
const serviceList = ref<Project[]>([])
const countsByStatus = ref({
  total: 0,
  deployed: 0,
  rolledBack: 0,
  pending: 0,
  abandoned: 0,
})

let barRank: echarts.ECharts
let heatmapDate: echarts.ECharts
let heatmapHour: echarts.ECharts

function onPick([minDate, maxDate]: [Date, Date]) {
  if (maxDate && minDate) {
    const oneYear = 366 * 24 * 3600 * 1000
    if (maxDate.getTime() - minDate.getTime() > oneYear) {
      ElMessage.error('选择的时间范围不能超过一年')
      form.value.date = undefined
    }
  }
}

const statistics = ref([
  {
    title: '总数',
    prop: 'total',
  },
  {
    title: '已上线',
    prop: 'deployed',
  },
  {
    title: '待上线',
    prop: 'pending',
  },
  {
    title: '已废弃',
    prop: 'abandoned',
  },
  {
    title: '已回滚',
    prop: 'rolledBack',
  },
  {
    title: '异常比例',
    prop: 'rolledBack',
  },
])

const barRankRef = ref(null)
const heatmapDateRef = ref(null)
const heatmapHourRef = ref(null)

onMounted(() => {
  getAllUsers()
  getAllProjectList()
  getData()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

function getData() {
  if (form.value.date) {
    form.value.startTime = dayjs(form.value.date[0]).format('YYYY-MM-DD HH:mm:ss')
    form.value.endTime = dayjs(form.value.date[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  else {
    form.value.startTime = ''
    form.value.endTime = ''
  }
  const loading = ElLoading.service({
    text: '数据加载中...',
  })
  fetchReleaseStatistics({
    startTime: form.value.startTime,
    endTime: form.value.endTime,
    userId: form.value.userId,
    projectId: form.value.projectId,
  }).then((res) => {
    countsByStatus.value = {
      total: 0,
      deployed: 0,
      rolledBack: 0,
      pending: 0,
      abandoned: 0,
    }
    res.countsByStatus.forEach((item) => {
      countsByStatus.value[item.status as keyof typeof countsByStatus.value] = Number(item.count)
      countsByStatus.value.total += Number(item.count)
    })
    nextTick(() => {
      initBarRank(res.countsByProject.reverse())
      initHeatmapDate(form.value.startTime, form.value.endTime, res.heatmapByDate)
      initHeatmapHour(res.heatmapByHour)
    })
  }).finally(() => {
    loading.close()
  })
}

function getAllUsers() {
  fetchAllUsers().then((res) => {
    userList.value = res || []
  })
}

function getAllProjectList() {
  fetchAllProjectList().then((res) => {
    serviceList.value = res
  })
}

function handleResize() {
  barRank?.resize()
  heatmapDate?.resize()
  heatmapHour?.resize()
}

function handleStatusClick(status: string) {
  const query = {
    status: status === 'total' ? '' : status,
    startTime: form.value.startTime,
    endTime: form.value.endTime,
    projectId: form.value.projectId,
    tester: undefined,
    developer: undefined,
  }
  if (form.value.userId) {
    const user = userList.value.find(item => item.value === form.value.userId)
    if (user?.role === '5') {
      query.tester = form.value.userId
    }
    else {
      query.developer = form.value.userId
    }
  }
  window.open(router.resolve({
    path: '/release',
    query,
  }).href, '_blank')
}

function initBarRank(list: FetchReleaseStatisticsResp['countsByProject']) {
  if (!barRankRef.value) {
    return
  }
  barRank = echarts.init(barRankRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter(params: { name: string, value: number }[]) {
        return `${params[0].name}：${params[0].value}`
      },
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: list.map(item => item.name),
      axisLabel: {
        width: 80,
        overflow: 'truncate',
      },
    },
    series: [
      {
        data: list.map(item => item.count),
        type: 'bar',
      },
    ],
    grid: {
      left: 100,
      right: 30,
      top: '10%',
      bottom: '10%',
    },
  }
  barRank.setOption(option)
}

function initHeatmapDate(startTime: string, endTime: string, list: FetchReleaseStatisticsResp['heatmapByDate']) {
  if (!heatmapDateRef.value) {
    return
  }
  if (startTime && endTime) {
    startTime = dayjs(startTime).format('YYYY-MM-DD')
    endTime = dayjs(endTime).format('YYYY-MM-DD')
  }
  else {
    startTime = dayjs().subtract(1, 'year').format('YYYY-MM-DD')
    endTime = dayjs().format('YYYY-MM-DD')
  }
  heatmapDate = echarts.init(heatmapDateRef.value)

  const data: (string | number)[][] = []
  let max = 0
  list.forEach((item) => {
    if (Number(item.count) > max) {
      max = Number(item.count)
    }
    data.push([
      dayjs(item.date).format('YYYY-MM-DD'),
      item.count,
    ])
  })
  const option = {
    tooltip: {
      position: 'top',
      formatter(params: { value: (string | number)[] }) {
        return `${params.value[0]}：${params.value[1]}`
      },
    },
    visualMap: {
      min: 0,
      max,
      type: 'piecewise',
      orient: 'horizontal',
      show: false,
    },
    calendar: {
      left: 30,
      right: 30,
      cellSize: ['auto', 'auto'],
      range: [startTime, endTime],
      itemStyle: {
        borderWidth: 0.5,
      },
    },
    series: {
      type: 'heatmap',
      coordinateSystem: 'calendar',
      data,
    },
  }
  heatmapDate.setOption(option)
}

function initHeatmapHour(list: FetchReleaseStatisticsResp['heatmapByHour']) {
  if (!heatmapHourRef.value) {
    return
  }
  heatmapHour = echarts.init(heatmapHourRef.value)
  const data: (string | number)[][] = []
  let max = 0
  list.forEach((item) => {
    if (Number(item.count) > max) {
      max = Number(item.count)
    }
    data.push([
      item.hour,
      0,
      item.count,
    ])
  })
  const hours = Array.from({ length: 24 }).fill(0).map((item, index) => `${index}点`)
  const days = ['']
  const option = {
    tooltip: {
      position: 'top',
    },
    grid: {
      left: 30,
      right: 30,
      top: '10%',
      bottom: '10%',
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: 0,
      max,
      orient: 'horizontal',
      left: 'center',
      show: false,
    },
    series: [
      {
        type: 'heatmap',
        data,
        label: {
          show: true,
        },
      },
    ],
  }
  heatmapHour.setOption(option)
}
</script>

<template>
  <div>
    <el-form :inline="true">
      <el-form-item label="上线时间">
        <el-date-picker
          v-model="form.date"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(new Date().setHours(0, 0, 0)), new Date(new Date().setHours(23, 59, 59))]"
          @calendar-change="onPick"
          @change="getData"
        />
      </el-form-item>
      <el-form-item label="人员">
        <el-select v-model="form.userId" placeholder="请选择" filterable clearable @change="getData">
          <el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="服务">
        <el-select v-model="form.projectId" placeholder="请选择服务" clearable filterable @change="getData">
          <el-option v-for="item in serviceList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <el-empty v-if="!countsByStatus.total" description="当前筛选条件无上线记录" />
    <template v-else>
      <el-card header="数据总览">
        <div class="statistics flex flex-wrap items-center justify-between">
          <el-statistic v-for="(item, index) in statistics" :key="index" class="mb-4 mt-4 w-1/3 text-center" :formatter="() => ''">
            <template #title>
              {{ item.title }}
              <el-tooltip v-if="item.title === '异常比例'" content="已回滚/总数" placement="right">
                <div class="i-solar-info-circle-bold inline-block cursor-pointer" />
              </el-tooltip>
            </template>
            <template #suffix>
              <div class="cursor-pointer" style="color: #337ab7;" @click="handleStatusClick(item.prop)">
                <span v-if="item.title === '异常比例'">{{ ((countsByStatus.rolledBack / countsByStatus.total) || 0).toFixed(2)
                }}</span>
                <span v-else>{{ countsByStatus[item.prop as keyof typeof countsByStatus] }}</span>
              </div>
            </template>
          </el-statistic>
        </div>
      </el-card>
      <div v-if="!form.projectId" class="mt-4">
        <el-card header="上线服务排行">
          <div ref="barRankRef" style="height:300px;" />
        </el-card>
      </div>
      <div class="mt-4">
        <el-card>
          <template #header>
            上线热力图（按日期）
            <el-tooltip content="当前筛选条件下，每天的总计上线次数" placement="right">
              <div class="i-solar-info-circle-bold inline-block cursor-pointer" />
            </el-tooltip>
          </template>
          <div ref="heatmapDateRef" style="height:300px;" />
        </el-card>
      </div>
      <div class="mt-4">
        <el-card>
          <template #header>
            上线热力图（按时段）
            <el-tooltip content="当前筛选条件下，各个时段的总计上线次数" placement="right">
              <div class="i-solar-info-circle-bold inline-block cursor-pointer" />
            </el-tooltip>
          </template>
          <div ref="heatmapHourRef" style="height:300px;" />
        </el-card>
      </div>
    </template>
  </div>
</template>
