<script setup lang="ts">
const router = useRouter()

type ButtonType = '' | 'text' | 'success' | 'primary' | 'warning' | 'default' | 'info' | 'danger'

const links: Array<{
  title: string
  children: Array<{
    buttonText: string
    buttonType: ButtonType
    plain: boolean
    icon: string
    url: string
  }>
}> = [
  {
    title: '服务构建与上线',
    children: [
      {
        buttonText: '上线管理',
        buttonType: 'success',
        plain: false,
        icon: 'i-solar-flag-linear',
        url: '/release',
      },
      {
        buttonText: '构建部署',
        buttonType: 'primary',
        plain: false,
        icon: 'i-famicons-git-network-outline',
        url: '/build',
      },
      {
        buttonText: '项目列表',
        buttonType: 'warning',
        plain: false,
        icon: 'i-material-symbols-format-list-bulleted-rounded',
        url: '/projects',
      },
    ],
  },
  {
    title: '快速通道',
    children: [
      {
        buttonText: 'CDN管理',
        buttonType: 'success',
        plain: true,
        icon: 'i-hugeicons-file-upload',
        url: 'http://10.8.8.153:8015/',
      },
      {
        buttonText: 'UI组件库',
        buttonType: 'primary',
        plain: true,
        icon: 'i-hugeicons-biscuit',
        url: 'http://onion-ui.yc345.tv',
      },
      {
        buttonText: 'YCAC洋葱周刊',
        buttonType: 'warning',
        plain: true,
        icon: 'i-hugeicons-book-bookmark-02',
        url: '/ycAc/article',
      },
      {
        buttonText: '页面性能检测',
        buttonType: 'success',
        plain: true,
        icon: 'i-hugeicons-dashboard-speed-01',
        url: 'https://7to12-test.yangcong345.com/hawkeye',
      },
      {
        buttonText: '长期技术项',
        buttonType: 'primary',
        plain: true,
        icon: 'i-hugeicons-text-check',
        url: 'https://guanghe.feishu.cn/base/OGDMbDTlGaOA3wscSLJckedTnse?table=tblTxwpJekRJBH9r&view=vewtR5FcaO',
      },
    ],
  },
  {
    title: '前端需求工单',
    children: [
      {
        buttonText: '基础建设',
        buttonType: 'primary',
        plain: true,
        icon: 'i-hugeicons-building-02',
        url: 'https://guanghe.feishu.cn/share/base/form/shrcno31LDRhilrQIFX57kD2cZg',
      },
      {
        buttonText: '性能优化',
        buttonType: 'warning',
        plain: true,
        icon: 'i-hugeicons-cpu',
        url: 'https://guanghe.feishu.cn/share/base/form/shrcn6mjwNn0oO9efI8VEtGRGJb',
      },
      {
        buttonText: '工程效率',
        buttonType: 'success',
        plain: true,
        icon: 'i-hugeicons-dashboard-speed-02',
        url: 'https://guanghe.feishu.cn/share/base/form/shrcn0VY5Nyw0YJfXyTgLdckzkg',
      },
    ],
  },
]

function handleRoute(url: string) {
  if (url.startsWith('http')) {
    window.open(url)
  }
  else {
    router.push(url)
  }
}
</script>

<template>
  <Welcome />
  <div class="mt-5">
    <div class="mt-5">
      <div v-for="link in links" :key="link.title">
        <h4 class="mb-3 font-bold">
          {{ link.title }}
        </h4>
        <div>
          <el-button
            v-for="child in link.children"
            :key="child.buttonText"
            class="mr-5 w-40"
            :type="child.buttonType"
            :plain="child.plain"
            @click="handleRoute(child.url)"
          >
            <i :class="child.icon" />
            <span class="ml-2">{{ child.buttonText }}</span>
          </el-button>
        </div>
        <el-divider />
      </div>
    </div>
  </div>
</template>

<route lang="yaml">
  meta:
    menu:
      name: 首页
      sort: 0
      path: /
      icon: i-hugeicons-home-09
</route>
