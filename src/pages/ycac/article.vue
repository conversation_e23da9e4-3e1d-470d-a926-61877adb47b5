<script setup lang="ts">
import type { ArticleType } from '~/pages/ycac/service'
import Card from '~/pages/ycac/components/Card.vue'
import { getArticlesApi } from '~/pages/ycac/service'

const list = ref<ArticleType[]>([])
getArticlesApi().then((res) => {
  if (res) {
    list.value = res
  }
})
</script>

<template>
  <div>
    <h2>洋葱周刊-所有文章</h2>
    <div class="contain">
      <el-col v-for="(item, index) in list" :key="index">
        <Card :article="item" />
      </el-col>
    </div>
  </div>
</template>

<style scoped>
.contain {
  margin: 40px auto;
  column-count: 4;
  column-gap: 10px;
}
</style>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: 洋葱周刊
      path: /ycAc
      icon: i-mage-calendar-2
      sort: 4
    name: 所有文章
    sort: 2
    path: /ycAc/article
</route>
