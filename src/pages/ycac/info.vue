<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'
import { getWeekAcInfoApi, previewMessageApi, updateWeekAcInfoApi } from '~/pages/ycac/service'

dayjs.extend(isoWeek)

// 表单ref声明
const formRef = ref<FormInstance>()

const form = reactive<{
  versions: string | null
  docs: string | null
  message: string | null
  email: string
}>({
  versions: '',
  docs: '',
  message: '',
  email: '',
})

const time = ref('')
const id = ref<null | number>(null)
const end = ref(false)
function getWeekAcInfo() {
  getWeekAcInfoApi().then((res) => {
    if (res) {
      time.value = res.time
      id.value = res.id
      form.versions = res.versions
      form.docs = res.docs
      form.message = res.message
    }
  }).catch((error) => {
    console.error('获取周刊信息失败:', error)
    ElMessage.error('获取周刊信息失败，请刷新页面重试')
  })
}

onMounted(() => {
  const hour = dayjs().format('HH')
  const weekday = dayjs().isoWeekday()
  end.value = weekday > 5 || (weekday === 5 && Number(hour) > 18)
  getWeekAcInfo()
})

function preview() {
  if (!form.email) {
    ElMessage.warning('请填写邮箱')
    return
  }
  const data = {
    docs: form.docs,
    versions: form.versions,
    message: form.message,
    email: form.email,
  }
  previewMessageApi(data).then(() => {
    ElMessage.success('已发送飞书消息')
  }).catch((error) => {
    console.error('发送飞书消息失败:', error)
    ElMessage.error('发送飞书消息失败，请检查邮箱格式或网络连接')
  })
}

function onSubmit() {
  const data = {
    id: id.value,
    docs: form.docs,
    versions: form.versions,
    message: form.message,
  }
  updateWeekAcInfoApi(data).then(() => {
    ElMessage.success('已提交')
  }).catch((error) => {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请检查网络连接或稍后重试')
  })
}
</script>

<template>
  <div>
    <h2>洋葱周刊-本周ac收集</h2>
    <div class="m-100px">
      <el-card class="box-card">
        <template #header>
          <div class="clearfix">
            <span v-if="!end">本期ycAc发布时间 {{ time }}</span>
            <span v-else> 周五 19：00 - 周末 暂停收集 </span>
            <span style="float: right; padding: 3px 0; color: grey; font-size: 12px">
              填写时间 周一 至 周五 19:00
            </span>
          </div>
        </template>
        <el-form ref="formRef" :model="form" label-width="120px">
          <el-form-item label="版本升级">
            <el-input v-model="form.versions" />
          </el-form-item>
          <el-form-item label="本周文档">
            <el-input v-model="form.docs" :placeholder="`${JSON.stringify([{ name: 'xx', link: 'xx' }])}`" />
            <p>自行按照JSON数组字符串形式填写</p>
          </el-form-item>
          <el-form-item label="消息窗">
            <el-input v-model="form.message" type="textarea" />
          </el-form-item>
          <el-form-item>
            <div class="w-100% flex items-center justify-between">
              <el-button :disabled="end" type="primary" @click="onSubmit">
                确 定
              </el-button>

              <div style="width: 300px">
                <el-input v-model="form.email" placeholder="输入光合邮箱，例**************" />
                <div style="height: 10px" />
                <el-button @click="preview">
                  发送飞书预览
                </el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: 洋葱周刊
      path: /ycAc
      icon: i-mage-calendar-2
      sort: 4
    name: 本周ac收集
    sort: 3
    path: /ycAc/info
</route>
