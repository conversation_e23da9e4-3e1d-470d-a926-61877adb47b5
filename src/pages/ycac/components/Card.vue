<script setup lang="ts">
import type { ArticleType } from '~/pages/ycac/service'

defineProps<{
  article: ArticleType
}>()

function handleLink(article: ArticleType) {
  if (article.url) {
    window.open(article.url, '_blank')
  }
}
</script>

<template>
  <el-card class="mx-10px my-20px" shadow="hover">
    <template #header>
      <h4>{{ article.title }}</h4>
      <el-button class="mb-20px" style="float: right" link type="primary" @click="handleLink(article)">
        查看文章
      </el-button>
    </template>
    <div class="mb-18px text-14px leading-30px">
      {{ `文章推荐 ${article.desc}` }}
    </div>
  </el-card>
</template>
