<script lang="ts" setup>
import type { WeeklyDetailType } from '~/pages/ycac/service'

const props = defineProps<{
  detail: WeeklyDetailType
}>()

const docsList = computed(() => {
  if (props.detail?.docs) {
    try {
      return JSON.parse(props.detail?.docs)
    }
    catch {
      return []
    }
  }
  return []
})
</script>

<template>
  <el-card class="mx-10px mb-20px" shadow="hover">
    <template #header>
      <div>
        <span>第{{ detail.id }}期</span>
        <span style="float: right"> <i class="el-icon-time" /> {{ detail.time }}</span>
      </div>
    </template>
    <div v-for="item in detail.articlesData" :key="item.id" class="mb-18px">
      <b>
        {{ `《${item.title}》` }}
      </b>
      <br>
      <el-link :href="item.url" type="primary" target="_blank">
        查看文章
      </el-link>
    </div>
    <template v-if="detail.question">
      <h4>YC 问答</h4>
      <p><b>Q</b>{{ detail.question }}</p>
      <p><b>A</b>{{ detail.answer }}</p>
    </template>
    <template v-if="detail.message">
      <h4><b>🎺</b> 消息窗</h4>
      <p>{{ detail.message }}</p>
    </template>
    <template v-if="detail.versions">
      <h4><b>🚀</b> 版本升级</h4>
      <p>{{ detail.versions }}</p>
    </template>
    <template v-if="detail.docs && docsList.length">
      <h4><b>📖</b> 本周文档</h4>
      <el-link v-for="doc in docsList" :key="doc.link" type="primary" target="_blank" :href="doc.link">
        {{ doc.name }}
      </el-link>
    </template>
  </el-card>
</template>
