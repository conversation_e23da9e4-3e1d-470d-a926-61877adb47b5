import axios from '~/helpers/request'

// ycac后端服务
const IP = 'https://fe.yc345.tv/ac/ac'
// const IP = 'http://***********:7001/ac'
// const IP = 'http://127.0.0.1:7001/ac'

export interface ArticleType {
  id: number
  url: string
  desc: string | null
  title: string
  category: number
  status: number
  count: number
  create_time: string
}

// 获取全部文章列表
export async function getArticlesApi(): Promise<ArticleType[]> {
  return axios.get(`${IP}/list`)
}

export interface WeeklyDetailType {
  id: number
  time: string
  question: string | null
  articles: string // 根据数据实际类型保持为string
  answer: string | null
  message: string
  banner: string
  articlesData: ArticleType[]
  versions: string
  docs: string | null
}

// 获取周刊列表
export async function getWeeklyApi(): Promise<WeeklyDetailType[]> {
  return axios.get(`${IP}/sendedWeekly`)
}

// 最新本周收集的消息窗OR问答信息
export interface HornType {
  answer: string | null
  docs: string | null
  id: number | null
  message: string | null
  question: string | null
  time: string
  versions: string | null
}
export async function getWeekAcInfoApi(): Promise<HornType> {
  return axios.get(`${IP}/horn`)
}

export async function updateWeekAcInfoApi(data: {
  id: number | null
  docs: string | null
  versions: string | null
  message: string | null
}): Promise<WeeklyDetailType[]> {
  return axios.post(`${IP}/horn`, data)
}

// 消息预览
export async function previewMessageApi(data: {
  email: string
  docs: string | null
  versions: string | null
  message: string | null
}): Promise<WeeklyDetailType[]> {
  return axios.post(`${IP}/previewMessage`, { data })
}
