<script setup lang="ts">
import type { WeeklyDetailType } from '~/pages/ycac/service'
import WeeklyCard from '~/pages/ycac/components/WeeklyCard.vue'
import { getWeeklyApi } from '~/pages/ycac/service'

const list = ref<WeeklyDetailType[]>([])
getWeeklyApi().then((res) => {
  if (res) {
    list.value = res
  }
})
</script>

<template>
  <div>
    <h2>洋葱周刊-过往周刊内容</h2>
    <div class="contain">
      <el-col v-for="(item, index) in list" :key="index">
        <WeeklyCard :detail="item" />
      </el-col>
    </div>
  </div>
</template>

<style scoped>
.contain {
  margin: 40px auto;
  column-count: 3;
  column-gap: 10px;
}
</style>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: 洋葱周刊
      path: /ycAc
      icon: i-mage-calendar-2
      sort: 4
    name: 过往周刊内容
    sort: 1
    path: /ycAc/weekly
</route>
