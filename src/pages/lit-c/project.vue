<script setup lang="ts">
import type { LitProjectType } from './service'
import { addLitProjectApi, getAllLitProjectApi, removeLitProjectApi, updateLitProjectApi } from './service'

const tableData = ref<LitProjectType[]>([])

function getTableData() {
  getAllLitProjectApi().then((res) => {
    tableData.value = res.map((item: LitProjectType) => {
      return {
        ...item,
        status: Number(item.isused) ? '统计中' : '不统计',
      }
    })
  })
}

getTableData()

// 新增项目弹窗
const dialogVisible = ref(false)
const form = reactive<{
  name: string
  git: string
  manage: string
}>({
  name: '',
  git: '',
  manage: '',
})
// 重置form
function resetForm() {
  form.git = ''
  form.name = ''
  form.manage = ''
}
function handleAddNewProject() {
  resetForm()
  dialogVisible.value = true
}
function addProject() {
  if (!form.name || !form.git || !form.manage) {
    ElMessage({
      message: '名称、地址、业务线不得为空',
      type: 'warning',
    })
    return
  }
  addLitProjectApi({
    ...form,
    isused: '1',
  }).then(() => {
    resetForm()
    getTableData()
    dialogVisible.value = false
    ElMessage({
      message: '新增成功',
      type: 'success',
    })
  })
}
function closeAddProject() {
  dialogVisible.value = false
  resetForm()
}

// 点击删除
async function handleDelete(id: string) {
  // 二次确认
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    removeLitProjectApi(id).then(() => {
      getTableData()
    })
  })
}

// 打开或关闭统计状态
async function handleStatus(id: string, status: string) {
  updateLitProjectApi({ id, isused: status === '1' ? '0' : '1' }).then(() => {
    getTableData()
  })
}
</script>

<template>
  <div>
    <h2>
      项目管理
    </h2>
    <div class="my-40px">
      <div class="mb-40px flex items-center justify-between">
        <el-button @click="handleAddNewProject">
          新增项目
        </el-button>
      </div>
      <section>
        <el-table :data="tableData" stripe style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="项目" width="180" />
          <el-table-column prop="git" label="地址" width="420" />
          <el-table-column prop="status" label="状态" />
          <el-table-column prop="manage" label="分组" />
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleDelete(scope.row.id)">
                删除
              </el-button>
              <el-button
                size="small"
                :type="`${Number(scope.row.isused) === 0 ? 'success' : 'warning'}`"
                @click="handleStatus(scope.row.id, scope.row.isused)"
              >
                {{ Number(scope.row.isused) ? '关闭' : '打开' }}统计
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </div>

    <el-dialog v-model="dialogVisible" title="新增项目" width="50%">
      <el-form :model="form" label-width="120px">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="项目GIT地址" prop="git">
          <el-input v-model="form.git" placeholder="https://gitlab.yc345.tv/frontend/xxxx.git" />
        </el-form-item>
        <el-form-item label="业务线" prop="manage">
          <el-radio-group v-model="form.manage">
            <el-radio value="学习工具">
              学习工具
            </el-radio>
            <el-radio value="商业化">
              商业化
            </el-radio>
            <el-radio value="平台">
              平台
            </el-radio>
            <el-radio value="入校">
              入校
            </el-radio>
            <el-radio value="电销">
              电销
            </el-radio>
            <el-radio value="智能硬件">
              智能硬件
            </el-radio>
            <el-radio value="技术支撑">
              技术支撑
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex items-center justify-end">
          <el-button @click="closeAddProject">
            取 消
          </el-button>
          <el-button type="primary" @click="addProject">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: lit-c包统计
      path: /lit-c
      icon: i-material-symbols-ssid-chart
      sort: 5
    name: 项目管理
    sort: 2
    path: /lit-c/project
</route>
