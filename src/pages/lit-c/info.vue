<script setup lang="ts">
import type { TopItemType, WeekAllLibraryItemType, WeekDataByLibraryType } from './service'
import { getLibrary<PERSON>pi, getProjectByLibraryApi, getTopByLibraryApi } from './service'

const tableData = ref<WeekAllLibraryItemType[]>([])

function getTableData() {
  getLibraryApi().then((res) => {
    tableData.value = res
  })
}

getTableData()

// 查看详情
const dialogVisible = ref(false)
const detailData = ref<WeekDataByLibraryType[]>([])
async function getProjectByLibrary(library: string) {
  getProjectByLibraryApi(library).then((res) => {
    // 同项目合并 版本
    const projects: Record<string, string[]> = {}
    res.forEach(({ project, version }) => {
      if (!projects[project]) {
        projects[project] = []
      }
      projects[project].push(version!)
    })
    detailData.value = Object.entries(projects).map(([project, versions]) => ({
      project,
      versions,
    }))
  })
}
const formatDetailData = computed(() => {
  return detailData.value.map((item, index) => {
    return {
      project: item.project,
      version: item?.versions?.join('、'),
      index: index + 1,
    }
  })
})
async function seeMore(library: string) {
  detailData.value = []
  await getProjectByLibrary(library)
  dialogVisible.value = true
}

// 查看Top5
const dialogTopVisible = ref(false)
const detailTopData = ref<TopItemType[]>([])
function seeTop(library: string) {
  detailTopData.value = []
  getTopByLibraryApi(library).then((res) => {
    detailTopData.value = res
    dialogTopVisible.value = true
  })
}
</script>

<template>
  <div>
    <h2>
      包统计数据
    </h2>
    <div class="mt-[40px]">
      <div class="mb-[40px] flex items-center justify-between">
        <h5 class="text-[12px]">
          🎺 : 次数指文件引用次数，如每单一文件中引入次数为1，非单一文件内使用次数
        </h5>
        <el-link
          type="success"
          target="_blank"
          href="https://grafana-test.yc345.tv/d/cd118cb5-6a6c-462a-9cec-c4e9701cd534/5YmN56uv5o-S5Lu2?orgId=1&var-library=@guanghe-pub%2Fonion-utils&var-component=Button&var-project=teacherh5&from=now-7d&to=now"
        >
          grafana表盘
        </el-link>
      </div>
      <section>
        <el-table :data="tableData">
          <el-table-column type="index" label="序号" width="100" />
          <el-table-column prop="library" label="npm包名" width="320" />
          <el-table-column prop="count" label="使用项目个数" />
          <el-table-column prop="pageCount" label="使用页面个数" />
          <el-table-column prop="desc" label="备注" />
          <el-table-column fixed="right" label="操作">
            <template #default="scope">
              <el-button style="margin-left: 10px;" link type="primary" @click="seeMore(scope.row.library)">
                查看详情
              </el-button>
              <el-button style="margin-left: 10px;" link type="primary" @click="seeTop(scope.row.library)">
                组件使用TOP5
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </div>
    <el-dialog v-model="dialogVisible" :title="`使用详情：${detailData.length}个项目`">
      <el-table
        max-height="400"
        :data="formatDetailData"
      >
        <el-table-column
          prop="index"
          label="序号"
          width="100"
        />
        <el-table-column
          prop="project"
          label="项目"
        />
        <el-table-column
          prop="version"
          label="版本号"
        />
      </el-table>
    </el-dialog>
    <el-dialog v-model="dialogTopVisible" title="组件使用TOP5">
      <el-table max-height="400" :data="detailTopData">
        <el-table-column type="index" label="序号" width="100" />
        <el-table-column prop="component" label="组件" />
        <el-table-column prop="count" label="使用数量" />
      </el-table>
    </el-dialog>
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: lit-c包统计
      path: /lit-c
      icon: i-material-symbols-ssid-chart
      sort: 5
    name: 包统计数据
    sort: 1
    path: /lit-c/info
</route>
