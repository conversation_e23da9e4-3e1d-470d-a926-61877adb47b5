import axios from '~/helpers/request'

// 后端服务
const IP = 'https://fe.yc345.tv/ac/litc'
// const IP = 'http://127.0.0.1:7001/litc'

// 获取包统计列表
export interface WeekAllLibraryItemType {
  count: number
  desc: string
  library: string
  pageCount: number
}
export async function getLibraryApi(): Promise<WeekAllLibraryItemType[]> {
  return axios.get(`${IP}/week_allLibrary`)
}
// 包统计查看详情
export interface WeekDataByLibraryType {
  project: string
  version?: string
  versions?: string[]
  index?: number
}
export async function getProjectByLibraryApi(library: string): Promise<WeekDataByLibraryType[]> {
  return axios.get(`${IP}/week_dataByLibrary?library=${library}`)
}
// 组件使用Top5
export interface TopItemType {
  component: string
  count: number
}
export async function getTopByLibraryApi(library: string): Promise<TopItemType[]> {
  return axios.get(`${IP}/week_getTopByLibrary?library=${library}`)
}
// 项目管理列表
export interface LitProjectType {
  id: string
  name: string
  isused: string
  git: string
  manage: string
  errorMsg?: string | null
  status?: string
}
export async function getAllLitProjectApi(): Promise<LitProjectType[]> {
  return axios.get(`${IP}/allLitProject`)
}
export async function removeLitProjectApi(id: string) {
  return axios.post(`${IP}/removeLitProject`, { data: id })
}
// lit-c 项目管理-新增项目
export async function addLitProjectApi(data: {
  name: string
  git: string
  manage: string
  isused: string
}) {
  return axios.post(`${IP}/addLitProject`, { data })
}
// lit-c 项目管理-更新状态
export async function updateLitProjectApi(data: {
  id: string
  isused: string
}) {
  return axios.post(`${IP}/updateLitProject`, { data })
}
// lit-c 包管理
export interface LitNpmType {
  id: string
  name: string
  desc: string
}
export async function getAllLitNpmsApi(): Promise<LitNpmType[]> {
  return axios.get(`${IP}/allLitNpms`)
}
// lit-c 包管理 新增
export async function addLitNpmsApi(data: {
  name: string
  desc?: string
}) {
  return axios.post(`${IP}/addLitNpms`, { data })
}
// lit-c 包管理 删除
export async function removeLitNpmApi(id: string) {
  const { data: response } = await axios.post(`${IP}/removeLitNpm`, { data: id })
  return response
}
