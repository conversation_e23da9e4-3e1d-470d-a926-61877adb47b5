<script setup lang="ts">
import type { LitNpmType } from './service'
import { addLitNpmsApi, getAllLitNpmsApi, removeLitNpmApi } from './service'

const tableData = ref<LitNpmType[]>([])

function getTableData() {
  getAllLitNpmsApi().then((res) => {
    tableData.value = res
  })
}

getTableData()

// 新增项目弹窗
const dialogVisible = ref(false)
const form = reactive<{
  name: string
  desc: string
}>({
  name: '',
  desc: '',
})
// 重置form
function resetForm() {
  form.name = ''
  form.desc = ''
}
function handleAddNewNpm() {
  resetForm()
  dialogVisible.value = true
}
function addNpm() {
  if (!form.name) {
    ElMessage({
      message: '名称不得为空',
      type: 'warning',
    })
    return
  }
  addLitNpmsApi({
    ...form,
  }).then(() => {
    resetForm()
    getTableData()
    dialogVisible.value = false
    ElMessage({
      message: '新增成功',
      type: 'success',
    })
  })
}
function closeAddNpm() {
  dialogVisible.value = false
  resetForm()
}

// 点击删除
async function handleDelete(id: string) {
  // 二次确认
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    removeLitNpmApi(id).then(() => {
      getTableData()
    })
  })
}
</script>

<template>
  <div>
    <h2>
      包管理
    </h2>
    <div class="my-40px">
      <div class="mb-40px flex items-center justify-between">
        <el-button @click="handleAddNewNpm">
          新增收集包
        </el-button>
      </div>
      <section>
        <el-table :data="tableData" stripe style="width: 100%">
          <el-table-column prop="name" label="项目" />
          <el-table-column prop="desc" label="备注" />
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleDelete(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </section>
    </div>

    <el-dialog v-model="dialogVisible" title="新增包" width="50%">
      <el-form :model="form" label-width="120px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="@guanghe-pub/onion-ui" />
        </el-form-item>
        <el-form-item label="备注" prop="desc">
          <el-input v-model="form.desc" placeholder="组件库" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex items-center justify-end">
          <el-button @click="closeAddNpm">
            取 消
          </el-button>
          <el-button type="primary" @click="addNpm">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: lit-c包统计
      path: /lit-c
      icon: i-material-symbols-ssid-chart
      sort: 5
    name: 包管理
    sort: 3
    path: /lit-c/npms
</route>
