interface SearchParam {
  monitorName: keyof typeof monitorMap
  sql: string
}

const monitorMap = {
  performance: 'f3069b7e-1e5d-4b5e-a7e9-4fb6873bacc5',
  abnormal: 'e331de27-c37d-402c-b7f0-4fbfa1e2eba4',
  api: '464e6207-8959-4dde-b033-6660138f4e7d',
  app: 'bd5f39fb-7e5e-4ce9-85e8-c4e0279bc9f6',
  loki: '1e0940dd-b66f-449a-b656-466604217f29',
} as const

export function openGrafana(projectName: string, pathname?: string): void {
  if (!projectName) {
    return
  }
  const paramsPathname = pathname ? `&var-pathname=${pathname}` : ''
  // 示例url https://grafana.yc345.tv/d/d33bab16-0ba1-4dc3-9032-6b7f0ad5ac65/5YmN56uv5pel5b-X55uR5o6n?orgId=1&var-appKey=study-app&var-os=All&var-pathname=/study-app/community&var-interv=7d&from=now-30d&to=now
  const url = `https://grafana.yc345.tv/d/d33bab16-0ba1-4dc3-9032-6b7f0ad5ac65/5YmN56uv5pel5b-X55uR5o6n?orgId=1&var-appKey=${projectName}&var-os=All${paramsPathname}&var-interv=7d&from=now-30d&to=now`
  window.open(url, '_blank')
}

function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 更现代的Unicode处理方式
function encodeBase64Url(str: string): string {
  const bytes = new TextEncoder().encode(str)
  const base64 = btoa(String.fromCharCode(...bytes))
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
}

// 火山云检索分析页面
export function openVolcengine(searchParam: SearchParam): void {
  const topicId = monitorMap[searchParam.monitorName]
  // 自定义时间范围：格式为 ${startTime},${endTime}，其中 startTime 和 endTime 的格式为 YYYY-MM-DD HH:mm:ss.SSS
  const endTime = new Date()
  const startTime = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000)
  const time = `${formatDate(startTime)},${formatDate(endTime)}`
  const codebase64 = encodeBase64Url(searchParam.sql)
  const url = `https://console.volcengine.com/tls/region:tls+cn-beijing/project/4813fbae-baef-441f-acf8-51093bcf3c57/search?topicId=${topicId}&time=${time}&codebase64=${codebase64}`
  window.open(url, '_blank')
}
