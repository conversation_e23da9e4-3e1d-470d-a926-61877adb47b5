/**
 * 资源错误率，1%
  白屏，1%
  js错误，10%
  接口错误，0.05%
  大图监控，1个，只有端内（结合PV）
  内存监控，300M，只有端内
  Loki，244K
 */

export type MonitorType = keyof typeof Vitals

export const Vitals = {
  resourceError: 1,
  blankScreen: 1,
  jsError: 10,
  apiError: 0.05,
  bigImage: 1,
  memory: 300,
  Loki: 244,
  CLS: 0.1,
  LCP: 2500,
  INP: 200,
} as const

/**
 * @param monitorType 监控类型
 * @param size 监控值
 * @returns 是否符合定义的指标
 */
export function isOverSize(monitorType: MonitorType, size: number): boolean {
  return size > Vitals[monitorType]
}

export function getOverSizeString(monitorType: MonitorType, size: number): string {
  if (isOverSize(monitorType, size)) {
    return `<span style="color: red; font-weight: bold;">${size}<span>`
  }
  return size.toString()
}
