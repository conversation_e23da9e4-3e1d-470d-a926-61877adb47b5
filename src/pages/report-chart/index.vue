<script setup lang="ts">
import type { MonitorName, TlsLogItemType } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'
import HawkEye from './components/hawkeye/hawkeye.vue'
import monitorJsError from './components/monitor-abnormal/jsErrorTop10.vue'
import monitorResourceError from './components/monitor-abnormal/resourceErrorTop10.vue'
import monitorImg from './components/monitor-app/bigImg.vue'
import pageInfo from './components/monitor-performance/pageInfo.vue'
import AppKeyEntry from './components/projectEntry/appKey.vue'
import PathnameEntry from './components/projectEntry/pathname.vue'
import ProjectInfo from './components/projectInfo/index.vue'
import Loki from './components/projectInfo/loki.vue'
import pagesBadFCP from './components/projectInfo/pagesBadFCP.vue'
import pagesBlankScreen from './components/projectInfo/pagesBlankScreen.vue'
import pagesJsError from './components/projectInfo/pagesJsError.vue'
import pagesResourceError from './components/projectInfo/pagesResourceError.vue'

interface AppKeySql {
  monitorName: MonitorName
  sql: string
}

const appKey = ref('study-app')
const pathname = ref('/study-app/home')
const appKeyList = ref<TlsLogItemType[]>([])
const appKeySql = ref<AppKeySql>({
  monitorName: 'performance',
  sql: `env:prod | SELECT appKey, pathname, count(*) as pv, count(DISTINCT(userId)) as uv GROUP by appKey, pathname  HAVING uv > 5 order by uv desc  limit 10000`,
})

const pathInfo = computed(() => {
  return appKeyList.value.find(item => item.pathname === pathname.value) || {
    pv: 0,
    uv: 0,
  }
})

onMounted(() => {
  getTlsLogsApi(appKeySql.value).then((res) => {
    appKeyList.value = res
  })
})

function handleAppKeyChange(newAppKey: string) {
  pathname.value = ''
  if (!newAppKey)
    return
  appKey.value = newAppKey
}

function handlePathnameChange(newPathname: string) {
  if (!newPathname)
    return
  pathname.value = newPathname
}
</script>

<template>
  <div class="app-monitor p-20px">
    <!-- ui模版参考：https://dashboardpack.com/live-demo-preview/?livedemo=378782 -->
    <div class="white-box mb-30">
      <el-row class="flex" :gutter="20">
        <el-col class="h-40px" :span="8">
          <AppKeyEntry :project-name="appKey" @app-key-change="handleAppKeyChange" />
        </el-col>
        <el-col class="h-40px" :span="16">
          <PathnameEntry :project-name="appKey" :pathname="pathname" @pathname-change="handlePathnameChange" />
        </el-col>
      </el-row>
    </div>
    <ProjectInfo :project-name="appKey" />
    <Loki :project-name="appKey" />
    <pagesBadFCP :project-name="appKey" />
    <div class="white-box flex">
      <pagesJsError :project-name="appKey" />
      <pagesResourceError :project-name="appKey" />
      <pagesBlankScreen :project-name="appKey" />
    </div>
    <h1 class="my-20px text-20px font-600">
      页面信息
    </h1>
    <pageInfo :pathname="pathname" :uv="Number(pathInfo.uv)" :pv="Number(pathInfo.pv)" />
    <div class="white-box inline-tables">
      <monitorImg :pathname="pathname" />
      <monitorJsError :pathname="pathname" />
      <monitorResourceError :pathname="pathname" />
    </div>
    <HawkEye :pathname="pathname" />
  </div>
</template>

<style lang="scss">
@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@400;600&display=swap');
.app-monitor {
  padding-bottom: 50px;
  background-color: #f5f7f9;
  font-family: 'Barlow', sans-serif;
  font-size: 15px;
  .mb-30 {
    margin-bottom: 30px;
  }
  .el-input--suffix .el-input__inner {
    font-family: 'Barlow', sans-serif;
    font-size: 15px;
  }
  .el-select-dropdown__item {
    font-size: 15px;
  }
  .el-form-item__label {
    font-weight: 600;
    font-size: 20px;
    color: #2d1967;
    text-transform: capitalize;
  }
  .white-box {
    padding: 30px;
    background-color: #ffffff;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
    .main-title {
      font-weight: 600;
      font-size: 20px;
      color: #2d1967;
      line-height: 1.2;
      text-transform: capitalize;
      padding-bottom: 20px;
    }
  }
  .single-list-counter {
    border-bottom: 1px solid #f1f3f5;
    padding: 35px 0 26px 57px;
    h3 {
      font-size: 30px;
      font-weight: 700;
      margin-bottom: 0px;
      color: #4567ee !important;
      line-height: 16px;
      margin-top: 0;
      text-transform: uppercase;
    }
    p {
      font-size: 17px;
      color: #6f658d;
      margin-bottom: 0;
      margin-top: 0;
      padding-top: 15px;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      .el-icon-info::before {
        color: #6f658d;
        margin-left: 6px;
        cursor: pointer;
      }
    }
  }
  .monitor-el-table {
    .el-table__header-wrapper {
      background: #f2f9ff;
      border-radius: 15px;
      overflow: hidden;
    }
    th.el-table__cell {
      background: #f2f9ff;
      padding: 17px 30px;
      line-height: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #2f90f7;
      white-space: nowrap;
      text-transform: capitalize;
    }
    th.el-table__cell.is-leaf {
      border: none;
    }
    .el-table__row {
      &:hover {
        td.el-table__cell {
          background: none !important;
        }
      }
    }
  }
  .inline-tables {
    display: flex;
  }
}
</style>

<route lang="yaml">
meta:
  menu:
    name: 体检表
    sort: 7
    path: /report-chart
    icon: i-codicon-debug-console
</route>
