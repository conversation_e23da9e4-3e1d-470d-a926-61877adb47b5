<script setup lang="ts">
import type { TlsLogItemType } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'
import { openVolcengine } from '~/pages/report-chart/utils'

interface Props {
  pathname: string
}

const props = defineProps<Props>()

const imgList = ref<TlsLogItemType[]>([])

const sql = computed(() =>
  `pathname:${props.pathname} AND env: prod AND errorType: jsError | select msg, count(*) as c group by msg order by c desc limit 10`,
)

watch(() => props.pathname, async (newVal) => {
  imgList.value = []
  if (newVal === '')
    return
  await fetchLogs()
}, { immediate: true })

async function fetchLogs() {
  imgList.value = await getTlsLogsApi({
    monitorName: 'abnormal',
    sql: sql.value,
  })
}
</script>

<template>
  <div style="flex: 1;margin: 0 16px">
    <div class="main-title">
      页面JS错误
      <el-button
        type="primary"
        size="small"
        @click.stop="openVolcengine({
          monitorName: 'abnormal',
          sql,
        })"
      >
        火山云
      </el-button>
    </div>
    <el-table class="monitor-el-table" :data="imgList" style="width: 100%" height="400">
      <el-table-column prop="msg" label="ErrorMsg" />
      <el-table-column prop="c" label="ErrorCount" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click.stop="openVolcengine({
              monitorName: 'abnormal',
              sql: `pathname:${pathname} AND errorType: jsError AND msg: '${row.msg}'`,
            })"
          >
            火山云
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
