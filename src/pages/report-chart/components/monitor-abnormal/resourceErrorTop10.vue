<script setup lang="ts">
import type { MonitorName, TlsLogItemType } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'
import { openVolcengine } from '~/pages/report-chart/utils'

interface Props {
  pathname: string
}

const props = defineProps<Props>()

const tableList = ref<TlsLogItemType[]>([])

const sql = computed(() => ({
  monitorName: 'abnormal' as MonitorName,
  sql: `pathname:${props.pathname} AND env: prod AND errorType: resourceError | select resourceUrl, count(*) as c group by resourceUrl order by c desc limit 10`,
}))

watch(() => props.pathname, async (newVal) => {
  if (!newVal)
    return
  await getResourceErrorLogs()
}, { immediate: true })

async function getResourceErrorLogs() {
  const res = await getTlsLogsApi(sql.value)
  if (res.length === 0)
    return
  tableList.value = res
}
</script>

<template>
  <div style="flex: 1">
    <div class="main-title">
      页面资源错误
      <el-button
        type="primary"
        size="small"
        @click.stop="openVolcengine(sql)"
      >
        火山云
      </el-button>
    </div>
    <el-table class="monitor-el-table" :data="tableList" style="width: 100%" height="400">
      <el-table-column prop="resourceUrl" label="resourceUrl" />
      <el-table-column prop="c" label="ErrorCount" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click.stop="openVolcengine({
              monitorName: 'abnormal',
              sql: `pathname:${pathname} AND errorType: resourceError AND resourceUrl: '${row.resourceUrl}'`,
            })"
          >
            火山云
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.page__monitor {
  .white-box {
    padding: 20px;
  }
}
</style>
