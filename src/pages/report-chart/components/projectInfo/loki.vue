<script setup lang="ts">
import type { MonitorName } from '~/apis/monitor'
import * as echarts from 'echarts'
import { computed, nextTick, onBeforeUnmount, ref, watch } from 'vue'
import { getTlsLogsApi } from '~/apis/monitor'

interface Props {
  projectName: string
}

const props = defineProps<Props>()

interface TableItem {
  path: string
  name: string
  fileSize: number
}

const tableList = ref<TableItem[]>([])
const lokiChartRef = ref<HTMLElement | null>(null)
let myChart: echarts.ECharts | null = null

const sql = computed(() => ({
  monitorName: 'loki' as MonitorName,
  sql: `appKey:"${props.projectName}" | SELECT appKey, oversizedChunks ORDER BY __time__ DESC LIMIT 1`,
}))

watch(() => props.projectName, async (newVal) => {
  if (!newVal)
    return
  await getLokiLogs()
  // 确保 DOM 更新完成
  await nextTick(() => {
    echartsInit()
  })
}, { immediate: true })

onBeforeUnmount(() => {
  // 组件销毁时清理图表
  if (myChart) {
    myChart.dispose()
    myChart = null
  }
})

async function getLokiLogs() {
  const res = await getTlsLogsApi(sql.value)
  if (res.length === 0)
    return
  tableList.value = JSON.parse(res[0]?.oversizedChunks || '[]')
}

async function echartsInit() {
  if (myChart) {
    myChart.dispose()
  }
  // 获取 DOM 引用
  const chartDom = lokiChartRef.value
  if (!chartDom) {
    // console.error('图表容器未找到')
    return
  }

  // 初始化图表
  myChart = echarts.init(chartDom)
  const res = await getTlsLogsApi(sql.value)
  function getLevelOption() {
    return [
      {
        itemStyle: {
          borderWidth: 0,
          gapWidth: 5,
        },
      },
      {
        itemStyle: {
          gapWidth: 1,
        },
      },
      {
        colorSaturation: [0.35, 0.8],
        itemStyle: {
          gapWidth: 1,
          borderColorSaturation: 0.6,
        },
      },
    ]
  }
  const formatUtil = echarts.format
  if (res.length === 0)
    return
  const mydata = JSON.parse(res[0]?.oversizedChunks || '[]').map((element: any) => ({
    ...element,
    value: element.fileSize,
  }))
  // console.log('mydata', mydata)
  const option = {
    tooltip: {
      formatter(info: any) {
        // console.log('info', info)
        const value = info.value
        const treePathInfo = info.treePathInfo
        const treePath = []
        for (let i = 1; i < treePathInfo.length; i++) {
          treePath.push(treePathInfo[i].name)
        }
        return [
          `<div class="tooltip-title">${
            formatUtil.encodeHTML(treePath.join('/'))
          }</div>`,
          `Disk Usage: ${formatUtil.addCommas(value)} KB`,
        ].join('')
      },
    },
    series: [
      {
        name: `${props.projectName} FileSize Info`,
        type: 'treemap',
        visibleMin: 300,
        label: {
          show: true,
          formatter: '{b}',
        },
        itemStyle: {
          borderColor: '#fff',
          borderRadius: 6,
        },
        levels: getLevelOption(),
        data: mydata,
        roam: false, // 禁用滚动缩放
      },
    ],
  }
  myChart.setOption(option)
  // 窗口变化时自适应
  window.addEventListener('resize', () => {
    myChart?.resize()
  })
}
</script>

<template>
  <div class="my-30px flex">
    <div class="white-box mr-20px w-600px">
      <div class="main-title">
        {{ projectName }} FileSize Info
      </div>
      <div ref="lokiChartRef" class="h-600px w-600px" />
    </div>
    <div class="white-box" style="flex: 1">
      <el-table class="monitor-el-table" height="580" :data="tableList" style="width: 100%;margin-bottom: 20px">
        <el-table-column prop="path" label="path" />
        <el-table-column prop="name" label="name" />
        <el-table-column prop="fileSize" label="fileSize(kb)" />
      </el-table>
    </div>
  </div>
</template>
