<script setup lang="ts">
import type { MonitorName, TlsLogItemType } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'
import { openVolcengine } from '~/pages/report-chart/utils'

interface Props {
  projectName: string
}

const props = defineProps<Props>()

const tableList = ref<TlsLogItemType[]>([])

const sql = computed(() => ({
  monitorName: 'abnormal' as MonitorName,
  sql: `appKey:"${props.projectName}" AND env: prod AND errorType: resourceError | SELECT pathname, COUNT(*) as errorCount GROUP BY pathname ORDER BY errorCount DESC LIMIT 10`,
}))

watch(() => props.projectName, (newVal) => {
  if (newVal === '')
    return
  getTlsLogs()
}, { immediate: true })

async function getTlsLogs() {
  const res = await getTlsLogsApi(sql.value)
  tableList.value = res || []
}
</script>

<template>
  <div style="flex: 1; margin: 0 16px">
    <div class="main-title">
      资源错误数 Top 10 页面
      <el-button
        type="primary"
        size="small"
        @click.stop="openVolcengine(sql)"
      >
        火山云
      </el-button>
    </div>
    <el-table class="monitor-el-table" :data="tableList" style="width: 100%" height="500">
      <el-table-column prop="pathname" label="pathname" />
      <el-table-column prop="errorCount" label="errorCount" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click.stop="openVolcengine({
              monitorName: 'abnormal',
              sql: `appKey: ${projectName} AND env: prod AND pathname: ${scope.row.pathname} AND errorType: resourceError`,
            })"
          >
            火山云
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
