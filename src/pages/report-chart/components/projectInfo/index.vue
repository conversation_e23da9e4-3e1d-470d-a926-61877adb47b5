<script setup lang="ts">
import type { MonitorName, SearchParam } from '~/apis/monitor'
import * as echarts from 'echarts'
import { getTlsLogsApi } from '~/apis/monitor'

interface Props {
  projectName: string
}

const props = defineProps<Props>()

interface ImageItem {
  pvCount: number
  jsErrorCount: number
  blankCount: number
  resourceErrorCount: number
  jsErrorRate: string
  blankRate: string
  resourceErrorRate: string
  fcp: string
  lcp: string
}

const imgList = ref<ImageItem[]>([])
const pvChartRef = ref<HTMLElement | null>(null)
let myChart: echarts.ECharts | null = null

const pvSql = computed(() => ({
  monitorName: 'performance' as MonitorName,
  sql: `appKey:${props.projectName} AND env: prod | SELECT COUNT(*) AS pv`,
}))

const pvSingleDaySql = computed(() => ({
  monitorName: 'performance' as MonitorName,
  sql: `appKey:${props.projectName} AND env: prod | SELECT COUNT(*) AS pv, DATE_FORMAT(__time__ - (__time__ % 86400000), 'yyyy-MM-dd') AS time GROUP BY time ORDER BY time ASC`,
}))

const jsErrorSql = computed(() => ({
  monitorName: 'abnormal' as MonitorName,
  sql: `appKey:${props.projectName || 'study-app'} AND env: prod AND errorType: jsError | SELECT COUNT(*) AS jsError`,
}))

const blankSql = computed(() => ({
  monitorName: 'abnormal' as MonitorName,
  sql: `appKey:${props.projectName || 'study-app'} AND env: prod AND errorType: blankScreen | SELECT COUNT(*) AS blankScreen`,
}))

const resourceErrorSql = computed(() => ({
  monitorName: 'abnormal' as MonitorName,
  sql: `appKey:${props.projectName || 'study-app'} AND env: prod AND errorType: resourceError | SELECT COUNT(*) AS resourceError`,
}))

const fcpSql = computed(() => ({
  monitorName: 'performance' as MonitorName,
  sql: `appKey:${props.projectName || 'study-app'} AND env: prod | SELECT AVG(FCP) WHERE FCP > 0 AND FCP < 10000`,
}))

const lcpSql = computed(() => ({
  monitorName: 'performance' as MonitorName,
  sql: `appKey:${props.projectName || 'study-app'} AND env: prod | SELECT AVG(LCP) WHERE LCP > 0 AND LCP < 10000`,
}))

async function echartsInit() {
  if (myChart) {
    myChart.dispose()
  }
  // 获取 DOM 引用
  const chartDom = pvChartRef.value
  if (!chartDom) {
    console.error('图表容器未找到')
    return
  }

  // 初始化图表
  myChart = echarts.init(chartDom)
  const res = await getTlsLogsApi(pvSingleDaySql.value as SearchParam)

  const option = {
    color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: res.map(item => item.time),
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [
      {
        name: 'Line 5',
        type: 'line',
        stack: 'Total',
        smooth: false,
        lineStyle: {
          width: 0,
        },
        showSymbol: false,
        label: {
          show: true,
          position: 'top',
        },
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: '#FF4500',
            },
            {
              offset: 1,
              color: '#FFA500',
            },
          ]),
        },
        emphasis: {
          focus: 'series',
        },
        data: res.map(item => item.pv),
      },
    ],
  }

  myChart.setOption(option)
  // 窗口变化时自适应
  window.addEventListener('resize', () => {
    myChart?.resize()
  })
}

async function getTlsLogsAll() {
  const [pv, jsError, blank, resourceError, fcp, lcp] = await Promise.all([
    getTlsLogsApi(pvSql.value as SearchParam),
    getTlsLogsApi(jsErrorSql.value as SearchParam),
    getTlsLogsApi(blankSql.value as SearchParam),
    getTlsLogsApi(resourceErrorSql.value as SearchParam),
    getTlsLogsApi(fcpSql.value as SearchParam),
    getTlsLogsApi(lcpSql.value as SearchParam),
  ])

  imgList.value = [
    {
      pvCount: pv[0].pv,
      jsErrorCount: jsError[0]?.jsError ? Number(jsError[0].jsError) : 0,
      blankCount: blank[0]?.blankScreen ? Number(blank[0].blankScreen) : 0,
      resourceErrorCount: resourceError[0]?.resourceError ? Number(resourceError[0].resourceError) : 0,
      jsErrorRate: jsError[0]?.jsError ? `${(Number(jsError[0].jsError) / pv[0].pv * 100).toFixed(2)}%` : '0',
      blankRate: blank[0]?.blankScreen ? `${(Number(blank[0].blankScreen) / pv[0].pv * 100).toFixed(2)}%` : '0',
      resourceErrorRate: resourceError[0]?.resourceError ? `${(Number(resourceError[0].resourceError) / pv[0].pv * 100).toFixed(2)}%` : '0',
      fcp: `${Number(fcp[0]._col0).toFixed(0)}ms`,
      lcp: `${Number(lcp[0]._col0).toFixed(0)}ms`,
    },
  ]
}

watch(() => props.projectName, (newVal) => {
  if (newVal === '')
    return
  getTlsLogsAll()
  nextTick(() => {
    echartsInit()
  })
}, { immediate: true })

onBeforeUnmount(() => {
  // 组件销毁时清理图表
  if (myChart) {
    myChart.dispose()
    myChart = null
  }
})
</script>

<template>
  <div>
    <el-row :gutter="20" style="display: flex;">
      <el-col :span="18">
        <div class="white-box" style="height: 100%;">
          <div class="main-title">
            {{ projectName }} 7-day PV
          </div>
          <div ref="pvChartRef" class="chartDom" />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="white-box" style="padding-top: 0;padding-bottom: 0;">
          <div class="single-list-counter">
            <h3 v-if="imgList[0]" class="deep_blue_2">
              <span class="deep_blue_2"> {{ imgList[0].fcp }}</span>
            </h3>
            <h3 v-else>
              - -
            </h3>
            <p>
              FCP
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <template #content>
                  <div>
                    First Contentful Pain（首次内容绘制）。<br>
                    FCP 是指浏览器首次将页面的任何内容（如文本、图像、SVG 等）绘制到屏幕上的时间点。<br>
                    它标志着用户从点击链接到页面开始显示有用内容所需的时间。
                  </div>
                </template>
                <i class="i-ix-alarm" />
              </el-tooltip>
            </p>
          </div>
          <div class="single-list-counter">
            <h3 v-if="imgList[0]" class="deep_blue_2">
              <span class="deep_blue_2">{{ imgList[0].lcp }}</span>
            </h3>
            <h3 v-else>
              - -
            </h3>
            <p>
              LCP
              <el-tooltip class="item" effect="dark" placement="bottom-start">
                <template #content>
                  <div>
                    Largest Contentful Paint<br>
                    是指页面视口中最大内容元素的可见时间点<br>
                    用于衡量用户在页面加载时能感知到主要内容加载完成的速度。
                  </div>
                </template>
                <i class="i-ix-alarm" />
              </el-tooltip>
            </p>
          </div>
          <div class="single-list-counter">
            <h3 v-if="imgList[0]" class="deep_blue_2">
              <span class="deep_blue_2">{{ imgList[0].jsErrorRate }}
                <span style="font-size: 20px;margin-left: 10px;">
                  ({{ imgList[0].jsErrorCount.toLocaleString() }}次)
                </span>
              </span>
            </h3>
            <h3 v-else>
              - -
            </h3>
            <p>
              JS Error Rate
            </p>
          </div>
          <div class="single-list-counter">
            <h3 v-if="imgList[0]" class="deep_blue_2">
              <span class="deep_blue_2">{{ imgList[0].blankRate }}
                <span style="font-size: 20px;margin-left: 10px;">
                  ({{ imgList[0].blankCount.toLocaleString() }}次)
                </span>
              </span>
            </h3>
            <h3 v-else>
              - -
            </h3>
            <p>Blank Rate </p>
          </div>
          <div class="single-list-counter">
            <h3 v-if="imgList[0]" class="deep_blue_2">
              <span class="deep_blue_2">{{ imgList[0].resourceErrorRate }}
                <span style="font-size: 20px;margin-left: 10px;">
                  ({{ imgList[0].resourceErrorCount.toLocaleString() }}次)
                </span>
              </span>
            </h3>
            <h3 v-else>
              - -
            </h3>
            <p>Resource Error Rate </p>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.chartDom {
  width: 100%;
  height: calc(100% - 24px);
}
</style>
