<script setup lang="ts">
import type { MonitorName } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'
import { openGrafana, openVolcengine } from '~/pages/report-chart/utils'

interface Props {
  projectName: string
}

const props = defineProps<Props>()

interface TableItem {
  pathname: string
  pv: number
  avgFcp: number
}

const tableList = ref<TableItem[]>([])

const sql = computed(() => ({
  monitorName: 'performance' as MonitorName,
  sql: `appKey:"${props.projectName}" AND env: prod | SELECT pathname, COUNT(*) AS pv, AVG(FCP) AS avgFcp WHERE FCP > 0 AND FCP < 10000 GROUP BY pathname ORDER BY avgFcp DESC LIMIT 10`,
}))

watch(() => props.projectName, (newVal) => {
  if (newVal === '')
    return
  getTlsLogs()
}, { immediate: true })

async function getTlsLogs() {
  const res = await getTlsLogsApi(sql.value)
  if (res) {
    tableList.value = res.map(item => ({
      ...item,
      avgFcp: item?.avgFcp ? ~~(item?.avgFcp) : 0,
    }))
  }
}
</script>

<template>
  <div class="mb-30px">
    <div class="white-box">
      <div class="main-title">
        bad FCP Top10 页面
        <el-button
          type="primary"
          size="small"
          @click.stop="() => openVolcengine(sql)"
        >
          火山云
        </el-button>
      </div>
      <el-table class="monitor-el-table" :data="tableList" style="width: 100%; height:500px">
        <el-table-column prop="pathname" label="页面路由" />
        <el-table-column prop="avgFcp" label="FCP(MS)" />
        <el-table-column prop="pv" label="PV" />
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="openGrafana((projectName || 'study-app'), row.pathname)"
            >
              grafana
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click.stop="openVolcengine({
                monitorName: 'performance',
                sql: `appKey: ${projectName} AND env: prod AND pathname: ${row.pathname}`,
              })"
            >
              火山云
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
