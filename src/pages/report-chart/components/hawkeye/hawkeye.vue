<script setup lang="ts">
import type * as echarts from 'echarts'
import { autoNativeQuery } from '@guanghe-pub/auto-native-query'
import { SocketIO } from './socketConnection'

interface Props {
  pathname: string
}

const props = defineProps<Props>()

const status = ref('loading')
const queryStr = ref('')
const msgInfo = ref('')
const inspectDetail = ref<{
  url: string
  name?: string
  msg: string
  htmlText: string
  code: number
}>({
  url: '',
  msg: '',
  htmlText: '',
  code: 0,
})

const iframeDom = ref<HTMLIFrameElement | null>(null)
let myChart: echarts.ECharts | null = null

// 初始化 Socket 监听
function initSocket() {
  SocketIO.socket.value?.on('message', (html: any) => {
    if (html.url !== inspectDetail.value.url)
      return
    inspectDetail.value = html
  })
}

// 搜索函数
function search() {
  const url = props.pathname
  if (!url)
    return

  const parseURL = `https://yangcong345.com${url}${queryStr.value}`
  inspectDetail.value = {
    name: url,
    url: parseURL,
    msg: '',
    htmlText: '',
    code: 0,
  }

  SocketIO.ready(() => {
    setTimeout(() => {
      SocketIO.socket.value?.emit('clientMessage', {
        url: {
          name: url.slice(url.lastIndexOf('/')) || url,
          value: parseURL,
        },
        socketUserId: SocketIO.socket.value?.io.opts.query?.userId,
      })
    }, 2000)
  })
}

// 监听 pathname 变化
watch(() => props.pathname, (newVal) => {
  if (newVal === '')
    return
  search()
})

// 监听 inspectDetail 变化
watch(inspectDetail, (newValue) => {
  const { htmlText, code, msg } = newValue
  if (htmlText && code === 1) {
    status.value = 'loaded'
    nextTick(() => {
      if (iframeDom.value?.contentWindow) {
        iframeDom.value.contentWindow.document.write(htmlText)
      }
    })
  }
  else {
    if (code === 0) {
      status.value = 'loading'
    }
    else if (code === 2) {
      status.value = 'error'
      msgInfo.value = msg
    }
  }
}, { deep: true })

// 组件创建时执行
onMounted(async () => {
  initSocket()

  try {
    const query = await autoNativeQuery({
      extra: {
        exerciseDebug: '0',
        notchHeight: '0',
        statusBarHeight: '0',
        appVersion: '7.72.0',
      },
      useName: 'tk01', // 账号 tk01-tk15 [范围]
      env: 'prod', // dev ｜ stage | prod
      // phone: '13611496568' // 白名单用户手机
    })
    queryStr.value = query
  }
  catch (error) {
    console.error('获取查询参数失败:', error)
  }
  finally {
    search()
  }
})

// 组件卸载时清理
onBeforeUnmount(() => {
  SocketIO.socket.value?.off('message')
  if (myChart) {
    myChart.dispose()
    myChart = null
  }
})
</script>

<template>
  <div class="is--hawkeye mt-30px">
    <h1 class="mb-20px text-20px font-600">
      hawkeye
    </h1>
    <div v-if="pathname !== ''" v-loading="status === 'loading'" element-loading-text="hawkeye生成中" class="render-html">
      <iframe
        v-if="status === 'loaded'"
        ref="iframeDom"
        class="hawkeye-content"
        title="hawkeye"
      />
      <div v-if="status === 'error'">
        hawkeye结果生成失败，<span style="color: #09f;" @click="search">点击重试</span>
        <p>{{ msgInfo }}</p>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.hawkeye-content {
  width: 100%;
  height: 1000px;
}
.is--hawkeye {
  min-height: 100px;
}
</style>
