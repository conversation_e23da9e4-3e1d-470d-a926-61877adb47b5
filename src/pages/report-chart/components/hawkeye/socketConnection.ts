import type { Socket } from 'socket.io-client'
import io from 'socket.io-client'

function getUserId(): string {
  const userId = localStorage.getItem('userId')
  if (!userId) {
    const newUserId = Math.random().toString(36).slice(2)
    localStorage.setItem('userId', newUserId)
    return newUserId
  }
  return userId
}

export interface SocketConnectionOptions {
  url: string
  room?: string
  reconnectionAttempts?: number
  autoConnect?: boolean
}

export function useSocketIO(options: SocketConnectionOptions = {
  url: 'https://hawkeye.yangcong345.com/wxconnection',
  room: 'report',
  reconnectionAttempts: 10,
  autoConnect: true,
}) {
  const socket = ref<Socket | null>(null)
  const isReady = ref(false)
  const readyCallbacks: Array<() => void> = []

  const log = (...args: any[]) => {
    // eslint-disable-next-line no-console
    console.log('socketConnection', ...args)
  }

  const connect = () => {
    socket.value = io(options.url, {
      transports: ['websocket'],
      reconnection: true,
      autoConnect: options.autoConnect,
      reconnectionAttempts: options.reconnectionAttempts,
      query: {
        room: options.room,
        userId: getUserId(),
      },
    })

    socket.value.on('connect', () => {
      isReady.value = true
      log('连接成功！', socket.value)
      readyCallbacks.forEach((callback) => {
        callback()
      })
      readyCallbacks.length = 0
    })

    socket.value.on('close', () => {
      log('关闭连接', socket.value)
    })

    socket.value.on('open', () => {
      log('打开连接', socket.value)
    })

    socket.value.on('disconnect', () => {
      log('关闭连接了', socket.value)
      isReady.value = false
    })

    socket.value.on('connect_error', (e) => {
      log('连接错误了', e)
      isReady.value = false
    })
  }

  // 如果设置了自动连接，则立即连接
  if (options.autoConnect) {
    connect()
  }

  const close = () => {
    socket.value?.close()
  }

  const open = () => {
    socket.value?.open()
  }

  const ready = (callback: () => void) => {
    if (isReady.value) {
      callback()
    }
    else {
      readyCallbacks.push(callback)
    }
  }

  const destroy = () => {
    if (socket.value) {
      socket.value.disconnect()
      socket.value.removeAllListeners()
      socket.value = null
    }
    isReady.value = false
  }

  return {
    socket,
    isReady,
    connect,
    close,
    open,
    ready,
    destroy,
  }
}

// 为了兼容性，提供一个默认实例
export const SocketIO = useSocketIO()
