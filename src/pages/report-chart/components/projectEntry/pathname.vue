<script setup lang="ts">
import type { MonitorName, SearchParam, TlsLogItemType } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'

interface Props {
  projectName: string
  pathname: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'pathnameChange', value: string): void
}>()

const pathnameList = ref<TlsLogItemType[]>([])
const localPathname = ref(props.pathname)

const pathnameSql = computed(() => ({
  monitorName: 'performance' as MonitorName,
  sql: `env:prod | SELECT appKey, pathname, count(*) as pv, count(DISTINCT(userId)) as uv WHERE appKey = '${props.projectName}' GROUP by appKey, pathname  HAVING uv > 5 order by uv desc  limit 10000`,
}))

watch(() => props.projectName, async (newVal) => {
  if (!newVal)
    return
  pathnameList.value = await getTlsLogsApi(pathnameSql.value as SearchParam)
}, { immediate: true })

watch(() => props.pathname, (val) => {
  localPathname.value = val
})

function changePathname(value: string) {
  emit('pathnameChange', value)
}
</script>

<template>
  <div>
    <el-form>
      <el-form-item label="pathname:">
        <el-select
          v-model="localPathname"
          style="width: 460px;margin-right: 15px;"
          placeholder="Pathname"

          filterable clearable
          :popper-append-to-body="false"
          @change="changePathname"
        >
          <el-option
            v-for="option of pathnameList"
            :key="option.pathname"
            :label="option.pathname"
            :value="option.pathname"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>
