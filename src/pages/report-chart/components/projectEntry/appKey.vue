<script lang="ts" setup>
import type { SearchParam, TlsLogItemType } from '~/apis/monitor'
import { getTlsLogsApi } from '~/apis/monitor'

const props = defineProps<{
  projectName: string
}>()

const emit = defineEmits<{
  (e: 'appKeyChange', value: string): void
}>()

const appKeyList = ref<TlsLogItemType[]>([])
const localProjectName = ref(props.projectName)

const appKeySql = computed(() => {
  return {
    monitorName: 'performance',
    sql: `env:prod | SELECT appKey, count(*) as pv, count(DISTINCT(userId)) as uv GROUP by appKey order by uv desc `,
  }
})

watch(() => props.projectName, (val) => {
  localProjectName.value = val
})

onMounted(async () => {
  appKeyList.value = await getTlsLogsApi(appKeySql.value as SearchParam)
})

function changeAppKey(value: string) {
  emit('appKeyChange', value)
  localProjectName.value = value
}
</script>

<template>
  <div>
    <el-form>
      <el-form-item label="appKey:">
        <el-select
          v-model="localProjectName"
          style="margin-right: 15px;"
          placeholder="AppKey"
          filterable
          :popper-append-to-body="false"
          @change="changeAppKey"
        >
          <el-option v-for="option of appKeyList" :key="option.appKey" :label="option.appKey" :value="option.appKey" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>
