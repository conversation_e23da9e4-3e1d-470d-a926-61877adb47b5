<!-- pathname:"/study-app/courses/system" | SELECT COUNT(*) AS pv -->

<script>
import { getTlsLogsApi } from '~/apis/monitor'
import { isOverSize } from '../../Vitals'

export default {
  props: ['pv', 'uv', 'pathname'],
  data() {
    return {
      imgList: [],
    }
  },
  computed: {
    jsErrorSql() {
      const pathname = this.pathname
      return {
        monitorName: 'abnormal',
        sql: `env:prod AND pathname:${pathname} AND errorType: jsError | SELECT COUNT(*) AS jsError`,
      }
    },
    blankSql() {
      const pathname = this.pathname
      return {
        monitorName: 'abnormal',
        sql: `env:prod AND pathname:${pathname} AND errorType: blankScreen | SELECT COUNT(*) AS blankScreen`,
      }
    },
    resourceErrorSql() {
      const pathname = this.pathname
      return {
        monitorName: 'abnormal',
        sql: `env:prod AND pathname:${pathname} AND errorType: resourceError | SELECT COUNT(*) AS resourceError`,
      }
    },
    inpSql() {
      const pathname = this.pathname
      return {
        monitorName: 'performance',
        sql: `env:prod AND pathname:${pathname} | SELECT ROUND(AVG(INP), 2) WHERE INP > 0 AND INP < 10000`,
      }
    },
    clsSql() {
      const pathname = this.pathname
      return {
        monitorName: 'performance',
        sql: `env:prod AND pathname:${pathname} | SELECT ROUND(AVG(CLS), 4) WHERE CLS > 0`,
      }
    },
    lcpSql() {
      const pathname = this.pathname
      return {
        monitorName: 'performance',
        sql: `env:prod AND pathname:${pathname} | SELECT ROUND(AVG(LCP), 2) WHERE LCP > 0 AND LCP < 10000`,
      }
    },
    memory() {
      return {
        monitorName: 'app',
        sql: `eventKey:memoryMonitor AND data.pageMemory:>0 AND data.pathName:${this.pathname} | select ROUND(AVG("data.pageMemory"), 2) as memory`,
      }
    },
  },
  watch: {
    pathname: {
      handler() {
        this.imgList = []
        if (this.pathname === '')
          return
        this.getTlsLogs()
      },
      immediate: true,
    },
  },
  methods: {
    isOverSize,
    getTlsLogs() {
      Promise.all([
        getTlsLogsApi(this.jsErrorSql),
        getTlsLogsApi(this.blankSql),
        getTlsLogsApi(this.resourceErrorSql),
        getTlsLogsApi(this.inpSql),
        getTlsLogsApi(this.clsSql),
        getTlsLogsApi(this.lcpSql),
        getTlsLogsApi(this.memory),
      ]).then(([jsError, blank, resourceError, inp, cls, lcp, memory]) => {
        const pv = this.pv
        this.imgList = [
          {
            pv,
            uv: this.uv,
            jsErrorCount: jsError[0].jsError,
            blankCount: blank[0].blankScreen,
            resourceErrorCount: resourceError[0].resourceError,
            jsErrorRate: (jsError[0].jsError / pv * 100).toFixed(2),
            blankRate: (blank[0].blankScreen / pv * 100).toFixed(2),
            resourceErrorRate: (resourceError[0].resourceError / pv * 100).toFixed(2),
            inp: Number(inp[0]._col0),
            lcp: Number(lcp[0]._col0),
            cls: Number(cls[0]._col0),
            memory: Number(memory[0].memory),
          },
        ]
      })
    },
  },
}
</script>

<template>
  <div class="white-box mb-30px">
    <el-table class="monitor-el-table" :data="imgList" style="width: 100%; margin-bottom: 20px;">
      <el-table-column prop="pv" label="pv" />
      <!-- <el-table-column prop="uv" label="uv" /> -->
      <el-table-column prop="inp" label="inp">
        <template #default="scope">
          <el-tooltip class="item" effect="dark" content="输入延迟" placement="top">
            <span :style="`${isOverSize('INP', scope.row.inp) ? 'color: red;font-weight: bold;' : ''}`">
              {{ scope.row.inp }}ms
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="lcp" label="lcp">
        <template #default="scope">
          <span :style="`${isOverSize('LCP', scope.row.lcp) ? 'color: red;font-weight: bold;' : ''}`">
            {{ scope.row.lcp }}ms
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="cls" label="cls">
        <template #default="scope">
          <span :style="`${isOverSize('CLS', scope.row.cls) ? 'color: red;font-weight: bold;' : ''}`">
            {{ scope.row.cls }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="jsErrorCount" label="js错误数" />
      <el-table-column prop="blankCount" label="白屏数" /> -->
      <!-- <el-table-column prop="resourceErrorCount" label="资源错误数" /> -->
      <el-table-column prop="jsErrorRate" label="jsErrorRate">
        <template #default="scope">
          <span :style="`${isOverSize('jsError', scope.row.jsErrorRate) ? 'color: red;font-weight: bold;' : ''}`">
            {{ scope.row.jsErrorRate }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="blankRate" label="blankRate">
        <template #default="scope">
          <span :style="`${isOverSize('blankScreen', scope.row.blankRate) ? 'color: red;font-weight: bold;' : ''}`">
            {{ scope.row.blankRate }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="resourceErrorRate" label="resourceErrorRate">
        <template #default="scope">
          <span
            :style="`${isOverSize('resourceError', scope.row.resourceErrorRate) ? 'color: red;font-weight: bold;' : ''}`"
          >
            {{ scope.row.resourceErrorRate }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="memory" label="内存占用">
        <template #default="scope">
          <span :style="`${isOverSize('memory', scope.row.memory) ? 'color: red;font-weight: bold;' : ''}`">
            {{ scope.row.memory }}M
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
