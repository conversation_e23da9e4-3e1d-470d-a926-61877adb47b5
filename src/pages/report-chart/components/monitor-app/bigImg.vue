<script setup lang="ts">
import type { MonitorName, TlsLogItemType } from '~/apis/monitor'
import { computed, ref, watch } from 'vue'
import { getTlsLogsApi } from '~/apis/monitor'
import { openVolcengine } from '~/pages/report-chart/utils'

interface Props {
  pathname: string
}

const props = defineProps<Props>()

const tableList = ref<TlsLogItemType[]>([])

const sql = computed(() => ({
  monitorName: 'app' as MonitorName,
  sql: `eventKey:webBigImageInfo AND data.pathName:${props.pathname} | select "data.content" as src group by src`,
}))

watch(() => props.pathname, async (newVal) => {
  if (!newVal)
    return
  await getBigImgLogs()
}, { immediate: true })

async function getBigImgLogs() {
  const res = await getTlsLogsApi(sql.value)
  if (res.length === 0)
    return
  tableList.value = res
}
</script>

<template>
  <div style="flex: 1">
    <div class="main-title">
      页面大图数据
      <el-button
        type="primary"
        size="small"
        @click.stop="openVolcengine(sql)"
      >
        火山云
      </el-button>
    </div>
    <el-table class="monitor-el-table" :data="tableList" style="width: 100%; height:400px">
      <el-table-column prop="src" label="imgSrc" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click.stop="openVolcengine({
              monitorName: 'app',
              sql: `eventKey:webBigImageInfo AND data.pathName:${pathname} AND data.content:'${row.src}'`,
            })"
          >
            火山云
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
