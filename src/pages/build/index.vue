<script lang="ts" setup>
import type { Belong, Label, User } from '~/apis/enums'
import type { Pipeline } from '~/apis/pipeline'
import type { Project } from '~/apis/project'
import type { Release } from '~/apis/release'
import { fetchAllUsers, fetchBelongs, fetchLabels } from '~/apis/enums'
import { fetchPipelineList } from '~/apis/pipeline'
import { fetchAllProjectList } from '~/apis/project'

import BuildPopup from '~/components/BuildPopup.vue'
import PipelineList from '~/components/pipeline/list.vue'
import ReleasePopup from '~/components/ReleasePopup.vue'

const route = useRoute()
const router = useRouter()

const currentBelong = ref<string>('')
const belongs = ref<Belong[]>([])

const currentLabel = ref<string>('')
const labels = ref<Label[]>([])

const projectId = ref<string>('')
const allProjectList = ref<Project[]>([])
const currentProjectList = ref<Project[]>([])

const users = ref<User[]>([])

function changeFilter() {
  const label = currentLabel.value
  const belong = currentBelong.value === '8' ? '' : currentBelong.value
  let list = allProjectList.value
  if (belong) {
    list = list.filter((item) => {
      return item.belong === belong
    })
  }
  if (label) {
    list = list.filter((item) => {
      return item.label.includes(label)
    })
  }
  currentProjectList.value = list
  projectId.value = ''
}

const page = ref<number>(1)
const pageSize = ref<number>(10)
const currentBranch = ref<string>('')
const count = ref<number>(0)
const pipelines = ref<Pipeline[]>([])

function getPipelineList() {
  if (projectId.value) {
    fetchPipelineList({
      page: page.value,
      pageSize: pageSize.value,
      projectId: projectId.value,
    }).then((res) => {
      if (res) {
        count.value = res.total
        pipelines.value = res.list
      }
    })
  }
}

function handleCurrentChange(currentPage: number) {
  page.value = currentPage
  getPipelineList()
}

const buildPopupVisible = ref(false)

function showBuildPopup() {
  buildPopupVisible.value = true
}

const releasePopupVisible = ref(false)
const releaseInfo = ref<Partial<Release>>({
  project_id: '',
  tag: '',
  publish_content: '',
  sql: '',
  remark: '',
  depend_project: '',
  notify_immediately: false,
  developer: '',
  tester: '',
  sonic: [],
})

function showReleasePopup(row: Pipeline) {
  if (!row.tag) {
    return ElMessage.warning('未获取到当前构建记录的tag')
  }
  releaseInfo.value.project_id = row.project_id
  releaseInfo.value.tag = row.tag
  releasePopupVisible.value = true
}

function handleReleaseSuccess() {
  ElMessageBox.confirm('详情可前往上线管理页面查看，是否前往？', '创建成功', {
    confirmButtonText: '去看看',
    callback: (action: string) => {
      if (action === 'confirm') {
        router.push('/release')
      }
    },
  })
}

const filterPipelines = computed(() => {
  return pipelines.value.filter((item) => {
    if (currentBranch.value) {
      return item.branch === currentBranch.value
    }
    return true
  })
})

const packageManager = ref<string>('')

watch(projectId, (value: string) => {
  getPipelineList()
  packageManager.value = allProjectList.value.find(item => item.id === value)?.config?.installCmd || ''
  const { name } = currentProjectList.value.find(item => item.id === value) || { name: '' }
  router.replace(`${route.path}?${name}`)
  localStorage.setItem('projectId', value)
})

const timer = ref<NodeJS.Timeout | null>(null)

function init() {
  fetchAllProjectList().then((res) => {
    allProjectList.value = res
    currentProjectList.value = res

    const { projectId: projectIdQuery, name } = route.query
    const problemKey = Object.keys(route.query)[0]
    let localprojectId = localStorage.getItem('projectId') || ''
    if (projectIdQuery) {
      localStorage.setItem('projectId', projectIdQuery as string)
      localprojectId = projectIdQuery as string
    }

    const matchedItem = allProjectList.value.find(
      (item) => {
        return item.id === projectIdQuery || item.name === name || item.name === problemKey || item.id === localprojectId
      },
    )

    if (matchedItem) {
      currentLabel.value = matchedItem.label[0]
      changeFilter()
      projectId.value = matchedItem.id
    }
  })
  fetchAllUsers().then((res) => {
    users.value = res
  })
  fetchBelongs().then((res) => {
    belongs.value = res
  })
  fetchLabels().then((res) => {
    labels.value = res
  })
  if (route.query?.projectId) {
    projectId.value = route.query?.projectId as string
  }
  timer.value = setInterval(() => {
    getPipelineList()
  }, 3000)
}

onMounted(() => {
  init()
})
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<template>
  <div>
    <div>
      <el-form inline>
        <el-form-item label="请选择服务:">
          <el-select
            v-model="currentBelong"
            class="mr-4 w-40"
            placeholder="选择业务归属"
            filterable
            clearable
            @change="changeFilter"
          >
            <el-option v-for="option of belongs" :key="option.value" :label="option.label" :value="option.value" />
          </el-select>
          <el-select
            v-model="currentLabel"
            class="mr-4 w-30"
            placeholder="类型"
            filterable
            @change="changeFilter"
          >
            <el-option v-for="item in labels" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-model="projectId" class="w-70" placeholder="请选择服务" filterable>
            <el-option v-for="item in currentProjectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button :disabled="!projectId" type="primary" @click="showBuildPopup">
            新增构建
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <h3>流水线记录</h3>
      <el-radio-group v-model="currentBranch" class="mb-5 mt-4">
        <el-radio-button label="master" value="master" />
        <el-radio-button label="develop" value="develop" />
        <el-radio-button label="release" value="release" />
        <el-radio-button label="全部" value="" />
      </el-radio-group>
    </div>
    <template v-if="projectId">
      <PipelineList :pipelines="filterPipelines" :users="users" @release="showReleasePopup" @refresh="getPipelineList" />
      <el-pagination
        :current-page="page"
        :page-size="pageSize"
        layout="total, prev,pager, next"
        :total="count"
        @current-change="handleCurrentChange"
      />
    </template>
    <el-empty v-else description="请先选择服务" />
  </div>
  <BuildPopup v-model="buildPopupVisible" :project-id="projectId" :package-manager="packageManager" @refresh="getPipelineList" />
  <ReleasePopup v-model="releasePopupVisible" :release-info="releaseInfo" :is-from-deploy="true" @refresh="handleReleaseSuccess" />
</template>

<route lang="yaml">
meta:
  menu:
    name: 构建
    sort: 1
    path: /build
    icon: i-famicons-git-network-outline
</route>
