<script lang="ts" setup>
import type { User } from '~/apis/enums'
import type { Alarm } from '~/apis/monitor'
import type { UserInfoType } from '~/apis/user'
import { ElMessageBox, ElNotification } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { fetchAllUsers } from '~/apis/enums'
import {
  alarmCreate,
  alarmDelete,
  alarmUpdate,
  fetchAlarmList,
} from '~/apis/monitor'
import { getUserListApi } from '~/apis/user'

import { dataToSource, sourceToData } from '~/pages/monitor/utils'

const route = useRoute()

const resetData = {
  //  告警策略id， 更新时必传
  alertId: '',

  // 同一个日志项目下，告警策略名称不可重复。
  // 只能包括小写字母、数字、- 必须以小写字母或者数字开头和结尾。
  // 长度为 3~63 字符。
  alertName: '',

  // 告警配置分类
  // 白屏错误: blankScreen, 接口错误: httperror,
  // js错误: js错误: jserror, 资源错误: resourceerror,
  // FCP错误: fcp, 通用告警：general
  alertDetailCategory: 'general',

  // 告警类型 enum 【trends:趋势告警，threshold: 阈值告警】
  // 趋势告警暂时仅支持 general之外的配置分类
  alertDetailType: 'threshold',

  // 计算方式，enum [count: 计数，百分比：percent]
  // 除了general类型外其他默认为百分比
  alertDetailComputeMode: 'count',

  // 告警级别， enum  [notice， warning， error]
  alertDetailLevel: 'error',
  alertDetailAppKey: '',

  // 每执行周期达到多少次/达到多少百分比告警,
  // count：10 可以表示数量达到十次或者占比达到10 % 产生预警 （根据alertDetail -> computeMode 决定）,
  conditionCount: 10,

  // 错误分类为general时，表示创建monitor-api 项目的告警，
  // example：NOT| 200 | 400 表示非200, 400状态码，
  // 其他规则包含equal：等于，gt：大于，lt：小于
  // 和 conditionErrorType 互斥
  conditionStatus: '200',
  statusType: 'not',

  // 错误分类为general时，表示创建monitor-abnormal项目告警，错误类型
  // 和status互斥
  conditionErrorType: '',
  // 底数 数量最低大于的值。
  // alertDetail -> computeMode 为percent，传该值，默认0
  // 比如状态码500超过1%，但是gt为10的话表示产生的总请求大于10的情况下。
  // 避免接口调用量少，导致几个错误产生的报警误报
  conditionGt: 100,
  // 火山云日志项目的topicName
  topicName: 'monitor-abnormal',
  status: true,

  // Period：周期执行，即每隔一段时间执行一次。
  // Fixed：定期执行，即每天的固定时间点执行一
  requestCycleType: 'Period',
  requestCycleTime: 30,
  // 自定义通知信息，产生告警时，会携带通知
  userDefineMsg: '',
  module: '',
  description: '',
  userName: '',
  userId: '',
  // 通知配置分组：
  // 学习工具前端：7; 商业化前端：8; 入校前端：9; 职教前端：10
  // 武汉电销前端：11，智能硬件前端：12，前端技术栈：13，技术支撑前端：14
  noticesGroup: [],
  // // 通知配置 默认飞书群，feishu
  // noticesType: 'feishu',

  /**
   * 持续周期。持续满足触发条件 TriggerPeriod 个周期后，再进行告警
   * 最小值为 1，最大值为 10，默认为 1
   * 暂不支持
   */
  // triggerPeriod: '',
  /**
   * 告警通知发送的周期。
   * 当告警持续触发次数达到指定限额（TriggerPeriod）时，日志服务会根据指定的周期发送告警通知。
   * 暂不支持
   */
  // alarmPeriod: '',
}

const appKeyArray = ref<string[]>([])
const appKeyValue = ref('')
const groupNameArray = [
  { text: '学习工具前端', value: 7 },
  { text: '商业化前端', value: 8 },
  { text: '入校前端', value: 9 },
  { text: '职教前端', value: 10 },
  { text: '武汉电销前端', value: 11 },
  { text: '技术支撑前端', value: 14 },
  { text: '智能硬件前端', value: 12 },
]
const groupNameValue = ref('')
const noticesGroupName = {
  7: '学习工具前端',
  8: '商业化前端',
  9: '入校前端',
  10: '职教前端',
  11: '武汉电销前端',
  12: '智能硬件前端',
  14: '技术支撑前端',
  13: '前端技术栈',
}
const allAlarmList = ref<Alarm[]>([])
const alarmList = ref<Alarm[]>([])
const dialogFormVisible = ref(false)
const form = reactive({ ...resetData })
const searchInfo = ref('')
const users = ref<User[]>([])
const formLabelWidth = '160px'
const rules = {
  alertName: [
    { required: true, message: '请输入告警名称,告警名称不可重复,仅限小写字母,数字,-；必须以小写字母或者数字开头和结尾；长度为3-63个字符.', trigger: 'blur' },
    { min: 3, max: 60, message: '长度在 3 到 60 个字符', trigger: 'blur' },
  ],
  alertDetailAppKey: [
    { required: true, message: '请填写正确的appKey', trigger: 'blur' },
  ],
  userDefineMsg: [
    { required: true, message: '请输入告警描述', trigger: 'blur' },
    { min: 3, max: 60, message: '长度在 3 到 60 个字符', trigger: 'blur' },
  ],
  module: [
    { required: true, message: '请输入告警模块', trigger: 'blur' },
    { validator: checkModule, trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入模块描述', trigger: 'blur' },
    { min: 3, max: 60, message: '长度在 3 到 60 个字符', trigger: 'blur' },
  ],
  userName: [
    { required: true, message: '请选择模块负责人', trigger: 'blur' },
  ],
}
const userData = ref<UserInfoType[]>([])
const formRef = ref()

watch(appKeyValue, (val) => {
  if (!val)
    return
  groupNameValue.value = ''
  alarmList.value = allAlarmList.value.filter((item: Alarm) => item.alertDetailAppKey === val)
})
watch(groupNameValue, (val) => {
  if (!val)
    return
  appKeyValue.value = ''
  alarmList.value = allAlarmList.value.filter((item: Alarm) => item.noticesGroup.includes(val.toString()))
})
watch(dialogFormVisible, (val) => {
  if (!val)
    Object.assign(form, resetData)
})

onMounted(async () => {
  await alarmLoad(true)
  fetchAllUsers().then((res) => {
    users.value = res
  })
  const res = await getUserListApi({ page: 1, pageSize: 200 })
  userData.value = res.list
})

function checkModule(rule: any, value: string, callback: (error?: Error) => void) {
  if (!value.startsWith('/')) {
    return callback(new Error('模块需要以 / 开头'))
  }
  if (value.endsWith('/')) {
    return callback(new Error('模块不需要以 / 结尾'))
  }
  callback()
}
function handleErrorTypeChange() {
  const typeStoreMap = {
    blankScreen: 'monitor-abnormal',
    httperror: 'monitor-api',
    jserror: 'monitor-abnormal',
    resourceerror: 'monitor-abnormal',
    fcp: 'monitor-performance',
  }
  form.topicName = typeStoreMap[form.conditionErrorType as keyof typeof typeStoreMap]
}
function alarmLoad(isFirst: boolean) {
  return fetchAlarmList().then((res) => {
    const list = res.list.map(dataToSource)
    allAlarmList.value = list
    alarmList.value = list
    appKeyArray.value = [...new Set(list.map(item => item.alertDetailAppKey))]
    if (isFirst) {
      const alertId = route.query.alertId
      if (alertId) {
        const active = alarmList.value.find(item => item.alertId === alertId)
        if (active) {
          Object.assign(form, active)
          dialogFormVisible.value = true
        }
      }
    }
  })
}
function alarmCreateFn(source: Alarm) {
  const data = sourceToData(source)
  return alarmCreate(data).then(() => {
    Object.assign(form, resetData)
    ElNotification({
      title: '成功',
      message: '创建成功',
      type: 'success',
    })
  }).catch((e) => {
    ElNotification.error({
      title: '错误',
      message: `创建失败：${e.message}`,
    })
    throw e
  })
}
function alarmDeleteFn(row: Alarm) {
  if (!row.alertId)
    return
  alarmDelete(row.alertId).then(() => {
    ElNotification({
      title: '成功',
      message: '删除成功',
      type: 'success',
    })
    alarmLoad(false)
  }).catch((e) => {
    ElNotification.error({
      title: '错误',
      message: `删除失败：${e.message}`,
    })
    throw e
  })
}
function alarmUpdateFn(row: Alarm) {
  const data = sourceToData(row)
  return alarmUpdate(data).then(() => {
    Object.assign(form, resetData)
    ElNotification({
      title: '成功',
      message: '更新成功',
      type: 'success',
    })
  }).catch((e) => {
    ElNotification.error({
      title: '错误',
      message: `更新失败：${e.message}`,
    })
    throw e
  })
}
function handleEdit(alarm: Alarm) {
  Object.assign(form, alarm)
  dialogFormVisible.value = true
}
function handleDelete(row: Alarm) {
  ElMessageBox.confirm('确认删除？')
    .then(() => {
      alarmDeleteFn(row)
    })
    .catch(() => { })
}
function handleSure() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      const action = form.alertId ? alarmUpdateFn : alarmCreateFn
      action(form).then(() => {
        alarmLoad(false)
        dialogFormVisible.value = false
      })
    }
  })
}
function handleChangeStatus(row: Alarm) {
  alarmUpdateFn({ ...row, status: !row.status }).then(() => {
    row.status = !row.status
  })
}
function handleAlertDetailCategoryChange(value: string) {
  if (value === 'general') {
    form.alertDetailType = 'threshold'
  }
}
function handleAlertDetailUserName(userName: string) {
  form.userId = userData.value.filter(i => i.name === userName)[0].feishu_id
}
</script>

<template>
  <div class="app-container">
    <div class="flex justify-between">
      <div>
        <el-select v-model="appKeyValue" class="w-50" placeholder="请选择appkey">
          <el-option v-for="item in appKeyArray" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="groupNameValue" class="w-50" placeholder="请选择业务归属">
          <el-option v-for="item in groupNameArray" :key="item.value" :label="item.text" :value="item.value" />
        </el-select>
      </div>
      <div>
        <el-button type="primary" plain @click="dialogFormVisible = true">
          新增告警
        </el-button>
      </div>
    </div>
    <el-table
      :data="alarmList.filter(data => !searchInfo || data.alertName.toLowerCase().includes(searchInfo.toLowerCase()))"
      style="width: 100%"
      height="1000"
    >
      <el-table-column label="appKey" prop="alertDetailAppKey" />
      <el-table-column label="模块地址" prop="module" />
      <el-table-column label="模块描述" prop="description" />
      <el-table-column label="告警指标" prop="condition.errorType" />
      <el-table-column label="业务归属" prop="noticesGroup" column-key="noticesGroup">
        <template #default="scope">
          {{ noticesGroupName[scope.row.noticesGroup[0] as keyof typeof noticesGroupName] }}
        </template>
      </el-table-column>

      <el-table-column label="计算方法" prop="alertDetailComputeMode" />
      <el-table-column label="告警条件-阈值" prop="conditionCount" />
      <!-- <el-table-column label="告警条件-基值" prop="conditionGt" /> -->
      <el-table-column label="开启状态">
        <template #default="scope">
          <el-switch
            :model-value="scope.row.status"
            @change="handleChangeStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="负责人" prop="userName" />
      <el-table-column align="right">
        <template #header>
          <el-input v-model="searchInfo" autocomplete="off" placeholder="输入告警名称搜索" />
        </template>
        <template #default="scope">
          <el-button @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogFormVisible" append-to-body :close-on-click-modal="false" title="告警编辑">
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-form-item label="是否开启" :label-width="formLabelWidth">
          <el-switch v-model="form.status" />
        </el-form-item>
        <el-form-item label="告警名称(纯英文)" :label-width="formLabelWidth" prop="alertName">
          <el-input v-model="form.alertName" placeholder="如:http-error-01" autocomplete="off" />
        </el-form-item>
        <el-form-item label="项目appKey" :label-width="formLabelWidth" prop="alertDetailAppKey">
          <el-input v-model="form.alertDetailAppKey" placeholder="接入奥创时填写的项目标识" autocomplete="off" />
        </el-form-item>
        <el-form-item label="告警描述" :label-width="formLabelWidth" prop="userDefineMsg">
          <el-input v-model="form.userDefineMsg" placeholder="如：做题接口半小时内超过5%" autocomplete="off" />
        </el-form-item>
        <el-form-item label="告警模块" :label-width="formLabelWidth" prop="module">
          <el-input
            v-model="form.module"
            placeholder="可以填写项目模块或者页面, 指定模块的时候输入页面前缀,比如社区 /study-app/community"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="模块描述" :label-width="formLabelWidth" prop="description">
          <el-input v-model="form.description" placeholder="如：社区的模块" autocomplete="off" />
        </el-form-item>
        <el-form-item label="模块负责人" :label-width="formLabelWidth" prop="userName">
          <el-select v-model="form.userName" placeholder="请选择模块负责人" filterable @change="handleAlertDetailUserName">
            <el-option v-for="i in users" :key="i.label" :label="i.label" :value="i.label" />
          </el-select>
        </el-form-item>
        <el-form-item v-show="false" label="负责人id" :label-width="formLabelWidth" prop="userId">
          <el-input v-model="form.userId" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="告警配置分类" :label-width="formLabelWidth">
          <el-select v-model="form.alertDetailCategory" placeholder="告警配置分类" @change="handleAlertDetailCategoryChange">
            <el-option label="blankScreen" disabled value="blankScreen" />
            <el-option label="httperror" disabled value="httperror" />
            <el-option label="jserror" disabled value="jserror" />
            <el-option label="resourceerror" disabled value="resourceerror" />
            <el-option label="fcp" disabled value="fcp" />
            <el-option label="通用告警" value="general" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.alertDetailCategory === 'general'" label="告警条件-告警配置分类" :label-width="formLabelWidth">
          <el-select v-model="form.conditionErrorType" placeholder="告警配置分类" @change="handleErrorTypeChange">
            <el-option label="blankScreen(白屏)" value="blankScreen" />
            <el-option label="httperror(接口异常)" value="httperror" />
            <el-option label="jserror(js异常)" value="jserror" />
            <el-option label="resourceerror(资源异常)" value="resourceerror" />
            <el-option label="fcp(首次加载)" value="fcp" />
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="火山云store" :label-width="formLabelWidth">
          {{ form.topicName }}
          <el-select v-model="form.topicName" placeholder="请选择火山云store">
            <el-option label="monitor-abnormal" value="monitor-abnormal" />
            <el-option label="monitor-api" value="monitor-api" />
            <el-option label="monitor-performance" value="monitor-performance" />
          </el-select>
        </el-form-item> -->
        <el-form-item
          v-if="form.alertDetailCategory === 'httperror' || form.conditionErrorType === 'httperror'"
          label="http告警条件"
          :label-width="formLabelWidth"
        >
          <el-input v-model="form.conditionStatus">
            <template #prepend>
              <el-select v-model="form.statusType" placeholder="规则" style="width: 100px">
                <el-option label="不等于" value="not" />
                <el-option label="等于" value="equal" />
                <el-option label="大于" value="gt" />
                <el-option label="小于" value="lt" />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="告警类型" :label-width="formLabelWidth">
          <!-- 趋势告警暂时仅支持 general之外的配置分类 -->
          <el-select v-model="form.alertDetailType" placeholder="请选择告警类型">
            <el-option v-if="form.alertDetailCategory !== 'general'" disabled label="趋势告警" value="trends" />
            <el-option label="阈值告警" value="threshold" />
          </el-select>
        </el-form-item>
        <el-form-item label="计算方式" :label-width="formLabelWidth">
          <el-select v-model="form.alertDetailComputeMode" placeholder="请选择计算方式">
            <el-option label="计数" value="count" />
            <el-option label="百分比" value="percent" />
          </el-select>
        </el-form-item>
        <el-form-item label="告警条件-阈值" :label-width="formLabelWidth">
          <el-input-number v-model="form.conditionCount" :min="1" />
          <template v-if="form.alertDetailComputeMode === 'count'">
            个
          </template>
          <template v-if="form.alertDetailComputeMode === 'percent'">
            %
          </template>
        </el-form-item>
        <el-form-item v-if="form.alertDetailComputeMode === 'percent'" label="告警条件-基值" :label-width="formLabelWidth">
          最低 <el-input-number v-model="form.conditionGt" :min="1" /> 个
        </el-form-item>
        <!-- <el-form-item label="告警级别" :label-width="formLabelWidth">
          <el-select v-model="form.alertDetailLevel" placeholder="请选择告警级别">
            <el-option disabled label="notice" value="notice" />
            <el-option disabled label="warning" value="warning" />
            <el-option disabled label="error" value="error" />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="告警执行方式" :label-width="formLabelWidth">
          <el-select v-model="form.requestCycleType" placeholder="请选择计算方式">
            <el-option label="周期执行" value="Period" />
            <el-option label="定期执行" disabled value="Fixed" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="周期时间" :label-width="formLabelWidth">
          <template v-if="form.requestCycleType === 'Period'">
            每隔
          </template>
          <template v-if="form.requestCycleType === 'Fixed'">
            每天第
          </template>
          <el-input-number v-model="form.requestCycleTime" :min="30" :max="1440" />
          分钟（最小30）
        </el-form-item>
        <el-form-item label="告警通知群" :label-width="formLabelWidth">
          <el-select v-model="form.noticesGroup" multiple placeholder="告警通知群">
            <el-option label="学习工具前端" value="7" />
            <el-option label="商业化前端" value="8" />
            <el-option label="入校前端" value="9" />
            <el-option label="职教前端" value="10" />
            <el-option label="武汉电销前端" value="11" />
            <el-option label="智能硬件前端" value="12" />
            <el-option label="技术支撑前端" value="14" />
            <el-option label="前端技术栈" value="13" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取 消
          </el-button>
          <el-button type="primary" @click="handleSure">
            确 定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: 奥创监控
      path: /monitor
      icon: i-solar-iphone-outline
      sort: 6
    name: 告警管理
    sort: 1
    path: /monitor/manage
    icon: i-material-symbols-ssid-chart
</route>
