import type { Alarm } from '~/apis/monitor'

const transformList = [
  'alertDetail.category',
  'alertDetail.type',
  'alertDetail.computeMode',
  'alertDetail.level',
  'alertDetail.appKey',
  'condition.count',
  'condition.status',
  'condition.errorType',
  'condition.gt',
  // 'notices.group',
  // 'notices.type',
  'requestCycle.type',
  'requestCycle.time',
]

export function sourceToData(source: Alarm) {
  const origin: any = { ...source }
  if (source.alertDetailCategory === 'httperror' || source.conditionErrorType === 'httperror') {
    if (!origin.conditionStatus) {
      origin.conditionStatus = 'not|200'
    }
    else {
      origin.conditionStatus = `${origin.statusType}|${origin.conditionStatus}`
      origin.conditionStatus = origin.conditionStatus.replace(/\s/g, '')
    }
    delete origin.statusType
  }
  else {
    // 非 httperror 不能发送 conditionStatus
    delete origin.conditionStatus
    if (origin.condition) {
      delete origin.condition.status
    }
  }
  transformList.forEach((item) => {
    const [key, subKey] = item.split('.')
    const sourceKey = `${key}${subKey[0].toUpperCase()}${subKey.slice(1)}`
    origin[key] = origin[key] || {}
    if (origin[sourceKey]) {
      origin[key][subKey] = origin[sourceKey]
      delete origin[sourceKey]
    }
  })
  origin.notices = (origin.noticesGroup || []).map((group: string) => {
    return {
      group,
      type: 'feishu',
    }
  })

  delete origin.noticesGroup
  return origin
}

export function dataToSource(data: Alarm) {
  const origin: any = { ...data }
  transformList.forEach((item) => {
    const [key, subKey] = item.split('.')
    const sourceKey = `${key}${subKey[0].toUpperCase()}${subKey.slice(1)}`
    // origin[key] = origin[key] || {}
    if (origin[key][subKey]) {
      origin[sourceKey] = origin[key][subKey]
      delete origin[key][sourceKey]
    }
  })
  origin.noticesGroup = (origin.notices || []).map((notice: { group: string }) => {
    return notice.group
  })

  if (origin.alertDetailCategory === 'httperror' || origin.conditionErrorType === 'httperror') {
    const firstLine = origin.conditionStatus.indexOf('|')
    origin.statusType = origin.conditionStatus.slice(0, firstLine)
    origin.conditionStatus = origin.conditionStatus.slice(firstLine + 1)
  }
  delete origin.notices
  return origin
}
