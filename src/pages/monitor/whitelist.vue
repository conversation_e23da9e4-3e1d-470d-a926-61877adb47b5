<script lang="ts" setup>
import type { AlertWhiteListItem } from '~/apis/monitor'
import { ElMessageBox, ElNotification } from 'element-plus'
import { onMounted, reactive, ref, watch } from 'vue'
import {
  deleteAlertVkeFrontedWhite,
  fetchAlertVkeFrontedWhiteList,
  postAlertVkeFrontedWhite,
  putAlertVkeFrontedWhite,
} from '~/apis/monitor'
import { useUserStore } from '~/stores/user'

const resetData = {
  id: null as null | number,
  category: '',
  module: '',
  appKey: '',
  whiteList: [''],
  authorName: '',
  status: 1,
}

const alarmList = ref<object[]>([])
const dialogFormVisible = ref(false)
const whiteListString = ref('')
const form = reactive({ ...resetData })
const formLabelWidth = '120px'

watch(dialogFormVisible, (val) => {
  if (!val) {
    Object.assign(form, resetData)
    whiteListString.value = ''
  }
})

const rules = {
  appKey: [{ required: true, message: '请输入项目appKey' }],
  module: [{ required: true, message: '请输入页面模块' }],
  category: [{ required: true, message: '请选择异常分类' }],
  whiteListString: [{ required: true, validator: (rule: any, value: any, callback: (error?: Error) => void) => {
    if (!whiteListString.value) {
      callback(new Error('请输入具体信息'))
    }
    else {
      callback()
    }
  } }],
}

const userStore = useUserStore()

onMounted(() => {
  whiteListLoad()
})

function whiteListLoad() {
  return fetchAlertVkeFrontedWhiteList().then((res) => {
    alarmList.value = res
  })
}

function whiteCreate(source: AlertWhiteListItem) {
  return postAlertVkeFrontedWhite(source).then(() => {
    Object.assign(form, resetData)
    ElNotification({
      title: '成功',
      message: '创建成功',
      type: 'success',
    })
  }).catch((e) => {
    ElNotification.error({
      title: '错误',
      message: `创建失败：${e.message}`,
    })
    throw e
  })
}

function whiteDelete(row: AlertWhiteListItem) {
  if (!row.id) {
    return
  }
  deleteAlertVkeFrontedWhite(row.id).then(() => {
    ElNotification({
      title: '成功',
      message: '删除成功',
      type: 'success',
    })
    whiteListLoad()
  }).catch((e) => {
    ElNotification.error({
      title: '错误',
      message: `删除失败：${e.message}`,
    })
    throw e
  })
}

function whiteUpdate(row: AlertWhiteListItem) {
  return putAlertVkeFrontedWhite(row).then(() => {
    Object.assign(form, resetData)
    ElNotification({
      title: '成功',
      message: '更新成功',
      type: 'success',
    })
  }).catch((e) => {
    ElNotification.error({
      title: '错误',
      message: `更新失败：${e.message}`,
    })
    throw e
  })
}

function handleEdit(alarm: AlertWhiteListItem) {
  whiteListString.value = alarm.whiteList.join(';')
  Object.assign(form, alarm)
  form.status = alarm.status
  dialogFormVisible.value = true
}

function handleDelete(row: AlertWhiteListItem) {
  ElMessageBox.confirm('确认删除？')
    .then(() => {
      whiteDelete(row)
    })
    .catch(() => { })
}

function changeWhiteList() {
  form.whiteList = whiteListString.value.split(';')
}

const formRef = ref()

function handleSure() {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      form.status = Number(form.status)
      form.authorName = userStore.userInfo.name
      form.id = Number(form.id)
      const action = form.id ? whiteUpdate : whiteCreate
      action(form).then(() => {
        whiteListLoad()
        dialogFormVisible.value = false
      })
    }
  })
}

function handleChangeStatus(row: AlertWhiteListItem) {
  whiteUpdate({
    ...row,
    status: row.status === 1 ? 2 : 1,
  }).then(() => {
    row.status = row.status === 1 ? 2 : 1
  })
}
</script>

<template>
  <div class="app-container">
    <el-button type="primary" plain @click="dialogFormVisible = true">
      添加白名单
    </el-button>
    <el-table :data="alarmList" style="width: 100%">
      <el-table-column label="appKey" prop="appKey" />
      <el-table-column label="页面模块" prop="module" />
      <el-table-column label="创建人" prop="authorName" />
      <el-table-column label="异常分类" prop="category" />
      <el-table-column label="具体信息" prop="whiteList" />
      <el-table-column label="开启状态">
        <template #default="scope">
          <el-switch
            :model-value="scope.row.status"
            :active-value="1"
            :inactive-value="2"
            @change="handleChangeStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column align="right">
        <template #default="scope">
          <el-button @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
      <el-dialog v-model="dialogFormVisible" append-to-body :close-on-click-modal="false" title="添加白名单">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-form-item label="是否开启" :label-width="formLabelWidth">
            <el-switch
              v-model="form.status"
              :active-value="1"
              :inactive-value="2"
            />
          </el-form-item>
          <el-form-item label="项目appKey" :label-width="formLabelWidth" prop="appKey">
            <el-input v-model="form.appKey" placeholder="接入奥创时填写的项目标识" autocomplete="off" />
          </el-form-item>
          <el-form-item label="页面模块" :label-width="formLabelWidth" prop="module">
            <el-input v-model="form.module" placeholder="如：/study-app/cosplay" autocomplete="off" />
          </el-form-item>
          <el-form-item label="异常分类" :label-width="formLabelWidth" prop="category">
            <el-select v-model="form.category" placeholder="请选择">
              <el-option label="jsError" value="jsError" />
              <el-option label="resourceError" value="resourceError" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="form.category !== 'blankScreen'"
            label="具体信息"
            :label-width="formLabelWidth"
            prop="whiteListString"
          >
            <el-input
              v-model="whiteListString"
              placeholder="需要录入多个白名单就用英文逗号；隔开。比如：Script error.,Uncaught TypeError: Cannot read properties of undefined (reading 'nodeName')"
              autocomplete="off"
              @input="changeWhiteList"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogFormVisible = false">
              取 消
            </el-button>
            <el-button type="primary" @click="handleSure">
              确 定
            </el-button>
          </div>
        </template>
      </el-dialog>
    </el-table>
  </div>
</template>

<route lang="yaml">
meta:
  menu:
    parentMenu:
      name: 奥创监控
      path: /monitor
      icon: i-solar-iphone-outline
      sort: 6
    name: 白名单
    sort: 2
    path: /monitor/whitelist
    icon: i-carbon-two-factor-authentication
</route>
