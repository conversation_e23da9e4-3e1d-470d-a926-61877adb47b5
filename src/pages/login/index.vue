<script setup lang="ts">
import { useUserStore } from '~/stores/user'
import { getFeishuAppId } from '~/utils/env'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

function login(code: string) {
  userStore.login(code).then(() => {
    const redirect = route.query.redirect as string
    router.push(redirect ? decodeURIComponent(redirect) : '/')
  })
}

if (route.query.code && route.query.state) {
  login(route.query.code as string)
}
else {
  const redirectUri = encodeURIComponent(window.location.href)
  const appId = getFeishuAppId()
  const state = '1'
  window.location.replace(`https://open.feishu.cn/open-apis/authen/v1/index?redirect_uri=${redirectUri}&app_id=${appId}&state=${state}`)
}
</script>

<template>
  <div v-loading="true" element-loading-text="登录中" class="h-screen" />
</template>

<route lang="yaml">
meta:
  layout: empty
</route>
