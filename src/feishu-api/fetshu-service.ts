import { Inject, Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import axios from 'axios'
import { ConfigService } from '@nestjs/config'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { Logger } from 'winston'
import { UpdateTemplateVars } from 'src/feishu-card/card'

export type SendMessageBody = {
  receive_id: string
  msg_type: 'text' | 'post' | 'interactive'
  content: Record<string, any> | TextContent
}

export type SendMessageBodyFromTemplateIdBody = {
  receive_id: string
  msg_type: 'text' | 'post' | 'interactive'
  template_id: string
  vars: UpdateTemplateVars | { content: string; cloudLink?: string }
}

interface TextContent {
  text: string
}

@Injectable()
export class FeishuApiService {
  public feishuAccessToken: {
    token: string
    expire: number
  }
  constructor(
    private readonly configService: ConfigService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger
  ) {
    this.feishuAccessToken = {
      token: '',
      expire: -1
    }
    this.getFeishuAccessToken()
  }

  /**
   * 获取飞书AccessToken
   * https://open.feishu.cn/api-explorer?apiName=app_access_token&from=op_doc_tab&project=auth&resource=auth&version=v3
   */
  async getFeishuAccessToken() {
    if (
      !this.feishuAccessToken.token ||
      dayjs().unix() - this.feishuAccessToken.expire > 30
    ) {
      const res = await axios.post<{
        app_access_token: string
        expire: number
      }>('https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal', {
        app_id: this.configService.get('appId'),
        app_secret: this.configService.get('appSecret')
      })
      if (res.status === 200) {
        const { expire, app_access_token } = res.data
        this.feishuAccessToken.expire = dayjs().unix() + expire
        this.feishuAccessToken.token = app_access_token
      }
    }
  }

  async getFeishuUserInfoFromCode(code: string) {
    await this.getFeishuAccessToken()
    const res = await axios.post<{
      code: number
      data: {
        avatar_thumb: string
        avatar_url: string
        name: string
        open_id: string
      }
    }>(
      'https://open.feishu.cn/open-apis/authen/v1/access_token',
      {
        code,
        grant_type: 'authorization_code',
        app_access_token: this.feishuAccessToken.token
      },
      {
        headers: {
          Authorization: `Bearer ${this.feishuAccessToken.token}`
        }
      }
    )
    if (res.status === 200 && res.data && res.data.data) {
      const { name, open_id, avatar_url } = res.data.data
      return {
        name,
        open_id,
        avatar_url
      }
    }
  }

  async sendMessage(
    receive_id_type: 'open_id' | 'user_id' | 'union_id' | 'email' | 'chat_id',
    data: SendMessageBody
  ) {
    await this.getFeishuAccessToken()
    try {
      const res = await axios.post(
        'https://open.feishu.cn/open-apis/im/v1/messages',
        {
          ...data,
          content: JSON.stringify(data.content)
        },
        {
          params: {
            receive_id_type
          },
          headers: {
            Authorization: `Bearer ${this.feishuAccessToken.token}`
          }
        }
      )
      return res
    } catch (e) {
      this.logger.error(
        `飞书消息发送失败-${JSON.stringify(
          data.content
        )}\nerror:${JSON.stringify(e)}`
      )
    }
  }

  async sendErrorMessageToUser(receive_id: string, content: string) {
    await this.getFeishuAccessToken()
    try {
      await axios.post(
        'https://open.feishu.cn/open-apis/im/v1/messages',
        {
          receive_id,
          msg_type: 'interactive',
          template_id: 'ctp_AAwf7geacNCN',
          content: JSON.stringify({
            type: 'template',
            data: {
              template_id: 'ctp_AAwf7geacNCN',
              template_variable: {
                content
              }
            }
          })
        },
        {
          params: {
            receive_id_type: 'open_id'
          },
          headers: {
            Authorization: `Bearer ${this.feishuAccessToken?.token}`
          }
        }
      )
    } catch (e) {
      this.logger.error(
        `飞书消息发送个人消息失败-${JSON.stringify({
          type: 'template'
        })}\nerror:${JSON.stringify(e)}`
      )
    }
  }

  async sendMessageFromTemplateId(
    receive_id_type: 'open_id' | 'user_id' | 'union_id' | 'email' | 'chat_id',
    {
      receive_id,
      vars,
      template_id,
      msg_type
    }: SendMessageBodyFromTemplateIdBody
  ) {
    await this.getFeishuAccessToken()
    try {
      await axios.post(
        'https://open.feishu.cn/open-apis/im/v1/messages',
        {
          receive_id,
          msg_type,
          content: JSON.stringify({
            type: 'template',
            data: {
              template_id,
              template_variable: vars
            }
          })
        },
        {
          params: {
            receive_id_type
          },
          headers: {
            Authorization: `Bearer ${this.feishuAccessToken?.token}`
          }
        }
      )
    } catch (e) {
      this.logger.error(
        `飞书消息发送失败-${JSON.stringify({
          type: 'template',
          data: {
            template_id,
            template_variable: vars
          }
        })}\nerror:${JSON.stringify(e)}`
      )
    }
  }

  async updateMessageFromTemplateId(
    message_id: string | number,
    template_id: string,
    vars: UpdateTemplateVars | { [key: string]: any }
  ) {
    await this.getFeishuAccessToken()
    try {
      await axios.patch(
        `https://open.feishu.cn/open-apis/im/v1/messages/${message_id}`,
        {
          content: JSON.stringify({
            type: 'template',
            data: {
              template_id,
              template_variable: vars
            }
          })
        },
        {
          headers: {
            Authorization: `Bearer ${this.feishuAccessToken?.token}`
          }
        }
      )
    } catch (e) {
      this.logger.error(
        `飞书消息发送失败-${JSON.stringify({
          type: 'template',
          data: {
            template_id,
            template_variable: vars
          }
        })}\nerror:${JSON.stringify(e)}`
      )
    }
  }

  async updateMessage(
    message_id: string | number,
    content: Record<string, any>
  ) {
    await this.getFeishuAccessToken()
    try {
      await axios.patch(
        `https://open.feishu.cn/open-apis/im/v1/messages/${message_id}`,
        {
          content: JSON.stringify(content)
        },
        {
          headers: {
            Authorization: `Bearer ${this.feishuAccessToken?.token}`
          }
        }
      )
    } catch (e) {
      this.logger.error(
        `飞书消息发送失败-${JSON.stringify(content)}\nerror:${JSON.stringify(
          e
        )}`
      )
    }
  }

  // 向多维表格添加记录
  async addRecordToBitable({ appToken, tableId, fields }) {
    await this.getFeishuAccessToken()
    try {
      await axios.post(
        `https://open.feishu.cn/open-apis/bitable/v1/apps/${appToken}/tables/${tableId}/records`,
        {
          fields
        },
        {
          headers: {
            Authorization: `Bearer ${this.feishuAccessToken?.token}`,
            'Content-Type': 'application/json'
          }
        }
      )
    } catch (e) {
      this.logger.error(
        `多维表格新增记录失败-${JSON.stringify(fields)}\nerror:${JSON.stringify(
          e
        )}`
      )
    }
  }
}
