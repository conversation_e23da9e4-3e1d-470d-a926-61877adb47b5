import Docker from 'dockerode'
import { PipeLineStage } from 'src/pipeline/entities/pipeline-stage.entity'
import stream from 'stream'
import * as shell from 'shelljs'

export async function asyncWaitForExec(
  container: Docker.Container,
  exec: Docker.Exec
) {
  const logStream = new stream.PassThrough()
  // 返回一个 Promise 以便等待脚本执行完成
  return new Promise((resolve, reject) => {
    // 开始执行命令
    exec.start({ hijack: true, stdin: true }, (err, stream) => {
      if (err) {
        console.error('脚本执行失败', err.message)
        reject(err)
      } else {
        container.modem.demuxStream(stream, logStream, logStream)
        logStream.on('data', (chunk) => {
          console.log(chunk.toString('utf8'))
        })
        stream?.on('end', async () => {
          console.log('脚本执行完成')
          // 获取执行的详细信息
          const execInfo = await exec.inspect()
          if (execInfo.ExitCode !== 0) {
            reject(false)
          }
          resolve(true) // 完成 Promise
        })
      }
    })
  })
}

export const getResultText = (pipeline_stage: PipeLineStage) => {
  const success = pipeline_stage.status === 'success'
  switch (pipeline_stage.stage) {
    case 'git-pull':
      return success ? 'git pull 拉取成功' : 'git pull 拉取失败'
    case 'install':
      return success ? 'install安装成功' : 'install安装失败'
    case 'build':
      return success ? 'build 构建成功' : 'build 构建失败'
    case 'docker-build':
      return success ? 'docker 镜像构建成功' : 'docker 镜像构建失败'
    case 'docker-push':
      return success ? 'docker 镜像 push成功' : 'docker 镜像 push失败'
  }
}

export const getStatusText = (content: string, success: boolean) => {
  return success ? `\x1B[32m${content}\x1B[0m` : `\x1B[31m${content}\x1B[0m`
}

export const getLogTitle = (pipeline_stage: PipeLineStage) => {
  const success = pipeline_stage.status === 'success'
  return getStatusText(getResultText(pipeline_stage), success)
}

export const getNewVersionTag = (
  version: string,
  type: string,
  branch: string
) => {
  const versionArr: any = version.split('.')
  if (type === 'bug') {
    versionArr[2] = parseInt(versionArr[2]) + 1
  } else {
    if (parseInt(versionArr[1]) >= 300) {
      const mainVersion = versionArr[0].replace(/[^\d]/g, '')
      versionArr[0] = `v${parseInt(mainVersion) + 1}`
      versionArr[1] = 0
      versionArr[2] = 0
    } else {
      versionArr[1] = parseInt(versionArr[1]) + 1
      versionArr[2] = 0
    }
  }
  version = versionArr.join('.')
  const jarvis = '_jarvis_'
  const tagName = `${version}${jarvis}${branch}`
  return tagName
}

const DOCKER_CONFIG_NEW = {
  CLIENT14: {
    IMAGE_NAME: 'docker.yc345.tv/7to12/jarvisbase', // 基础镜像名称
    IMAGE_TAG: '14.21.1' // tag 14.21.XX--node:14.21.3  pnpm:7.33.6
  },
  CLIENT16: {
    IMAGE_NAME: 'docker.yc345.tv/7to12/jarvisbase', // 基础镜像名称
    IMAGE_TAG: '16.15.1' // tag 16.15.XX --node:16.15.1 pnpm:8.12.1
  },
  CLIENT18: {
    IMAGE_NAME: 'docker.yc345.tv/7to12/jarvisbase', // 基础镜像名称
    IMAGE_TAG: '18.15.1' // tag 18.15.XX--node:18.15.0 pnpm:8.12.1
  },
  CLIENT22: {
    IMAGE_NAME: 'docker.yc345.tv/7to12/jarvisbase', // 基础镜像名称
    IMAGE_TAG: '22.12.4' // tag 22.12.XX--node:22.12.0 corepack:0.32.0
  }
}

// 根据项目node版本匹配对应的Docker基础镜像
export const getDockerBaseImage = (node: string | undefined) => {
  if (!node) {
    return DOCKER_CONFIG_NEW.CLIENT14
  }
  let mainVersion = '14'
  const versionSplit = node.split('.')
  if (versionSplit.length > 0) {
    mainVersion = versionSplit[0]
  }
  if (mainVersion === '16') {
    return DOCKER_CONFIG_NEW.CLIENT16
  }
  if (mainVersion === '18') {
    return DOCKER_CONFIG_NEW.CLIENT18
  }
  if (mainVersion === '22') {
    return DOCKER_CONFIG_NEW.CLIENT22
  }
  return DOCKER_CONFIG_NEW.CLIENT14
}

export const sendFeishuNotification = ({projectId, projectName, branch, tag, message}) => {
  let url = `https://jarvis.yc345.tv/pipeline/pipeline?projectId=${projectId}`
  if (process.env.NODE_ENV !== 'production') {
    url = `http://jarvis-test.yc345.tv/pipeline/pipeline?projectId=${projectId}`
    projectName = `${projectName}（测试，请忽略）`
    // return
  }
  const json = {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title: projectName,
          content: [
            [{
              tag: 'text',
              text: '分支：'
            }, {
              tag: 'text',
              text: branch
            }],
            [{
              tag: 'text',
              text: 'TAG：'
            }, {
              tag: 'text',
              text: tag || '无'
            }],
            [{
              tag: 'text',
              text: '构建结果：'
            }, {
              tag: 'text',
              text: message
            }],
            [{
              tag: 'text',
              text: '构建日志：'
            }, {
              tag: 'a',
              text: '点击查看',
              href: url
            }]
          ]
        }
      }
    }
  }
  shell.exec(
    `curl -X POST 'https://open.feishu.cn/open-apis/bot/v2/hook/04de7f05-2149-49ca-946b-5ed697c79892' -H 'content-type: application/json' -d '${JSON.stringify(
      json
    )}'`,
    { async: true },
  )
}
