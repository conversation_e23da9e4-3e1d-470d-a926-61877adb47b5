import { Modu<PERSON> } from '@nestjs/common'
import { PipelineService } from './pipeline.service'
import { DockerModule } from 'src/docker/docker.module'
import { PipelineController } from './pipeline.controller'
import { ProjectModule } from 'src/project/project.module'
import { GitlabModule } from 'src/gitlab/gitlab.module'
import { TypeOrmModule } from '@nestjs/typeorm'
import { PipeLine } from 'src/pipeline/entities/pipeline.entity'
import { PipeLineStage } from 'src/pipeline/entities/pipeline-stage.entity'
import { PipeLineLog } from 'src/pipeline/entities/pipeline-log.entity'
import { KuberModule } from 'src/kuber/kuber.module'

@Module({
  providers: [PipelineService],
  imports: [
    TypeOrmModule.forFeature([PipeLine, PipeLineStage, PipeLineLog]),
    DockerModule,
    ProjectModule,
    GitlabModule,
    KuberModule
  ],
  controllers: [PipelineController]
})
export class PipelineModule {}
