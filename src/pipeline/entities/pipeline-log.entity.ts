import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

@Entity()
export class PipeLineLog {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'uuid'
  })
  pipeline_id: string

  @Column({
    type: 'varchar'
  })
  log: string

  @CreateDateColumn({
    type: 'timestamp'
  })
  create_at: Date

  @UpdateDateColumn({
    type: 'timestamp'
  })
  update_at: Date
}
