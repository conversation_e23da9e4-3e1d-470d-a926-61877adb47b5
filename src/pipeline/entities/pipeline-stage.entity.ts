import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

export type PipelineStagesType =
  | 'git-pull'
  | 'install'
  | 'build'
  | 'docker-build'
  | 'docker-push'

@Entity()
export class PipeLineStage {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'uuid'
  })
  pipeline_id: string

  @Column({
    type: 'uuid'
  })
  project_id: string

  @Column({
    type: 'varchar'
  })
  stage: PipelineStagesType

  @Column({
    type: 'varchar',
    default: 'pending'
  })
  status: 'pending' | 'success' | 'fail'

  @Column({
    type: 'jsonb',
    nullable: true
  })
  job_data: Record<string, any>

  @Column({
    type: 'varchar'
  })
  container_id: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  exec_id: string

  @Column({
    type: 'uuid',
    nullable: true
  })
  user_id: string

  @Column({
    type: 'uuid',
    nullable: true
  })
  last_operator: string

  @Column({
    type: 'int',
    default: 0
  })
  count_time: number

  @CreateDateColumn({
    type: 'timestamp'
  })
  create_at: Date

  @UpdateDateColumn({
    type: 'timestamp'
  })
  update_at: Date
}
