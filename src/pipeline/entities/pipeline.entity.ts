import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

type PipelineStage = 'pending' | 'success' | 'fail' | 'stop'

@Entity()
export class PipeLine {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'varchar',
    default: 'pending'
  })
  state: PipelineStage

  @Column({
    type: 'uuid'
  })
  user_id: string

  @Column({
    type: 'uuid'
  })
  last_operator: string

  @Column({
    type: 'uuid'
  })
  project_id: string

  @Column({
    type: 'varchar'
  })
  container_id: string

  @Column({
    type: 'text',
    default: 'webpage'
  })
  source: string

  @Column({
    type: 'varchar',
    default: 'build'
  })
  type: string

  @Column({
    type: 'jsonb',
    nullable: true
  })
  custom_config: Record<string, any>

  @Column({
    type: 'varchar'
  })
  branch: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  tag: string

  @Column({
    type: 'varchar'
  })
  tag_type: string

  @Column({
    type: 'int',
    default: 0
  })
  count_time: number

  @CreateDateColumn({
    type: 'timestamp'
  })
  create_at: Date

  @UpdateDateColumn({
    type: 'timestamp'
  })
  update_at: Date
}
