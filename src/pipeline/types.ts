import { PipelineStagesType } from 'src/pipeline/entities/pipeline-stage.entity'

interface ProjectDetails {
  id: string
  name: string
  name_space: string
  address: string
  gitlab_id: string
  config: {
    nodeVesion: string
    masterBuild: string
    releaseBuild: string
    developBuild: string
  }
  [key: string]: any
}

export interface PipelineJob {
  project_data: ProjectDetails
  package_manager: 'pnpm' | 'npm' | 'yarn'
  branch: string
  tag_type: string
  pipeline_id: string
  pipeline_stage_id?: string
  pipeline_stage_exec_id?: string
  new_tag: string
  docker_image?: string
  stage: PipelineStagesType
}
