import { CreateNewPipelineDto, PipiesPagesDto } from './dto/pipeline.dto'
import { Body, Controller, Get, Post, Query, Request } from '@nestjs/common'
import { PipelineService } from 'src/pipeline/pipeline.service'
import { Public } from 'src/auth/jwt-meta'

@Controller('pipeline')
export class PipelineController {
  constructor(private readonly pipelineService: PipelineService) {}
  @Post()
  async createNewPipeline(
    @Request() req: any,
    @Body() body: CreateNewPipelineDto
  ) {
    if (req.user.id) {
      return this.pipelineService.createNewPipeline(body, req.user.id)
    }
  }

  @Get('/page')
  async getPipelinePages(@Query() query: PipiesPagesDto) {
    return this.pipelineService.getPipelinePages(query)
  }

  @Get('/stages')
  async getPipelineStages(@Query('pipelineId') pipelineId: string) {
    return this.pipelineService.getPipelineStages(pipelineId)
  }

  @Get('/log')
  async getPipelineLog(@Query('pipelineId') pipelineId: string) {
    return this.pipelineService.getPipelineLogs(pipelineId)
  }

  @Get('/stop')
  async stopPipeline(
    @Request() req: any,
    @Query('pipelineId') pipelineId: string
  ) {
    if (req.user.id) {
      return this.pipelineService.stopPipeline(pipelineId, req.user.id)
    }
  }
}
