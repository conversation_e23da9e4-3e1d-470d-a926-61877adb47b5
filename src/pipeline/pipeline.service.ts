import { CreateNewPipelineDto, PipiesPagesDto } from './dto/pipeline.dto'
import { Injectable } from '@nestjs/common'
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter'
import { InjectRepository } from '@nestjs/typeorm'
import dayjs from 'dayjs'
import { DockerService } from 'src/docker/docker.service'
import { PipeLineStage } from 'src/pipeline/entities/pipeline-stage.entity'
import { PipeLine } from 'src/pipeline/entities/pipeline.entity'
import { PipelineJob } from 'src/pipeline/types'
import { ProjectServer } from 'src/project/project.service'
import { FindOptionsWhere, Repository } from 'typeorm'
import fs from 'node:fs'
import { PipeLineLog } from 'src/pipeline/entities/pipeline-log.entity'
import {
  asyncWaitForExec,
  getLogTitle,
  getNewVersionTag,
  getDockerBaseImage,
  sendFeishuNotification,
  getResultText
} from 'src/pipeline/utils'
import { GitlabService } from 'src/gitlab/gitlab.service'
import Dockerode from 'dockerode'
import { KuberService } from 'src/kuber/kuber.service'
import path from 'node:path'
import { ConfigService } from '@nestjs/config'

@Injectable()
export class PipelineService {
  constructor(
    private readonly dockerService: DockerService,
    private readonly projectService: ProjectServer,
    @InjectRepository(PipeLine)
    public readonly pipielineRepository: Repository<PipeLine>,
    @InjectRepository(PipeLineStage)
    public readonly pipielineStageRepository: Repository<PipeLineStage>,
    @InjectRepository(PipeLineLog)
    public readonly pipelineLogRepository: Repository<PipeLineLog>,
    private readonly eventEmitter: EventEmitter2,
    private readonly gitlabService: GitlabService,
    private readonly kuberService: KuberService,
    private readonly configService: ConfigService
  ) {
    if (process.env.PIPE_LINE === 'true') {
      this.checkUnFinshedStage()
    }
  }

  async checkUnFinshedStage() {
    const stages = await this.pipielineStageRepository.find({
      where: {
        status: 'pending'
      }
    })
    stages.forEach(async (stage) => {
      const pipe_line = await this.pipielineRepository.findOne({
        where: {
          id: stage.pipeline_id
        }
      })
      if (pipe_line?.state === 'pending' && stage.job_data) {
        this.eventEmitter.emit('build.exec-check', stage.job_data)
      }
    })
  }

  async getPipelinePages(query: PipiesPagesDto) {
    const findQuery: FindOptionsWhere<PipeLine> = {
      project_id: query.projectId
    }
    const [pipelines, count] = await Promise.all([
      this.pipielineRepository.find({
        take: query.pageSize,
        skip: (query.page - 1) * query.pageSize,
        where: findQuery,
        order: {
          create_at: 'DESC'
        }
      }),
      this.pipielineRepository.count({
        where: findQuery
      })
    ])
    return {
      page: query.page,
      pageSize: query.pageSize,
      list: pipelines,
      total: count
    }
  }

  async stopPipeline(id: string, user_id: string) {
    const pipeline = await this.pipielineRepository.findOne({
      where: {
        id
      }
    })
    if (pipeline?.state === 'pending') {
      pipeline.state = 'stop'
      pipeline.last_operator = user_id
      await this.pipielineRepository.save(pipeline)
      await this.removePipeLineContainer(pipeline)
    }
  }

  async getPipelineStages(id: string) {
    return this.pipielineStageRepository.find({
      where: {
        pipeline_id: id
      },
      order: {
        create_at: 'ASC'
      }
    })
  }

  async getPipelineLogs(id: string) {
    return this.pipelineLogRepository.findOne({
      where: {
        pipeline_id: id
      }
    })
  }

  async createNewPipeline(data: CreateNewPipelineDto, user_id: string) {
    const project = await this.projectService.findProjectAndConfig(
      data.project_id
    )
    if (!project) {
      return
    }
    const baseDockerConfig = getDockerBaseImage(project.config?.nodeVesion)
    const image = `${baseDockerConfig.IMAGE_NAME}:${baseDockerConfig.IMAGE_TAG}`
    const container = await this.dockerService.createNewPipeContainer(image)
    const new_pipeline = this.pipielineRepository.create({
      project_id: data.project_id,
      branch: data.branch,
      container_id: container.id,
      tag_type: data.tag_type,
      user_id,
      last_operator: user_id
    })

    await this.pipielineRepository.save(new_pipeline)
    const job_data = {
      project_data: project,
      pipeline_id: new_pipeline.id,
      package_manager: data.package_manager,
      branch: data.branch,
      tag_type: data.tag_type
    }
    this.eventEmitter.emitAsync('build.git-pull', job_data).catch(async () => {
      new_pipeline.state = 'fail'
      await this.pipielineRepository.save(new_pipeline)
      await this.removePipeLineContainer(new_pipeline)
      sendFeishuNotification({
        projectId: data.project_id,
        projectName: project.name,
        branch: data.branch,
        tag: '',
        message: '拉取代码失败'
      })
    })
    return new_pipeline
  }

  async getPipeline(pipeline_id: string) {
    return this.pipielineRepository.findOne({
      where: {
        id: pipeline_id
      }
    })
  }

  async getPipelineStage(pipeline_stage_id: string) {
    return this.pipielineStageRepository.findOne({
      where: {
        id: pipeline_stage_id
      }
    })
  }

  async getPipelineAndContainer(pipeline_id: string) {
    const pipe_line = await this.getPipeline(pipeline_id)
    if (pipe_line?.state === 'pending') {
      const container = await this.dockerService.getContainer(
        pipe_line.container_id
      )
      return {
        container,
        pipe_line
      }
    }
    return {
      container: null,
      pipe_line: null
    }
  }

  async removePipeLineContainer(pipeline: PipeLine) {
    await this.dockerService.removeContainer(pipeline.container_id)
    const file_path = path.resolve(
      this.configService.get<string>('LOG_PATH')!,
      pipeline.container_id
    )
    const logs_file = fs.existsSync(file_path)
    if (logs_file) {
      fs.unlinkSync(file_path)
    }
  }

  async getPipelineLogFromFile(
    pipeline: PipeLine,
    pipeline_stage: PipeLineStage
  ) {
    const file_path = path.resolve(
      this.configService.get<string>('LOG_PATH')!,
      pipeline.container_id
    )
    if (fs.existsSync(file_path)) {
      const logs_text = fs.readFileSync(file_path, 'utf-8')
      const log = await this.pipelineLogRepository.findOne({
        where: {
          pipeline_id: pipeline.id
        }
      })
      const stageLog = getLogTitle(pipeline_stage)
      if (log) {
        log.log = `${logs_text}\n${stageLog}`
        await this.pipelineLogRepository.save(log)
      } else {
        const new_log = this.pipelineLogRepository.create({
          pipeline_id: pipeline.id,
          log: `${logs_text}\n${stageLog}`
        })
        await this.pipelineLogRepository.save(new_log)
      }
    }
  }

  @OnEvent('build.exec-check')
  async checkExec(payload: PipelineJob) {
    const timer = setInterval(
      async () => {
        const pipeLine = await this.getPipeline(payload.pipeline_id)
        if (pipeLine?.state !== 'pending') {
          clearInterval(timer)
          return
        }
        const pipeline_stage = await this.getPipelineStage(
          payload.pipeline_stage_id!
        )
        if (!pipeline_stage || pipeline_stage.status !== 'pending') {
          clearInterval(timer)
          return
        }
        const current_time = dayjs().unix()
        const result = await this.dockerService.getExecResult(
          pipeline_stage.exec_id
        )
        pipeLine.count_time = current_time - dayjs(pipeLine.create_at).unix()
        pipeline_stage.count_time =
          current_time - dayjs(pipeline_stage.create_at).unix()
        if (!result) {
          clearInterval(timer)
          pipeLine.state = 'fail'
          pipeline_stage.status = 'fail'
          await Promise.all([
            this.pipielineRepository.save(pipeLine),
            this.pipielineStageRepository.save(pipeline_stage)
          ])
          sendFeishuNotification({
            projectId: payload.project_data.id,
            projectName: payload.project_data.name,
            branch: payload.branch,
            tag: '',
            message: '构建失败'
          })
        } else if (result?.ExitCode === 0) {
          clearInterval(timer)
          pipeline_stage.status = 'success'
          await Promise.all([
            this.pipielineRepository.save(pipeLine),
            this.pipielineStageRepository.save(pipeline_stage)
          ])
          await this.getPipelineLogFromFile(pipeLine, pipeline_stage)
          let emit_name = ''
          switch (pipeline_stage.stage) {
            case 'git-pull':
              emit_name = 'build.install'
              break
            case 'install':
              emit_name = 'build.build'
              break
            case 'build':
              emit_name = 'build.docker-build'
              break
            case 'docker-build':
              emit_name = 'build.docker-push'
              break
          }
          this.eventEmitter.emitAsync(emit_name, payload).catch(async () => {
            pipeLine.state = 'fail'
            await this.pipielineRepository.save(pipeLine)
            await this.removePipeLineContainer(pipeLine)
            sendFeishuNotification({
              projectId: payload.project_data.id,
              projectName: payload.project_data.name,
              branch: payload.branch,
              tag: '',
              message: '构建失败'
            })
          })
        } else if (
          typeof result.ExitCode === 'number' &&
          result.ExitCode !== 0
        ) {
          clearInterval(timer)
          pipeline_stage.status = 'fail'
          pipeLine.state = 'fail'
          await Promise.all([
            this.pipielineRepository.save(pipeLine),
            this.pipielineStageRepository.save(pipeline_stage)
          ])
          await this.getPipelineLogFromFile(pipeLine, pipeline_stage)
          await this.removePipeLineContainer(pipeLine)
          sendFeishuNotification({
            projectId: payload.project_data.id,
            projectName: payload.project_data.name,
            branch: payload.branch,
            tag: '',
            message: getResultText(pipeline_stage)
          })
        }
      },
      payload.stage === 'install' || payload.stage === 'build' ? 5000 : 1000
    )
  }

  async createNewTag(
    container: Dockerode.Container,
    tag_type: string,
    gitlab_id: string,
    branch: string
  ) {
    let version = await this.gitlabService.getGitlabBuildTag(gitlab_id, branch)
    if (!version) {
      version = `v0.0.1_jarvis_${branch}`
    }
    const new_tag = getNewVersionTag(version, tag_type, branch)
    const push_tag_exec = await container.exec({
      Cmd: [
        '/bin/sh',
        '-c',
        `cd /app/fe && git tag ${new_tag} && git push origin ${new_tag}`
      ],
      AttachStdout: true,
      AttachStderr: true
    })
    await asyncWaitForExec(container, push_tag_exec)
    return new_tag
  }

  @OnEvent('build.git-pull', {
    suppressErrors: false
  })
  async gitPullWorker(payload: PipelineJob) {
    const { container, pipe_line } = await this.getPipelineAndContainer(
      payload.pipeline_id
    )
    if (container && pipe_line) {
      const has_stage = await this.pipielineStageRepository.findOne({
        where: {
          pipeline_id: pipe_line.id,
          stage: 'git-pull'
        }
      })
      if (has_stage) {
        return
      }
      const new_exec = await container.exec({
        Cmd: [
          '/bin/sh',
          '-c',
          `GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no" git clone --progress --branch ${pipe_line.branch} --depth 1 ${payload.project_data.address} fe >> /app/log/${container.id} 2>> /app/log/${container.id}`
        ],
        AttachStderr: true,
        AttachStdout: true
      })
      const new_stage = this.pipielineStageRepository.create({
        pipeline_id: pipe_line.id,
        project_id: payload.project_data.id,
        stage: 'git-pull',
        container_id: pipe_line.container_id,
        exec_id: new_exec.id
      })
      await this.pipielineStageRepository.save(new_stage)
      const new_payload = {
        ...payload,
        pipeline_stage_id: new_stage.id,
        pipeline_stage_exec_id: new_exec.id,
        stage: 'git-pull'
      }
      new_stage.job_data = new_payload
      await this.pipielineStageRepository.save(new_stage)
      await new_exec.start({
        hijack: false
      })
      this.eventEmitter.emit('build.exec-check', new_payload)
    }
  }

  @OnEvent('build.install', {
    promisify: true,
    suppressErrors: false
  })
  async installWorker(payload: PipelineJob) {
    const { container, pipe_line } = await this.getPipelineAndContainer(
      payload.pipeline_id
    )
    if (container && pipe_line) {
      const has_stage = await this.pipielineStageRepository.findOne({
        where: {
          pipeline_id: pipe_line.id,
          stage: 'install'
        }
      })
      if (has_stage) {
        return
      }
      let install_cmd = 'npm install --registry https://npm.yc345.tv'
      if (payload.package_manager === 'pnpm') {
        install_cmd =
          'pnpm install --no-frozen-lockfile --registry https://npm.yc345.tv'
      }
      if (payload.package_manager === 'yarn') {
        install_cmd = 'yarn'
      }
      const new_exec = await container.exec({
        WorkingDir: '/app/fe',
        Cmd: [
          '/bin/sh',
          '-c',
          `${install_cmd} >> /app/log/${container.id} 2>> /app/log/${container.id} >> /app/log/${container.id} 2>> /app/log/${container.id}`
        ],
        AttachStderr: true,
        AttachStdout: true
      })
      const new_stage = this.pipielineStageRepository.create({
        pipeline_id: pipe_line.id,
        project_id: payload.project_data.id,
        stage: 'install',
        container_id: pipe_line.container_id,
        exec_id: new_exec.id
      })

      await this.pipielineStageRepository.save(new_stage)
      const new_payload = {
        ...payload,
        pipeline_stage_id: new_stage.id,
        pipeline_stage_exec_id: new_exec.id,
        stage: 'install'
      }
      new_stage.job_data = new_payload
      await this.pipielineStageRepository.save(new_stage)
      await new_exec.start({
        hijack: false
      })
      this.eventEmitter.emit('build.exec-check', new_payload)
    }
  }

  @OnEvent('build.build', {
    promisify: true,
    suppressErrors: false
  })
  async buildWoker(payload: PipelineJob) {
    const { container, pipe_line } = await this.getPipelineAndContainer(
      payload.pipeline_id
    )
    if (container && pipe_line) {
      const has_stage = await this.pipielineStageRepository.findOne({
        where: {
          pipeline_id: pipe_line.id,
          stage: 'build'
        }
      })
      if (has_stage) {
        return
      }
      const build_cmd = payload.project_data.config[`${payload.branch}Build`]
      if (build_cmd) {
        const new_exec = await container.exec({
          WorkingDir: '/app/fe',
          Cmd: [
            '/bin/sh',
            '-c',
            `${build_cmd} >> /app/log/${container.id} 2>> /app/log/${container.id} >> /app/log/${container.id} 2>> /app/log/${container.id}`
          ],
          AttachStderr: true,
          AttachStdout: true
        })
        const new_stage = this.pipielineStageRepository.create({
          pipeline_id: pipe_line.id,
          project_id: payload.project_data.id,
          stage: 'build',
          container_id: pipe_line.container_id,
          exec_id: new_exec.id
        })
        await this.pipielineStageRepository.save(new_stage)
        const new_payload = {
          ...payload,
          pipeline_stage_id: new_stage.id,
          pipeline_stage_exec_id: new_exec.id,
          stage: 'build'
        }
        new_stage.job_data = new_payload
        await this.pipielineStageRepository.save(new_stage)
        await new_exec.start({
          hijack: false
        })
        this.eventEmitter.emit('build.exec-check', new_payload)
      }
    }
  }

  @OnEvent('build.docker-build', {
    promisify: true,
    suppressErrors: false
  })
  async dockerBuild(payload: PipelineJob) {
    const { container, pipe_line } = await this.getPipelineAndContainer(
      payload.pipeline_id
    )
    if (container && pipe_line) {
      let new_tag = ''
      try {
        new_tag = await this.createNewTag(
          container,
          payload.tag_type,
          payload.project_data.gitlab_id,
          payload.branch
        )
      } catch (e) {
        pipe_line.state = 'fail'
        await this.pipielineRepository.save(pipe_line)
        sendFeishuNotification({
          projectId: payload.project_data.id,
          projectName: payload.project_data.name,
          branch: payload.branch,
          tag: '',
          message: '创建tag失败'

        })
      }
      const docker_image = `docker.yc345.tv/${payload.project_data.name_space}/${payload.project_data.name}:${new_tag}`
      const new_exec = await container.exec({
        WorkingDir: '/app/fe',
        Cmd: [
          '/bin/sh',
          '-c',
          `docker build . -t ${docker_image}  >> /app/log/${container.id} 2>> /app/log/${container.id}`
        ],
        AttachStderr: true,
        AttachStdout: true
      })
      const new_stage = this.pipielineStageRepository.create({
        pipeline_id: pipe_line.id,
        project_id: payload.project_data.id,
        stage: 'docker-build',
        container_id: pipe_line.container_id,
        exec_id: new_exec.id
      })
      await this.pipielineStageRepository.save(new_stage)
      const new_payload = {
        ...payload,
        pipeline_stage_id: new_stage.id,
        pipeline_stage_exec_id: new_exec.id,
        docker_image,
        stage: 'docker-build',
        new_tag
      }
      new_stage.job_data = new_payload
      await this.pipielineStageRepository.save(new_stage)
      await new_exec.start({
        hijack: false
      })
      this.eventEmitter.emit('build.exec-check', new_payload)
    }
  }

  @OnEvent('build.docker-push', {
    promisify: true,
    suppressErrors: false
  })
  async dockerPush(payload: PipelineJob) {
    const { container, pipe_line } = await this.getPipelineAndContainer(
      payload.pipeline_id
    )
    if (container && pipe_line) {
      if (payload.docker_image) {
        const new_stage = this.pipielineStageRepository.create({
          pipeline_id: pipe_line.id,
          project_id: payload.project_data.id,
          stage: 'docker-push',
          container_id: pipe_line.container_id
        })
        await this.pipielineStageRepository.save(new_stage)
        const res = await this.dockerService.pushImage(payload.docker_image)
        const log = await this.pipelineLogRepository.findOne({
          where: {
            pipeline_id: pipe_line.id
          }
        })

        if (log) {
          if (res === true) {
            new_stage.status = 'success'
            const stageLog = getLogTitle(new_stage)
            log.log = `${log.log}\n${stageLog}`
            await this.removePipeLineContainer(pipe_line)
            await this.pipielineStageRepository.save(new_stage)
            try {
              if (payload.branch !== 'master') {
                await this.kuberService.patchPipeline(
                  payload.project_data.name,
                  payload.project_data.name_space,
                  payload.new_tag,
                  payload.branch
                )
                log.log = `${log.log}\nk8s集群项目容器推送成功`
                await this.pipelineLogRepository.save(log)
              }
              pipe_line.state = 'success'
              await this.pipielineRepository.save(pipe_line)
              sendFeishuNotification({
                projectId: payload.project_data.id,
                projectName: payload.project_data.name,
                branch: payload.branch,
                tag: payload.new_tag,
                message: '构建成功'
              })
            } catch (e) {
              log.log = `${log.log}\nk8s集群项目容器推送失败\n${e.message}`
              await this.pipelineLogRepository.save(log)
              pipe_line.state = 'fail'
              sendFeishuNotification({
                projectId: payload.project_data.id,
                projectName: payload.project_data.name,
                branch: payload.branch,
                tag: payload.new_tag,
                message: 'k8s集群项目容器推送失败'
              })
              await this.pipielineRepository.save(pipe_line)
            }
          } else {
            new_stage.status = 'fail'
            const stageLog = getLogTitle(new_stage)
            log.log = `${log.log}\n${stageLog}\n${res}`
            pipe_line.state = 'fail'
            await this.pipelineLogRepository.save(log)
            await this.pipielineRepository.save(pipe_line)
            sendFeishuNotification({
              projectId: payload.project_data.id,
              projectName: payload.project_data.name,
              branch: payload.branch,
              tag: payload.new_tag,
              message: 'docker push失败'
            })
          }
        }
      }
    }
  }
}
