import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsOptional, IsString } from 'class-validator'
import { PageBaseDto } from 'src/utils/pageBaseDto'

export class CreateNewPipelineDto {
  @IsString()
  branch: string

  @IsString()
  package_manager: 'npm' | 'yarn' | 'pnpm'

  @IsString()
  project_id: string

  @IsString()
  tag_type: 'feat' | 'debug'
}

export class PipiesPagesDto extends PageBaseDto {
  @ApiProperty({
    required: false,
    description: '项目ID'
  })
  @IsOptional()
  projectId: string
}
