import type { UserModule } from './types'
import { ID_INJECTION_KEY, ZINDEX_INJECTION_KEY } from 'element-plus'

import { setupLayouts } from 'virtual:generated-layouts'

import { ViteSSG } from 'vite-ssg'
import { routes } from 'vue-router/auto-routes'
import App from './App.vue'

import '@unocss/reset/tailwind.css'
import 'element-plus/dist/index.css'

import './styles/main.css'
import 'uno.css'

// import 'element-plus/dist/index.css'

// const app = createApp(App)

// https://github.com/antfu/vite-ssg
export const createApp = ViteSSG(
  App,
  {
    routes: setupLayouts(routes),
    base: import.meta.env.BASE_URL,
  },
  (ctx) => {
    Object.values(import.meta.glob<{ install: UserModule }>('./modules/*.ts', { eager: true }))
      .forEach(i => i.install?.(ctx))

    ctx.app.provide(ID_INJECTION_KEY, {
      prefix: 1024,
      current: 0,
    })
    ctx.app.provide(ZINDEX_INJECTION_KEY, { current: 0 })
  },
)
