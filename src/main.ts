import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { HttpExceptionFilter } from './global-filters/http-exception.filter'
import { ResponseInterceptor } from './global-filters/response.interceptor'
import { ValidationPipe } from '@nestjs/common'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'

async function bootstrap() {
  const app = await NestFactory.create(AppModule)
  app.useGlobalFilters(new HttpExceptionFilter())
  app.useGlobalInterceptors(new ResponseInterceptor())
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true
      },
      enableDebugMessages: true,
      stopAtFirstError: true
    })
  )
  app.enableCors({ origin: '*' })
  /**
   * swagger配置
   * 会自动在项目url中生成api文档
   * 地址为 项目地址/api
   * 本地为 http://localhost:8400/api
   */
  const config = new DocumentBuilder()
    .setTitle('JARVIS api')
    .setDescription('The JARVIS API description')
    .setVersion('1.0')
    .addTag('JARVIS')
    .build()
  const document = SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('api', app, document)
  // 线上环境端口：8899； 测试环境端口：8088
  const port = process.env.NODE_ENV === 'production' ? '8899' : '8088'
  await app.listen(port)
}
bootstrap()
