export const BelongsEnumsArray = [
  {
    label: '学习工具',
    value: '1'
  },

  {
    label: '商业化',
    value: '2'
  },

  {
    label: '入校',
    value: '3'
  },
  {
    label: '智能硬件',
    value: '4'
  },
  {
    label: '武汉电销',
    value: '5'
  },
  {
    label: 'App',
    value: '6'
  },
  {
    label: '运维',
    value: '7'
  },
  {
    label: '职教',
    value: '9'
  },
  {
    label: '前端技术栈',
    value: '10'
  },
  {
    label: '后端技术栈',
    value: '11'
  },
  {
    label: '测试技术栈',
    value: '12'
  },
  {
    label: '技术支撑',
    value: '13'
  },
  {
    label: '全部',
    value: '8'
  }
]

const learning_tools_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_b3199f5f68887e0dc65663e88bbc50f5'
const school_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_25b92f7cc1c2754019eb122f614e3778'
const business_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_05c2563755a0bd25eed06f19701e8476'

const wuhan_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_f195d9bd16e13f1b12120c4b5e795510'
const hardware_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_91df0b49d6f4dd6cef2f4ed9bd2cfe2e'

const zhongzhi_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_c968629509975648fbdb467fc69d1f00'

const jishuzhicheng_chat =
  process.env.NODE_ENV !== 'production'
    ? 'oc_a5f385ef52eea0ad201967934f32e2b4'
    : 'oc_8f8e37fb15350d7411b309e9b344f4fb'

export const getBelongsChatId = (belong: string) => {
  switch (belong) {
    case '1':
      return learning_tools_chat
    case '2':
      return business_chat
    case '3':
      return school_chat
    case '4':
      return hardware_chat
    case '5':
      return wuhan_chat
    case '9':
      return zhongzhi_chat
    case '13':
      return jishuzhicheng_chat
    default:
      return ''
  }
}

export const RoleEnumsArray = [
  {
    label: '前端开发',
    value: '1'
  },
  {
    label: '后端开发',
    value: '2'
  },
  {
    label: '安卓开发',
    value: '3'
  },
  {
    label: 'IOS开发',
    value: '4'
  },
  {
    label: '测试',
    value: '5'
  },
  {
    label: '运维',
    value: '6'
  }
]

export const LabelEnumsArray = [
  {
    label: '前端',
    value: '1'
  },
  {
    label: '后端',
    value: '2'
  },
  {
    label: 'Native',
    value: '3'
  },
  {
    label: '运营后台',
    value: '4'
  },
  {
    label: 'ToB',
    value: '5'
  },
  {
    label: 'ToC',
    value: '6'
  },
  {
    label: 'npm包',
    value: '7'
  }
]
