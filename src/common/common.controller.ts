import { Controller, Get } from '@nestjs/common'
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger'
import { BelongsEnumsArray, LabelEnumsArray, RoleEnumsArray } from './enum'

@Controller('config')
export class CommonConfigController {
  @Get('belongs')
  @ApiOperation({
    summary: '获取所有项目归属枚举值'
  })
  @ApiOkResponse({
    description: '项目归属枚举值',
    schema: {
      type: 'array',
      items: {
        properties: {
          label: {
            type: 'string',
            description: '枚举label',
            examples: ['学习工具前端', '入校前端', '...']
          },
          value: {
            type: 'number',
            description: '枚举值',
            examples: [1, 2, '...']
          }
        }
      }
    }
  })
  async getBelongs() {
    return BelongsEnumsArray
  }
  @ApiOperation({
    summary: '获取所有项目的label枚举值'
  })
  @Get('labels')
  async getLabels() {
    return LabelEnumsArray
  }
  @ApiOperation({
    summary: '获取所有用户role枚举'
  })
  @Get('roles')
  async getRoles() {
    return RoleEnumsArray
  }
}
