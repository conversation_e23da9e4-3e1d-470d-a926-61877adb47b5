import { Test, TestingModule } from '@nestjs/testing'
import { CommonConfigController } from './common.controller'

describe('CommonConfigController', () => {
  let controller: CommonConfigController

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommonConfigController]
    }).compile()

    controller = module.get<CommonConfigController>(CommonConfigController)
  })

  it('should be defined', () => {
    expect(controller).toBeDefined()
  })
})
