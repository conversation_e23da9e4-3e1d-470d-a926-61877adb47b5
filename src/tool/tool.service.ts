import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { CreateTool } from './dto/tool.dto'
import { Tool } from './tool.entity'
import { Injectable } from '@nestjs/common'

@Injectable()
export class ToolService {
  constructor(
    @InjectRepository(Tool)
    private readonly toolRepository: Repository<Tool>
  ) {}

  create(createTool: CreateTool) {
    const tool = new Tool()
    tool.name = createTool.name
    tool.creator = createTool.creator
    tool.config = createTool.config
    tool.repository = createTool.repository
    return this.toolRepository.save(tool)
  }

  async findAll() {
    return await this.toolRepository.find()
  }

  /**
   * 根据id获取单个项目详情
   * @param id
   * @returns
   */
  async findOne(id: string) {
    return this.toolRepository.findOne({
      where: {
        id: Number(id)
      }
    })
  }

  findOneByNameAndRepository(name: string) {
    return this.toolRepository.findOne({
      where: {
        name: name
      }
    })
  }

  async remove(id: string) {
    await this.toolRepository.delete(id)
  }

  async upDateConfig(id: string, config: { [key: string]: any }) {
    await this.toolRepository.update(id, { ...config })
  }
}
