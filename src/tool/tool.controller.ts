import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query
} from '@nestjs/common'
import { CreateTool } from './dto/tool.dto'
import { ToolService } from './tool.service'
import { ApiOperation, ApiTags } from '@nestjs/swagger'

@ApiTags('工具集')
@Controller('tool')
export class ToolController {
  constructor(private readonly toolService: ToolService) {}

  @Post()
  @ApiOperation({
    summary: '创建工具集'
  })
  async create(@Body() createTool: CreateTool) {
    const { id, name, repository, config, creator } = createTool
    const updated_at = new Date()
    if (id) {
      const project = await this.toolService.findOne(id)
      if (project) {
        return this.toolService.upDateConfig(id, {
          name,
          repository,
          config,
          creator,
          updated_at
        })
      }
    } else {
      return await this.toolService.create({
        name,
        repository,
        config,
        creator
      })
    }
  }

  @Get()
  @ApiOperation({
    summary: '获取所有项目数据'
  })
  async findAll() {
    return this.toolService.findAll()
  }

  @Get(':id')
  @ApiOperation({
    summary: '根据Id获取工具集配置'
  })
  async findOne(@Param('id') id: string) {
    return this.toolService.findOne(id)
  }
  // /tool/delete?id=2
  @Delete('delete')
  @ApiOperation({
    summary: '删除单个工具集'
  })
  async remove(@Query('id') id: string) {
    this.toolService.remove(id)
  }
}
