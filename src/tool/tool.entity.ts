import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn
} from 'typeorm'

@Entity()
export class Tool {
  @PrimaryGeneratedColumn()
  id: number

  @Column({
    nullable: true,
    comment: '工具集名称',
    name: 'name'
  })
  name?: string

  @Column({
    nullable: true,
    comment: '创建人',
    name: 'creator'
  })
  creator?: string

  @Column({
    nullable: true,
    comment: '仓库地址',
    name: 'repository'
  })
  repository?: string

  @Column({
    type: 'json',
    default: {}
  })
  config: unknown

  @CreateDateColumn({ name: 'created_at', type: 'timestamp with time zone' })
  created_at: Date

  @CreateDateColumn({ name: 'updated_at', type: 'timestamp with time zone' })
  updated_at: Date
}
