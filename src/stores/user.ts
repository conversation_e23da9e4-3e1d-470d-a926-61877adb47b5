import type { UserInfoType } from '~/apis/user'
import { acceptHMRUpdate, defineStore } from 'pinia'
import { fetchUserInfo, login as loginApi } from '~/apis/user'
import { getToken, removeToken, setToken } from '~/helpers/auth'

export const useUserStore = defineStore('user', () => {
  const token = ref<string>(getToken())
  const user = ref<UserInfoType>()
  interface UserInfo {
    id: string
    name: string
    avatar: string
    phone: string
  }
  const userInfo = reactive<UserInfo>({
    id: '',
    name: '',
    avatar: '',
    phone: '',
  })

  async function login(code: string) {
    const res = await loginApi(code)
    const { token: apiToken } = res
    token.value = apiToken
    setToken(apiToken)
  }

  async function getUserInfo() {
    const res = await fetchUserInfo()
    const { id, name, avatar_url, phone } = res
    userInfo.id = id
    userInfo.name = name
    userInfo.avatar = avatar_url
    userInfo.phone = phone
    user.value = res
  }

  function logout() {
    token.value = ''
    Object.keys(userInfo).forEach((key) => {
      userInfo[key as keyof UserInfo] = ''
    })
    removeToken()
  }

  return {
    token,
    user,
    userInfo,
    login,
    logout,
    getUserInfo,
  }
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useUserStore as any, import.meta.hot))
