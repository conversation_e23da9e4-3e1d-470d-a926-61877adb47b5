import { Injectable } from '@nestjs/common'
import axios from 'axios'

@Injectable()
export class SonicService {
  async getProjects() {
    const res = await axios.get('https://ycapi.yc345.tv/api/sonicProject')
    return res.data
  }

  async getCaseByName(name: string) {
    const res = await axios.get('https://ycapi.yc345.tv/api/sonicCaseSuite', {
      params: {
        name
      }
    })
    return res.data
  }

  async runSonicCase(body: Record<string, any>) {
    return axios.post('https://ycapi.yc345.tv/api/runSonicCase/', body)
  }
}
