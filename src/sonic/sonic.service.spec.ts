import { Test, TestingModule } from '@nestjs/testing';
import { SonicService } from './sonic.service';

describe('SonicService', () => {
  let service: SonicService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SonicService],
    }).compile();

    service = module.get<SonicService>(SonicService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
