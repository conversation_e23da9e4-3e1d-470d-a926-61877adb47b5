import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON>Controller } from './sonic.controller';

describe('SonicController', () => {
  let controller: SonicController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SonicController],
    }).compile();

    controller = module.get<SonicController>(SonicController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
