import { Controller, Get, Query } from '@nestjs/common'
import { SonicService } from 'src/sonic/sonic.service'
import { Public } from 'src/auth/jwt-meta'

@Controller('sonic')
export class SonicController {
  constructor(private readonly sonicService: SonicService) {}
  @Public()
  @Get('/sonicProject')
  async getProjects() {
    return this.sonicService.getProjects()
  }
  @Public()
  @Get('/sonicCaseSuite')
  async getCaseByName(@Query('name') name: string) {
    return this.sonicService.getCaseByName(name)
  }
}
