import { Inject, Injectable } from '@nestjs/common'
import * as k8s from '@kubernetes/client-node'
import type { KubeConfig, AppsV1Api } from '@kubernetes/client-node'
import { InjectRepository } from '@nestjs/typeorm'
import { PublishersKuberJob } from 'src/kuber/entities/publisher-kuber-job.entity'
import { Repository } from 'typeorm'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { Logger } from 'winston'

const configs = {
  test: {
    cluster: {
      server: 'https://localk8s.yc345.tv:6443',
      name: 'test-cluster',
      skipTLSVerify: true
    },
    context: {
      name: 'testenv',
      user: 'gitlab-ci',
      cluster: 'test-cluster'
    },
    user: {
      name: 'gitlab-ci',
      token:
        'eyJhbGciOiJSUzI1NiIsImtpZCI6Ijh4ZnhZLVYzbE8yNExzc0ZaS1ZaZEJxaDJWd0ttTVVKTFo2d2NIM3lYUEEifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Opw5qjA3eApZanQgVVLaKqdtcrQhtlAjk66xEEPIfXZ4jS94ZJmZ7wSwwfzQR5CkcilAc4psWsp6MjVMbEBDbxK0iNRbKKi6S6clMqG9pRTK2Dj3tuGOLCisgM4tXGBZJYeBamUvfHfQ4-zZ2YE-nseoFmbcFr7IraQJbRyugDa2mkQJepKjVuGkAWgLUgkYhJZp4xPL_dYLfzXuJNUa6KSLjnpDKlb7VOpUCu4JrCSnt-YTYtkJJxrEU9L-YfsuBfOziU06ybJKRpS6dH-Y_04V8Cc3J8jNK3rCHQH5eo4iunmGWbNXWW0lPmRyBG_tQLfi8hqPAsykoG1fbRCMkw'
    }
  },
  stage: {
    cluster: {
      server: 'https://*************:6443',
      name: 'stage-cluster',
      skipTLSVerify: true
    },
    context: {
      name: 'stageenv',
      user: 'gitlab-ci',
      cluster: 'stage-cluster'
    },
    user: {
      name: 'gitlab-ci',
      token:
        'eyJhbGciOiJSUzI1NiIsImtpZCI6ImFzZzZsRVV2YjZrUGRNM1M2ZU84ZnlkNjV2b2tPc0tSMlpWWU00WjQ1SEEifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.E3uOnYrffNboZKqqAo2tE1YmTeMqoBst9zFu5uOmBrnuCOTRujdM1fHOwNqKQvHfqepUUotJVCFfSm2OLLV91Q9z_P7OsPyz_vLJ8yNWzS2RCCVMy2oGu-tmf1WEWaoh7gcqCOV_vhHoNmXdnYS5B2TfvcQicZRPB423MmfweXpI2dvvsIB6AlsZISW26gUxDbkKBcRVgZvPw8oNSX2XMHhP3EfE6VXEveiSqVw1zuzMXn5Nq-y23M698d7kr8fUbf--VtuCHM1QfKVwm_toPzgwiVd4ExHHuNxiBAhCKAOPM2BvlNk2rJWo1bB9PqkuJOmuskMPwHWbdFzdIa0e6Q'
    }
  },
  prod: {
    cluster: {
      server: 'https://*************:6443',
      name: 'prod-cluster',
      skipTLSVerify: true
    },
    context: {
      name: 'prodenv',
      user: 'gitlab-ci',
      cluster: 'prod-cluster'
    },
    user: {
      name: 'gitlab-ci',
      token:
        '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    }
  }
}

@Injectable()
export class KuberService {
  public kc: KubeConfig
  public k8sAppV1Api: AppsV1Api
  public testK8sAppV1Api: AppsV1Api
  public stageK8sAppV1Api: AppsV1Api
  constructor(
    @InjectRepository(PublishersKuberJob)
    public readonly publishersKuberJobRepository: Repository<PublishersKuberJob>,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger
  ) {
    this.init()
    this.getPipelineKc()
  }
  async init() {
    const kc = new k8s.KubeConfig()
    const config =
      process.env.NODE_ENV === 'production' ? configs.prod : configs.test

    kc.loadFromOptions({
      clusters: [config.cluster],
      users: [config.user],
      contexts: [config.context],
      currentContext: config.context.name
    })
    this.kc = kc
    this.k8sAppV1Api = kc.makeApiClient(k8s.AppsV1Api)
  }

  async getPipelineKc() {
    const test_env_kc = new k8s.KubeConfig()
    const config = configs.test
    test_env_kc.loadFromOptions({
      clusters: [config.cluster],
      users: [config.user],
      contexts: [config.context],
      currentContext: config.context.name
    })
    this.testK8sAppV1Api = test_env_kc.makeApiClient(k8s.AppsV1Api)
    const stage_env_kc = new k8s.KubeConfig()
    const stage_config = configs.stage
    stage_env_kc.loadFromOptions({
      clusters: [stage_config.cluster],
      users: [stage_config.user],
      contexts: [stage_config.context],
      currentContext: stage_config.context.name
    })
    this.stageK8sAppV1Api = stage_env_kc.makeApiClient(k8s.AppsV1Api)
  }

  async patchPipeline(
    name: string,
    name_space: string,
    tag: string,
    branch: string
  ) {
    if (branch === 'develop') {
      return this.patchTestK8sPipeline(name, name_space, tag)
    }
    if (branch === 'release') {
      return this.patchStageK8sPipeline(name, name_space, tag)
    }
  }

  async patchTestK8sPipeline(name: string, name_space: string, tag: string) {
    try {
      const deployment = await this.testK8sAppV1Api.readNamespacedDeployment(
        name,
        name_space
      )
      const container = deployment.body.spec?.template.spec?.containers.find(
        (el) => el.image?.includes(name) || el.name === name
      )
      if (!container) {
        throw new Error(`${name} 未找到对应deployment项目镜像,k8s镜像更新失败`)
      }
      const imagePrefix = container.image?.split(':')[0]
      const newImage = `${imagePrefix}:${tag}`
      container.image = newImage
      const res = await this.testK8sAppV1Api.patchNamespacedDeployment(
        name,
        name_space,
        {
          spec: {
            template: {
              spec: {
                containers: [
                  {
                    name: container.name,
                    image: newImage
                  }
                ]
              }
            }
          }
        },
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        {
          headers: {
            'Content-Type': k8s.PatchUtils.PATCH_FORMAT_STRATEGIC_MERGE_PATCH
          }
        }
      )
      return res
    } catch (e) {
      throw new Error(`${name} k8s镜像更新失败`)
    }
  }

  async patchStageK8sPipeline(name: string, name_space: string, tag: string) {
    try {
      const deployment = await this.stageK8sAppV1Api.readNamespacedDeployment(
        name,
        name_space
      )
      const container = deployment.body.spec?.template.spec?.containers.find(
        (el) => el.image?.includes(name) || el.name === name
      )
      if (!container) {
        throw new Error(`${name} 未找到对应deployment项目镜像,k8s镜像更新失败`)
      }
      const imagePrefix = container.image?.split(':')[0]
      const newImage = `${imagePrefix}:${tag}`
      container.image = newImage
      const res = await this.stageK8sAppV1Api.patchNamespacedDeployment(
        name,
        name_space,
        {
          spec: {
            template: {
              spec: {
                containers: [
                  {
                    name: container.name,
                    image: newImage
                  }
                ]
              }
            }
          }
        },
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        {
          headers: {
            'Content-Type': k8s.PatchUtils.PATCH_FORMAT_STRATEGIC_MERGE_PATCH
          }
        }
      )
      return res
    } catch (e) {
      throw new Error(`${name} k8s镜像更新失败`)
    }
  }

  async getDeployment(name: string, nameSpace: string) {
    return this.k8sAppV1Api.readNamespacedDeployment(name, nameSpace)
  }

  async getDeploymentTagsHistory(name: string, name_space: string) {
    try {
      const deploymentRes = await this.getDeployment(name, name_space)
      const deployment = deploymentRes.body
      const labelSelector = await this.getDeploymentSelectLabel(
        name,
        name_space
      )
      if (!labelSelector) {
        return []
      }
      if (!deployment.metadata?.namespace) {
        return []
      }
      const replicaSets = await this.k8sAppV1Api.listNamespacedReplicaSet(
        deployment.metadata.namespace,
        undefined,
        undefined,
        undefined,
        undefined,
        labelSelector
      )
      const items = replicaSets.body.items.sort((a, b) => {
        return (
          a.metadata!.creationTimestamp!.valueOf() -
          b.metadata!.creationTimestamp!.valueOf()
        )
      })
      if (!items) {
        return []
      }

      const tags = items
        .slice(-11, -1)
        .reverse()
        .map((item) => {
          const container = item.spec?.template?.spec?.containers.find(
            (container) => container.image?.includes(name)
          )
          if (container) {
            const tag = container.image?.split(':')?.[1]
            return tag
          }
          return undefined
        })
      return tags.filter((el) => el)
    } catch (e) {
      this.logger.error(
        `获取历史tags失败 - ${name}:${name_space}\nerror:${JSON.stringify(e)}`
      )
      return []
    }
  }

  async getDeploymentSelectLabel(name: string, nameSpace: string) {
    const deployment = await this.getDeployment(name, nameSpace)
    const labels = deployment.body.metadata?.labels
    if (labels?.app) {
      return `app=${labels['app']}`
    }
  }
}
