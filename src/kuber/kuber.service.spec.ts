import { Test, TestingModule } from '@nestjs/testing';
import { KuberService } from './kuber.service';

describe('KuberService', () => {
  let service: KuberService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [KuberService],
    }).compile();

    service = module.get<KuberService>(KuberService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
