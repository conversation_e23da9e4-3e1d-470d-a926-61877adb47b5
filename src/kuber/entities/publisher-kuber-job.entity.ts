import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn
} from 'typeorm'

export type KuberJobStatus = 'pending' | 'success' | 'fail'

export type KuberJobType = 'deployed' | 'rolledBack'

export type Deployments = {
  meta_name: string
  meta_namespace: string
  replicas: number
  generation: number
}

@Entity()
export class PublishersKuberJob {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'uuid'
  })
  project_id: string

  @Column({
    type: 'varchar'
  })
  name: string

  @Column({
    type: 'varchar'
  })
  meta_name: string

  @Column({
    type: 'varchar'
  })
  meta_namespace: string

  @Column({
    type: 'varchar'
  })
  tag: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  roll_back_tag: string

  @Column({
    type: 'int',
    default: 0
  })
  replicas: number

  @Column({
    type: 'int',
    default: 0
  })
  generation: number

  @Column({
    type: 'int',
    default: 0
  })
  check_time: number

  @Column({
    type: 'varchar',
    default: '',
    nullable: true
  })
  sql: string

  @Column({
    type: 'varchar',
    default: ''
  })
  receive_id: string

  @Column({
    type: 'varchar',
    default: ''
  })
  publisher_id: string

  @Column({
    type: 'varchar',
    default: ''
  })
  developer_name: string

  @Column({
    type: 'varchar',
    default: ''
  })
  developer_feishuId: string

  @Column({
    type: 'varchar',
    default: ''
  })
  tester_name: string

  @Column({
    type: 'varchar',
    default: ''
  })
  tester_feishuId: string

  @Column({
    type: 'varchar',
    default: ''
  })
  deployer_name: string

  @Column({
    type: 'varchar',
    default: ''
  })
  deployer_feishuId: string
  @Column({
    type: 'varchar',
    default: ''
  })
  content: string

  @Column({
    type: 'varchar',
    default: 'pending'
  })
  status: KuberJobStatus

  @Column({
    type: 'varchar',
    default: ''
  })
  belong: string

  @Column({
    type: 'varchar',
    default: ''
  })
  trigger: KuberJobType

  @Column({
    type: 'varchar',
    nullable: true
  })
  remark: string

  @Column({
    type: 'jsonb',
    nullable: true
  })
  deployments: Deployments[]

  @CreateDateColumn({
    type: 'timestamp',
    nullable: true
  })
  create_time: Date
  @UpdateDateColumn({
    type: 'timestamp',
    nullable: true
  })
  update_time: Date
}
