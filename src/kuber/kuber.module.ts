import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { FeishuApiModule } from 'src/feishu-api/feishu-module'
import { PublishersKuberJob } from 'src/kuber/entities/publisher-kuber-job.entity'
import { KuberService } from 'src/kuber/kuber.service'
import { KuberWorkerService } from 'src/kuber/kuber.worker'
import { ProjectModule } from 'src/project/project.module'
import { UserModule } from 'src/user/user.module'

@Module({
  imports: [
    TypeOrmModule.forFeature([PublishersKuberJob]),
    FeishuApiModule,
    UserModule,
    ProjectModule
  ],
  controllers: [],
  providers: [KuberService, KuberWorkerService],
  exports: [KuberService]
})
export class KuberModule {}
