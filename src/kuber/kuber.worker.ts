import { Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { <PERSON>ron } from '@nestjs/schedule'
import { InjectRepository } from '@nestjs/typeorm'
import { PublishersKuberJob } from 'src/kuber/entities/publisher-kuber-job.entity'
import { Repository } from 'typeorm'
import { FeishuApiService } from 'src/feishu-api/fetshu-service'
import { UserService } from 'src/user/user.service'
import { BelongsEnumsArray, getBelongsChatId } from 'src/common/enum'
import { KuberService } from 'src/kuber/kuber.service'
import { ProjectServer } from 'src/project/project.service'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { Logger } from 'winston'
import type { V1DeploymentStatus, V1Deployment } from '@kubernetes/client-node'
import {
  UpdateTemplateVars,
  deploy_rollback,
  deploy_rollback_success,
  deploy_success,
  deploy_success_group
} from 'src/feishu-card/card'

@Injectable()
export class KuberWorkerService {
  constructor(
    @InjectRepository(PublishersKuberJob)
    private readonly publishersKuberJobRepository: Repository<PublishersKuberJob>,
    private readonly feishuService: FeishuApiService,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly kuberService: KuberService,
    private readonly projectService: ProjectServer,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger
  ) {}

  @Cron('*/10 * * * * *')
  async deploymentWatchCronJob() {
    if (!process.env.PIPE_LINE || process.env.PIPE_LINE !== 'true') {
      return
    }
    const jobs = await this.publishersKuberJobRepository.find({
      where: {
        status: 'pending'
      }
    })
    if (jobs.length) {
      jobs.forEach((job) => {
        this.checkDeploymentJob(job)
      })
    }
  }

  async checkDeployment(
    name: string,
    name_space: string,
    replicas: number,
    generation: number
  ) {
    const curDeployment = await this.kuberService.getDeployment(
      name,
      name_space
    )
    const curStatus = curDeployment.body.status!
    const isSuccess =
      curStatus?.updatedReplicas === replicas &&
      curStatus?.replicas === replicas &&
      curStatus?.availableReplicas === replicas &&
      curStatus!.observedGeneration! >= generation
    return {
      isSuccess,
      curStatus,
      deployment: curDeployment
    }
  }

  async checkDeploymentJob(job: PublishersKuberJob) {
    let res: {
      isSuccess: boolean
      curStatus: V1DeploymentStatus
      deployment: {
        body: V1Deployment
      }
    }[] = []
    if (job.deployments && job.deployments.length) {
      res = await Promise.all(
        job.deployments.map((deployment) => {
          return this.checkDeployment(
            deployment.meta_name,
            deployment.meta_namespace,
            deployment.replicas,
            deployment.generation
          )
        })
      )
    } else {
      res = await Promise.all([
        this.checkDeployment(
          job.meta_name,
          job.meta_namespace,
          job.replicas,
          job.generation
        )
      ])
    }
    const isSuccess = !res.find((el) => !el.isSuccess)
    const type = job.trigger === 'deployed' ? '上线' : '回滚'
    try {
      const config = await this.projectService.projectConfigEntity.findOneBy({
        id: job.project_id
      })
      if (!config) {
        return
      }
      if (!isSuccess) {
        if (job.check_time < 51) {
          await this.publishersKuberJobRepository.update(job.id, {
            check_time: job.check_time + 1
          })
          res.forEach((el) => {
            this.feishuService.sendMessage('open_id', {
              receive_id: job.deployer_feishuId,
              msg_type: 'text',
              content: {
                text: `${type}检查：${el.deployment.body.metadata?.name}-${
                  job.tag
                }正在进行第${job.check_time + 1}检查，${JSON.stringify(
                  el.curStatus.conditions?.[el.curStatus.conditions.length - 1]
                )}`
              }
            })
          })
        } else {
          await this.publishersKuberJobRepository.update(job.id, {
            status: 'fail'
          })
          res.forEach((el) => {
            this.feishuService.sendMessageFromTemplateId('open_id', {
              receive_id: job.deployer_feishuId,
              msg_type: 'interactive',
              template_id: 'ctp_AAmcJpbKIIfY',
              vars: {
                content: `${el.deployment.body.metadata?.name}-${
                  job.tag
                }${type}失败,${JSON.stringify(
                  el.curStatus.conditions?.[el.curStatus.conditions.length - 1]
                )}`,
                cloudLink: `https://console.volcengine.com/vke/region:vke+cn-beijing/cluster/ccdrjoqnqtofnmccjjj1g/deployment/${el.deployment.body?.metadata?.namespace}/${el.deployment.body?.metadata?.name}/detail?tab=pods`
              }
            })
          })
        }
      } else {
        await this.publishersKuberJobRepository.update(job.id, {
          status: 'success'
        })
        res.forEach((el) => {
          this.feishuService.sendMessageFromTemplateId('open_id', {
            receive_id: job.deployer_feishuId,
            msg_type: 'interactive',
            template_id: 'ctp_AAmcZqyblZ11',
            vars: {
              content: `${el.deployment.body.metadata?.name}-${
                job.tag
              }${type}成功,${JSON.stringify(
                el.curStatus.conditions?.[el.curStatus.conditions.length - 1]
              )}`
            }
          })
        })
        const vars: UpdateTemplateVars = {
          developerName: job.developer_name,
          developerFeishuId: job.developer_feishuId,
          deployerName: job.deployer_name,
          testerName: job.tester_name,
          testerFeishuId: job.tester_feishuId,
          name: job.name,
          tag: job.tag,
          sql: job.sql,
          content: job.content,
          publisherId: job.publisher_id,
          deployerFeishuId: job.deployer_feishuId,
          remark: job.remark || '无备注',
          belong:
            BelongsEnumsArray.find((el) => el.value === job.belong)?.label ?? ''
        }
        if (job.trigger === 'deployed') {
          //多deloyments的不拉取历史tag版本
          const tags =
            job.deployments && job.deployments.length
              ? []
              : await this.kuberService.getDeploymentTagsHistory(
                  job.meta_name || job.deployments[0].meta_name,
                  job.meta_namespace || job.deployments[0].meta_namespace
                )
          //@ts-ignore
          vars.rollBackVersion = tags
            .filter((el) => !!el)
            .map((el) => {
              return {
                text: el,
                value: el
              }
            })

          if (
            !vars.rollBackVersion?.length &&
            (!job.deployments || !job.deployments.length)
          ) {
            this.feishuService.sendErrorMessageToUser(
              job.developer_feishuId,
              '未获取到历史tag，请联系运维进行检查容器是否有正确的labels字段!'
            )
          }
          const user = await this.userService.findUserByFeishuId(
            job.developer_feishuId
          )
          if (user && user.belong) {
            this.feishuService.sendMessage('chat_id', {
              receive_id: getBelongsChatId(user.belong),
              msg_type: 'interactive',
              content: deploy_success(vars, config.is_white_list)
            })
          }
          this.feishuService.sendMessage('chat_id', {
            receive_id: this.configService.get('messageChatId') as string,
            msg_type: 'interactive',
            content: deploy_success_group(vars, config.is_white_list)
          })
        } else {
          vars.rollbackTag = job.roll_back_tag

          this.feishuService.sendMessage('chat_id', {
            receive_id: this.configService.get('messageChatId') as string,
            msg_type: 'interactive',
            content: deploy_rollback_success(vars, config.is_white_list)
          })
          const user = await this.userService.findUserByFeishuId(
            job.developer_feishuId
          )
          if (user && user.belong) {
            this.feishuService.sendMessage('chat_id', {
              receive_id: getBelongsChatId(user.belong),
              msg_type: 'interactive',
              content: deploy_rollback_success(vars, config.is_white_list)
            })
          }
        }
      }
    } catch (e) {
      this.logger.error(`检查上线失败\nerror:${JSON.stringify(e)}`)
    }
  }
}
