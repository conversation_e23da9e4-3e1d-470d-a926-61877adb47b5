import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

export enum DocumentType {
  'ruXiao' = '入校项目',
  'xueXiGongJu' = '学习工具',
  'shangYeHua' = '商业化',
  'jiChu' = '基础设施'
}

@Entity()
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    nullable: false,
    comment: '项目名称',
    name: 'name'
  })
  name: string
  @Column({
    nullable: false,
    comment: '飞书OpenId',
    name: 'user_id'
  })
  userId: string

  @Column({
    nullable: false,
    comment: '飞书用户名',
    name: 'user_name',
    default: ''
  })
  userName: string

  @Column({
    comment: '飞书文档Url地址',
    name: 'feishu_url',
    default: ''
  })
  feishuUrl: string

  @Column({
    comment: '项目类型',
    type: 'enum',
    enum: ['ruXiao', 'xueXiGongJu', 'shangYeHua', 'jiChu']
  })
  type: keyof typeof DocumentType

  @Column({
    comment: '备注信息',
    nullable: true
  })
  remark: string

  @CreateDateColumn({
    name: 'create_time',
    comment: '创建时间'
  })
  createTime: Date

  @UpdateDateColumn({
    name: 'update_time',
    comment: '更新时间'
  })
  updateTime: Date
}
