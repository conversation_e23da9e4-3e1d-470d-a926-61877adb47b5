import { UpdateDocumentDto } from './dto/update-document.dto'
import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { DocumentsService } from './documents.service'

@Controller('documents')
export class DocumentsController {
  constructor(private readonly documentServicer: DocumentsService) {}
  @Get()
  async list(@Query('type') type: string) {
    const list = await this.documentServicer.find(type)
    return list
  }
  @Post()
  async create(@Body() body: UpdateDocumentDto) {
    const data = await this.documentServicer.updateDocument(body)
    return data
  }
  @Get('/delete')
  async deleteDoc(@Query('id') id: string) {
    await this.documentServicer.deleteDocument(id)
    return '删除成功'
  }
}
