import { UpdateDocumentDto } from './dto/update-document.dto'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { Document, DocumentType } from './documents.entity'
import { UserService } from 'src/user/user.service'

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document) private documentModel: Repository<Document>,
    private userService: UserService
  ) {}
  async find(type: string) {
    return this.documentModel.find({
      where: {
        type: type as keyof typeof DocumentType
      },
      order: {
        updateTime: 'ASC'
      }
    })
  }
  async updateDocument(document: UpdateDocumentDto) {
    const user = await this.userService.findUserById(document.userId)
    if (!document.id) {
      const newDoc = {
        ...document,
        userId: user!.id,
        userName: user!.name
      }
      const newDocument = this.documentModel.create(newDoc)
      await this.documentModel.save(newDocument)
      return newDocument
    } else {
      await this.documentModel.update(document.id, {
        ...document,
        userId: user!.id,
        userName: user!.name
      })
    }
  }
  async deleteDocument(id: string) {
    await this.documentModel.delete(id)
  }
}
