import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  DeleteDateColumn
} from 'typeorm'

@Entity()
export class AdminUser {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'varchar',
    length: 50
  })
  name: string

  @Column({
    type: 'varchar'
  })
  feishu_id: string
  @Column({
    type: 'varchar',
    default: ''
  })
  belong: string

  @Column({
    type: 'varchar',
    default: ''
  })
  role: string

  @Column({
    type: 'varchar'
  })
  avatar_url: string

  @Column({
    type: 'varchar',
    default: ''
  })
  phone: string

  @Column({
    type: 'varchar',
    default: '在职'
  })
  status: '在职' | '离职'

  @CreateDateColumn()
  create_time: Date

  @UpdateDateColumn()
  update_time: Date

  @Column({
    type: 'varchar',
    default: ''
  })
  update_user: string

  @DeleteDateColumn()
  delete_time: Date
}
