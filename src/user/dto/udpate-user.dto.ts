import { IsString, IsOptional } from 'class-validator'
import { ApiProperty } from '@nestjs/swagger'

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  id?: string

  @ApiProperty({ description: '用户姓名' })
  @IsString()
  name: string

  @ApiProperty({ description: '飞书用户ID' })
  @IsString()
  feishu_id: string

  @ApiProperty({ description: '业务归属' })
  @IsString()
  belong: string

  @ApiProperty({ description: '用户角色' })
  @IsString()
  role: string

  @ApiProperty({ description: '手机号码' })
  @IsString()
  phone: string

  @ApiProperty({ description: 'GitLab用户名', required: false })
  @IsString()
  @IsOptional()
  gitlab_username?: string

  @ApiProperty({ description: '邮箱地址', required: false })
  @IsString()
  @IsOptional()
  email?: string

  @ApiProperty({ description: '用户状态' })
  @IsString()
  status: '离职' | '在职'
}
