import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor
} from '@nestjs/common'
import { Request } from 'express'
import { map, Observable } from 'rxjs'

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const httpContext = context.switchToHttp()
    const request = httpContext.getRequest<Request>()
    //飞书回调不进行返回数据包装
    if (request.url.includes('/callback')) {
      return next.handle()
    }
    return next.handle().pipe(
      map((payload) => {
        return {
          data: payload,
          code: 0,
          error: false
        }
      })
    )
  }
}
