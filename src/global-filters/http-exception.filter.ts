import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException
} from '@nestjs/common'
import { Response } from 'express'

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp()
    const response = ctx.getResponse<Response>()
    const errorResponse = exception.getResponse()
    const request = ctx.getRequest<Request>()
    //飞书回调不进行返回数据包装
    if (request.url.includes('/callback')) {
      return
    }
    let message
    if (typeof errorResponse === 'string') {
      message = errorResponse
    } else {
      message = (errorResponse as any)?.message || ''
    }
    const res = {
      code: exception.getStatus() || 500,
      message,
      error: true
    }
    response.header('Content-Type', 'application/json; charset=utf-8')
    response.send(res)
  }
}
