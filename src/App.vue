<script setup lang="ts">
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

useHead({
  title: 'Onion JARVIS',
  meta: [
    {
      name: 'description',
      content: '一站式开发平台',
    },
    {
      name: 'theme-color',
      content: () => isDark.value ? '#00aba9' : '#ffffff',
    },
  ],
  link: [
    {
      rel: 'icon',
      type: 'image/svg+xml',
      href: '/favicon.svg',
    },
  ],
})
</script>

<template>
  <!-- <NConfigProvider
    :theme-overrides="{
      common: {
        primaryColor: '#0f766e',
      },
    }"
  > -->
  <el-config-provider :locale="zhCn">
    <RouterView />
  </el-config-provider>
  <!-- </NConfigProvider> -->
</template>
