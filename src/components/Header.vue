<script setup lang="ts">
import EditUserPopup from '~/pages/user/components/editUserPopup.vue'
import { useUserStore } from '~/stores/user'

const userStore = useUserStore()
const avatar = computed(() => userStore.userInfo.avatar)

function logout() {
  userStore.logout()
  location.reload()
}

const dialogForm = ref(false)
const manuallyClose = ref(true)
watch(() => userStore.userInfo, (val) => {
  if (val && !val.phone) {
    manuallyClose.value = false
    dialogForm.value = true
  }
})
function editUser() {
  dialogForm.value = true
}
function closeDialog() {
  dialogForm.value = false
  manuallyClose.value = true
  userStore.getUserInfo()
}
</script>

<template>
  <div class="fixed left-0 top-0 z-10 h-15 w-full flex items-center bg-#304156 nav-shadow" style="box-shadow: 0 2px 6px rgba(0,0,0,.08);">
    <div class="px-20px" flex="~ col justify-center items-center">
      <img class="h-60px" src="https://fp.yangcong345.com/middle/1.0.0/20230118-123401.jpg" alt="logo">
    </div>
    <div class="flex-1" />
    <div class="mr-4 flex items-center justify-center">
      <el-dropdown placement="bottom-end">
        <div class="flex items-end">
          <img class="h-10 w-10 rounded-md" :src="avatar" alt="avatar">
          <div class="i-solar-alt-arrow-down-bold h-4 w-4 color-white" />
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="logout">
              退出登录
            </el-dropdown-item>
            <el-dropdown-item @click="editUser">
              编辑信息
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <EditUserPopup :manually-close="manuallyClose" :dialog-form="dialogForm" :raw-data="userStore.user" :page-type="false" @close-dialog="closeDialog" />
</template>
