<script lang="ts" setup>
import { ref } from 'vue'
import { createNew<PERSON>ipeline } from '~/apis/pipeline'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  projectId: {
    type: String,
    required: true,
  },
  packageManager: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['refresh', 'update:modelValue'])

const visible = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})

const deployConfig = ref({
  branch: 'develop',
  tag_type: 'bug',
  package_manager: props.packageManager,
})

watch(() => props.packageManager, (value: string) => {
  deployConfig.value.package_manager = value
})

const createLoading = ref(false)

function confirm() {
  if (!deployConfig.value.package_manager) {
    ElMessage({
      message: '请先设置构建工具',
      type: 'warning',
    })
    return
  }
  createLoading.value = true
  createNewPipeline({
    project_id: props.projectId,
    tag_type: deployConfig.value.tag_type,
    package_manager: deployConfig.value.package_manager,
    branch: deployConfig.value.branch,
  }).then(() => {
    emit('update:modelValue', false)
    ElMessage.success('创建成功')
    emit('refresh')
  }).finally(() => {
    createLoading.value = false
  })
}
</script>

<template>
  <el-dialog v-model="visible" title="新建构建任务" :close-on-click-modal="false">
    <el-form :model="deployConfig" label-width="200px" label-position="left">
      <el-form-item label="分支：">
        <el-radio-group v-model="deployConfig.branch">
          <el-radio-button value="develop">
            develop
          </el-radio-button>
          <el-radio-button value="release">
            release
          </el-radio-button>
          <el-radio-button value="master">
            master
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="类型：">
        <el-radio-group v-model="deployConfig.tag_type">
          <el-radio-button value="feat">
            feat
          </el-radio-button>
          <el-radio-button value="bug">
            bug
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="install选项">
        <div v-if="deployConfig.package_manager">
          {{ deployConfig.package_manager }}
          <el-tooltip placement="top-start">
            <template #content>
              <div>
                此处不支持切换。如确需切换，请与项目维护者沟通后，<br>前往项目详情，修改构建工具
              </div>
            </template>
            <div class="i-solar-info-circle-bold inline-block cursor-pointer" />
          </el-tooltip>
        </div>
        <div v-else>
          当前项目未设置构建工具，请前往
          <a class="text-blue-500" :href="`/projects/detail?id=${props.projectId}`" target="_blank">项目详情</a>
          ，点击“编辑”，设置“构建工具”后，刷新页面重新构建
        </div>
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button type="danger" @click="visible = false">
        取消
      </el-button>
      <el-button :loading="createLoading" type="primary" @click="confirm">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>
