<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { envConfig } from '~/utils/env'

const lines = [
  'Hello，开发者。',
  '欢迎来到Jarvis平台。',
  'Jarvis致力于提供一站式开发服务体验。',
  '在这里，你可以完成服务构建部署、上线管理，',
  '以及工具、文档的快捷访问等。',
  '同时，期待你能助力技术栈的建设，',
  '提出需求、指出问题、贡献想法，或者深度参与其中。',
  '愿bug退散，按时下班～',
]

const lineIndex = ref(-1)
const userStore = useUserStore()
const name = computed(() => userStore.userInfo.name)
const { envName } = envConfig

onMounted(() => {
  const timer = setInterval(() => {
    lineIndex.value++
    if (lineIndex.value === lines.length - 1) {
      clearInterval(timer)
    }
  }, 600)
})
</script>

<template>
  <div>
    <div class="relative flex items-center">
      <div>
        <img src="https://fp.yangcong345.com/middle/1.0.0/<EMAIL>" class="h-80 rounded shadow-md">
        <div class="absolute left-5 top-5 text-2xl font-bold">
          欢迎 {{ name || '开发者' }} 来访！
        </div>
        <div v-if="envName !== '生产环境'" class="absolute bottom-5 left-5 text-xl color-gray-400 font-bold">
          {{ envName }}
        </div>
      </div>
      <div class="ml-12 text-lg leading-10">
        <div v-for="(line, index) in lines" :key="index" class="opacity-0" :class="[{ 'opacity-100 transition-opacity duration-300': lineIndex >= index }]">
          {{ line }}
        </div>
      </div>
    </div>
  </div>
</template>
