<script lang="ts" setup>
import type { CascaderNode, CascaderOption, CascaderValue, FormInstance } from 'element-plus'
import type { <PERSON>ong, User } from '~/apis/enums'
import type { Project } from '~/apis/project'
import type { Release, SonicItem } from '~/apis/release'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref, watch } from 'vue'
import { fetchAllUsers, fetchBelongs } from '~/apis/enums'
import { fetchAllProjectList } from '~/apis/project'
import {
  fetchProjectTags,
  fetchReleaseDepends,
  fetchReleaseDetail,
  fetchUITestProjectDetail,
  fetchUITestProjectList,
  postRelease,
} from '~/apis/release'
import { useUserStore } from '~/stores/user'

const props = defineProps({
  h5style: Boolean,
  modelValue: Boolean,
  releaseInfo: {
    type: Object,
    default: () => ({
      project_id: '',
      tag: '',
      publish_content: '',
      sql: '',
      remark: '',
      depend_project: '',
      notify_immediately: false,
      sonic: [],
    }),
  },
  isUpdate: Boolean,
  isFromDeploy: Boolean,
})

const emit = defineEmits(['refresh', 'update:modelValue'])

const route = useRoute()

const userStore = useUserStore()
const userId = computed(() => userStore.userInfo.id)

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  },
})

const createLoading = ref(false)
const projectList = ref<Project[]>([])
const belongs = ref<Belong[]>([])
const selectProjectAllList = ref<{ value: string, label: string }[]>([])
const depend_projectList = ref<{ value: string, label: string }[]>([])
const testerList = ref<User[]>([])
const developerList = ref<User[]>([])
const tagList = ref<{ value: string, label: string }[]>([])
const updateReleaseInfo = ref<Partial<Release>>({
  project_id: '',
  tag: '',
  publish_content: '',
  sql: '',
  remark: '',
  depend_project: '',
  notify_immediately: false,
  sonic: [],
})
const UITestValve = ref<string[]>([])
const addForm = ref<FormInstance | null>(null)

const release: Ref<Partial<Release>> = computed(() => {
  if (props.isUpdate && route.query.id) {
    return updateReleaseInfo.value
  }
  return props.releaseInfo
})

function checkTag(rule: any, value: string, callback: (error?: Error) => void) {
  if (!value) {
    return callback(new Error('来上线不选 tag，是没有能让你心动的 tag 吗。'))
  }
  const test = /test|dev|release|stage/.test(value)
  if (test) {
    return callback(new Error('你别瞎选啊，铁子，你还年轻。'))
  }
  return callback()
}

function checkDeveloper(rule: any, value: string, callback: (error?: Error) => void) {
  if (props.isUpdate && userId.value === release.value.tester) {
    return callback()
  }
  if (value !== userId.value)
    return callback(new Error('用别人身份上线？有点不符合道上的规矩啊！'))
  return callback()
}

const rules = {
  project_id: [
    { required: true, message: '哪个项目啊？没项目上线你过来干啥。', trigger: 'change' },
  ],
  tag: [
    { required: true, validator: checkTag, trigger: 'change' },
  ],
  developer: [
    { required: true, validator: checkDeveloper, trigger: 'change' },
  ],
  tester: [
    { required: true, message: '不选测试？悄悄地上线？明天就给你开复盘会！等着！！清算！！！', trigger: 'change' },
  ],
  publish_content: [
    { required: true, message: '一点改动没有嘛？别害羞慢慢说。', trigger: 'blur' },
  ],
}

const UITestProps = {
  lazy: true,
  lazyLoad: async (node: CascaderNode, resolve: (nodes: CascaderOption[]) => void) => {
    const { level } = node
    let nodes: CascaderOption[] = []
    if (level === 0) {
      const res = await fetchUITestProjectList()
      const list = res?.data || []
      nodes = list.map(item => ({
        value: item.projectName,
        label: item.projectName,
      }))
    }
    if (level === 1 && node.data?.label) {
      nodes = await changeUIProject(node.data.label)
    }
    resolve(nodes)
  },
}

const isShowUI = computed(() => {
  const curProject = projectList.value.find(item => item.id === release.value.project_id)
  return curProject ? !curProject.label.includes('2') : false
})

watch(() => props.isUpdate, async (val) => {
  if (val && route.query.id) {
    updateReleaseInfo.value = await fetchReleaseDetail(route.query.id as string)
  }
  if (val && Number(release.value.sonic?.length) > 0) {
    const sonicItem = release.value.sonic?.[0]
    if (sonicItem) {
      UITestValve.value = [sonicItem.projectName, sonicItem.caseSuiteName]
    }
  }
}, { immediate: true })

function changeBelong() {
  release.value.project_id = ''
  release.value.tag = ''
  tagList.value = []
  selectProjectAllList.value = []
  addForm.value?.clearValidate()
  selectProjectAllList.value = (release.value.belong && release.value.belong !== '8'
    ? projectList.value.filter(i => i.belong === release.value.belong)
    : projectList.value).map(({ id, name }) => ({ value: id, label: name }))
}

async function init() {
  const [projectRes, usersRes, dependsRes, belongsRes] = await Promise.all([
    fetchAllProjectList(),
    fetchAllUsers(),
    fetchReleaseDepends(),
    fetchBelongs(),
  ])

  projectList.value = projectRes
  selectProjectAllList.value = projectRes.map(({ id, name }) => ({ value: id, label: name }))
  testerList.value = usersRes.filter(item => item.role === '5')
  developerList.value = usersRes.filter(item => ['1', '2', '3', '4'].includes(item.role))

  if (!props.isUpdate && developerList.value.findIndex(item => item.value === userId.value) !== -1) {
    release.value.developer = userId.value
  }

  depend_projectList.value = dependsRes.map(item => ({
    value: item.project_id,
    label: `${item?.name}-${item.tag}`,
  }))

  belongs.value = belongsRes
}

async function getProjectTags(id: string) {
  const res = await fetchProjectTags(id)
  tagList.value = res
    .filter(i => !i.name.includes('jarvis_release') && !i.name.includes('jarvis_develop') && i.name.includes('v'))
    .map(item => ({
      value: item.name,
      label: item.name,
    }))
  return tagList.value
}

function changeProjectId(value: string) {
  getProjectTags(value)
}

function cancel() {
  dialogVisible.value = false
  createLoading.value = false
  tagList.value = []
  addForm.value?.resetFields()
}

async function submitRelease(param: Partial<Release>) {
  try {
    await postRelease({ ...param })
    ElMessage({
      showClose: true,
      message: '创建成功',
      type: 'success',
      duration: 5000,
    })
    emit('refresh')
    cancel()
  }
  catch {
    ElMessage({
      showClose: true,
      message: '创建失败',
      type: 'error',
      duration: 5000,
    })
  }
}

function confirm() {
  addForm.value?.validate((valid) => {
    if (valid) {
      createLoading.value = true
      submitRelease(release.value)
    }
  })
}

async function changeUIProject(name: string) {
  const res = await fetchUITestProjectDetail(name)
  const listData = res?.data || []
  return listData.map(item => ({
    value: item.name,
    label: item.name,
    leaf: true,
  }))
}

function changeUI(valve: CascaderValue) {
  if (Array.isArray(valve) && valve.length > 1) {
    const sonicItem = {
      projectName: valve[0],
      caseSuiteName: String(valve[1]),
    }
    release.value.sonic = [sonicItem] as SonicItem[]
  }
  else {
    release.value.sonic = []
  }
}

onMounted(() => {
  init()
})
</script>

<template>
  <div :class="[{ h5style }]">
    <el-dialog
      v-model="dialogVisible"
      :title="!isUpdate ? '新建上线' : '编辑'"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="!h5style"
      :class="[h5style ? 'w-full h-screen mt-0' : '']"
      @close="cancel"
    >
      <el-form
        ref="addForm"
        :model="release"
        :rules="rules"
        label-width="90px"
      >
        <el-form-item label="业务归属">
          <el-select
            v-model="release.belong"
            filterable
            placeholder="请选择项目业务归属"
            clearable
            :disabled="isUpdate || isFromDeploy"
            @change="changeBelong"
          >
            <el-option
              v-for="option of belongs"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="项目名称" prop="project_id">
          <el-select
            v-model="release.project_id"
            placeholder="请选择"
            filterable
            :disabled="isUpdate || isFromDeploy"
            @change="changeProjectId"
          >
            <el-option
              v-for="item in selectProjectAllList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="上线tag" prop="tag">
          <el-select
            v-model="release.tag"
            filterable
            placeholder="请选择"
            :disabled="isUpdate || isFromDeploy"
          >
            <el-option
              v-for="item in tagList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="开发" prop="developer">
          <el-select
            v-model="release.developer"
            filterable
            placeholder="请选择"
            :disabled="isUpdate"
          >
            <el-option
              v-for="item in developerList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="测试" prop="tester">
          <el-select
            v-model="release.tester"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in testerList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="上线内容" prop="publish_content">
          <el-input
            v-model="release.publish_content"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入内容"
          />
        </el-form-item>

        <el-form-item label="sql" prop="sql">
          <el-input
            v-model="release.sql"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入内容"
          />
        </el-form-item>

        <el-form-item label="依赖" prop="depend_project">
          <el-select
            v-model="release.depend_project"
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in depend_projectList"
              :key="`依赖-${index}-${item.value}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="release.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入内容"
          />
        </el-form-item>

        <el-form-item
          v-if="isShowUI"
          label="UI测试用例"
        >
          <el-cascader
            v-model="UITestValve"
            :props="UITestProps"
            class="w-full"
            clearable
            @change="changeUI"
          />
        </el-form-item>

        <el-form-item
          label="是否立即通知测试"
          label-width="140px"
          prop="remark"
        >
          <el-radio-group v-model="release.notify_immediately">
            <el-radio :value="true">
              是
            </el-radio>
            <el-radio :value="false">
              否
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div class="flex" :class="[h5style ? 'justify-center' : 'justify-end']">
        <el-button
          v-if="!h5style"
          type="danger"
          @click="cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="createLoading"
          :class="[h5style && 'absolute left-15% w-70%']"
          @click="confirm"
        >
          {{ !isUpdate ? '创建' : '保存' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.h5style :deep(.el-dialog__body) {
  background: #fff;
  padding-bottom: 80px;
}
</style>
