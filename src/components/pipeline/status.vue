<script setup lang="ts">
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

const props = defineProps({
  state: {
    type: String,
    required: true,
  },
  countTime: {
    type: Number,
    required: true,
  },
})

dayjs.extend(duration)

const stateText = computed(() => {
  switch (props.state) {
    case 'success':
      return '构建成功'
    case 'fail':
      return '构建失败'
    case 'stop':
      return '构建取消'
    case 'pending':
      return '正在构建'
    default:
      return ''
  }
})

const timeText = computed(() => {
  return dayjs.duration(props.countTime, 's').format('HH:mm:ss')
})
</script>

<template>
  <div class="flex flex-col items-center justify-center">
    <div class="flex items-center">
      <div class="text-xl">
        <div v-if="props.state === 'success'" class="i-akar-icons-circle-check-fill" style="color: #108548;" />
        <div v-if="props.state === 'fail'" class="i-akar-icons-circle-x-fill" style="color: #dd2b0e;" />
        <div v-if="props.state === 'stop'" class="i-akar-icons-block" style="color: #737278;" />
        <div v-if="props.state === 'pending'" class="i-akar-icons-clock" style="color: #108548;" />
      </div>
      <span class="ml-1">{{ stateText }}</span>
    </div>
    <div class="flex items-center">
      <div class="i-akar-icons-alarm mr-1" style="font-size: 14px" />
      <span>{{ timeText }}</span>
    </div>
  </div>
</template>
