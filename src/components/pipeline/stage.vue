<script setup lang="ts">
import type { PipelineStage } from '~/apis/pipeline'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

const props = defineProps<{
  pipelineStage: PipelineStage
}>()

dayjs.extend(duration)

const stageStatusText = computed(() => {
  switch (props.pipelineStage.status) {
    case 'success':
      return '完成'
    case 'pending':
      return '进行中'
    case 'fail':
      return '失败'
    default:
      return ''
  }
})

const stageText = computed(() => {
  switch (props.pipelineStage.stage) {
    case 'git-pull':
      return 'git pull'
    case 'install':
      return '依赖安装 install'
    case 'build':
      return '代码构建 build'
    case 'docker-build':
      return 'docker镜像构建'
    case 'docker-push':
      return 'docker镜像推送'
    default:
      return ''
  }
})

const timeCount = computed(() => {
  return dayjs.duration(props.pipelineStage.count_time, 's').format('mm:ss')
})
</script>

<template>
  <el-tooltip effect="dark" placement="top">
    <template #content>
      <div>
        <div>{{ stageText }}</div>
      </div>
    </template>
    <div class="mr-2 inline-flex flex-col items-center text-xs">
      <div class="text-base">
        <div v-if="pipelineStage.status === 'success'" class="i-akar-icons-circle-check-fill" style="color: #108548;" />
        <div v-if="pipelineStage.status === 'pending'" class="i-akar-icons-clock" style="color: #108548;" />
        <div v-if="pipelineStage.status === 'fail'" class="i-akar-icons-circle-x-fill" style="color: #dd2b0e;" />
      </div>
      <div>
        {{ stageStatusText }}
      </div>
      <div v-if="pipelineStage.count_time">
        {{ timeCount }}
      </div>
    </div>
  </el-tooltip>
</template>
