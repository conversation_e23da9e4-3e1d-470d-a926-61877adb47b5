<script lang="ts" setup>
import { FitAddon } from '@xterm/addon-fit'
import { Terminal } from '@xterm/xterm'
import { computed, nextTick, onBeforeUnmount, ref, watch } from 'vue'
import { fetchPipelineLogs } from '~/apis/pipeline'
import '@xterm/xterm/css/xterm.css'

const props = defineProps({
  modelValue: <PERSON>olean,
  id: String,
  needFetch: <PERSON>olean,
})

const emit = defineEmits(['update:modelValue'])

const xtermContainer = ref()
const contents = ref<string[]>([])
const timer = ref<NodeJS.Timeout | null>(null)
const terminal = ref<Terminal | null>(null)
const fitAddon = ref<FitAddon | null>(null)

const visible = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

function resizeHandler() {
  setTimeout(() => {
    fitAddon.value?.fit()
  }, 200)
}

function initTerminal() {
  if (!terminal.value) {
    const newTerminal = new Terminal({
      scrollback: 1000,
      rows: 900,
      disableStdin: true,
      cursorInactiveStyle: 'none',
      cursorStyle: 'underline',
    })
    const newFitAddon = new FitAddon()
    newTerminal.loadAddon(newFitAddon)
    nextTick(() => {
      newTerminal.open(xtermContainer.value)
      newFitAddon.fit()
      fitAddon.value = newFitAddon
      terminal.value = newTerminal
      window.addEventListener('resize', resizeHandler)
    })
  }
}

function fetch() {
  if (props.id) {
    fetchPipelineLogs(props.id).then((res) => {
      if (res.log) {
        const newContents = res.log.split(/\n/)
        if (newContents.length > contents.value.length) {
          const addedContents = newContents.slice(contents.value.length)
          contents.value.push(...addedContents)
          setTimeout(() => {
            addedContents.forEach((el) => {
              terminal.value?.writeln(el)
            })
          }, 10)
        }
      }
    })
  }
}

function clearTimer() {
  if (timer.value) {
    window.clearInterval(timer.value)
    timer.value = null
  }
}

function getNewFetch() {
  clearTimer()
  timer.value = setInterval(() => {
    fetch()
  }, 3000)
}

watch(() => props.modelValue, (val) => {
  if (val) {
    initTerminal()
    terminal.value?.clear()
    contents.value = []
    fetch()
    if (props.needFetch) {
      getNewFetch()
    }
  }
  else {
    clearTimer()
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeHandler)
  clearTimer()
})
</script>

<template>
  <el-dialog v-model="visible" width="80vw" title="构建日志">
    <div ref="xtermContainer" class="text-left" />
  </el-dialog>
</template>
