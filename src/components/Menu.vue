<script lang="tsx" setup>
import type { IMenu } from '~/helpers/menu'
import { ElMenuItem, ElSubMenu } from 'element-plus'
import { transformMenus } from '~/helpers/menu'

const isCollapse = ref(false)

const menus = transformMenus(
  useRouter()
    .getRoutes()
    .filter(route => Boolean(route?.meta?.menu))
    .map(route => route.meta.menu) as IMenu[],
)

function Menus() {
  return menus.map(
    menu =>
      menu.children && menu.children.length > 0
        ? (
            <ElSubMenu
              index={menu.path!}
              v-slots={{
                title: () => (
                  <>
                    <div class={menu.icon} />
                    <span>{menu.name}</span>
                  </>
                ),
              }}
            >
              {menu.children.map(child => (
                <ElMenuItem index={child.path}>
                  <div class={child.icon} />
                  <span>{child.name}</span>
                </ElMenuItem>
              ))}
            </ElSubMenu>
          )
        : (
            <ElMenuItem index={menu.path!}>
              <div class={menu.icon} />
              <span>{menu.name}</span>
            </ElMenuItem>
          ),
  )
}
</script>

<template>
  <ElMenu
    background-color="#f6f8fa"
    router
    :default-active="useRoute().path"
    :collapse="isCollapse"
  >
    <div class="my-20px flex items-center px-20px" @click="isCollapse = !isCollapse">
      <i v-if="!isCollapse" class="h-20px w-20px" i-ep-fold />
      <i v-else class="h-20px w-20px" i-ep-expand />
    </div>
    <Menus />
  </ElMenu>
</template>
