import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn
} from 'typeorm'
import { LogType } from '../logger.type'

@Entity()
export class Logger {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column('uuid')
  target_id: string

  @Column('uuid')
  operator: string

  @CreateDateColumn()
  create_time: Date

  @Column({
    type: 'varchar',
    length: 50
  })
  action: string

  @Column({
    type: 'varchar',
    length: 50
  })
  type: LogType
}
