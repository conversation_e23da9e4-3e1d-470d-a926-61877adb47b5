import { Inject, Injectable } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm'
import { Repository } from 'typeorm'
import { Logger } from './entities/logger.entitity'
import { LogType } from './logger.type'

@Injectable()
export class LoggerService {
  constructor(
    @InjectRepository(Logger)
    public readonly loggerRepository: Repository<Logger>
  ) {}
  async append(
    target_id: string,
    action: string,
    type: LogType,
    userId: string
  ) {
    const newLogger = this.loggerRepository.create({
      target_id,
      action,
      type,
      operator: userId
    })
    await this.loggerRepository.save(newLogger)
  }
}
