import { UserModule } from 'src/user/user.module'
import { Module } from '@nestjs/common'
import { ProjectModule } from 'src/project/project.module'
import { PublisherController } from './publisher.controller'
import { PublisherService } from './publisher.service'
import { TypeOrmModule } from '@nestjs/typeorm'
import { Publishers } from './entities/publisher.entity'
import { PublisherCallbackController } from './publisher-callback.controller'
import { PublisherKuberService } from './publisher-kuber.service'
import { FeishuApiModule } from 'src/feishu-api/feishu-module'
import { KuberModule } from 'src/kuber/kuber.module'
import { PublisherLimitServcie } from 'src/publisher/publisher-limit.service'
import { PublisherStatisticService } from './publisher-statistic.service'
import { SonicModule } from 'src/sonic/sonic.module'
@Module({
  imports: [
    ProjectModule,
    TypeOrmModule.forFeature([Publishers]),
    UserModule,
    FeishuApiModule,
    KuberModule,
    SonicModule
  ],
  controllers: [PublisherController, PublisherCallbackController],
  providers: [
    PublisherService,
    PublisherKuberService,
    PublisherLimitServcie,
    PublisherStatisticService
  ],
  exports: [PublisherService]
})
export class PublisherModule {}
