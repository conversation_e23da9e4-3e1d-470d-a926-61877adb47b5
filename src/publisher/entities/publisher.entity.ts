import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm'

export type PublisherStatus =
  | 'pending'
  | 'passTest'
  | 'deployed'
  | 'abandoned'
  | 'rolledBack'

export type Sonic = {
  projectName: string
  caseSuiteName: string
}[]

@Entity()
export class Publishers {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({
    type: 'uuid'
  })
  project_id: string

  @Column({
    nullable: false
  })
  tag: string

  @Column({
    nullable: false
  })
  developer: string

  @Column({
    nullable: true,
    default: ''
  })
  tester: string

  @Column({
    default: '',
    nullable: true,
    type: 'varchar',
    length: 500
  })
  publish_content: string

  @Column({
    type: 'varchar',
    default: '',
    nullable: true
  })
  sql: string

  @Column({
    type: 'varchar',
    nullable: true,
    default: ''
  })
  message_id: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  depend_project: string

  @Column({
    type: 'varchar',
    default: '',
    nullable: true,
    length: 500
  })
  remark: string

  @Column({
    type: 'boolean',
    default: false
  })
  notify_immediately: boolean

  @Column({
    type: 'varchar',
    default: 'pending'
  })
  status: PublisherStatus

  @Column({
    type: 'jsonb',
    nullable: true
  })
  sonic: Sonic

  @CreateDateColumn({
    type: 'timestamp',
    nullable: true
  })
  create_time: Date
  @UpdateDateColumn({
    type: 'timestamp',
    nullable: true
  })
  update_time: Date

  @Column({
    type: 'timestamp',
    nullable: true
  })
  deployed_time: Date

  @Column({
    type: 'timestamp',
    nullable: true
  })
  abandoned_time: Date
}
