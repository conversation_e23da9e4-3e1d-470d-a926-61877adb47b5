import { Body, Controller, Get, Post, Query, Request } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Public } from 'src/auth/jwt-meta'
import { FindPublisherDto } from './dto/find-publisher.dto'
import { UpdatePublisherDto } from './dto/update-publisher.dto'
import { PublisherService } from './publisher.service'
import { PublisherStatisticService } from './publisher-statistic.service'

@ApiTags('上线任务')
@Controller('publisher')
export class PublisherController {
  constructor(
    private readonly publisherService: PublisherService,
    private readonly publisherStatisticService: PublisherStatisticService
  ) {}
  @ApiOperation({
    summary: '新建或更新上线任务'
  })
  @Post('/')
  async createOrUpdate(@Body() body: UpdatePublisherDto) {
    if (!body.id) {
      return this.publisherService.createNewPublisher(body)
    } else {
      return this.publisherService.updatePublisher(body)
    }
  }
  @ApiOperation({
    summary: '根据上线任务Id获取上线任务详情'
  })
  @Get('/')
  async getPublisher(@Query('id') id: string) {
    return this.publisherService.findPublisher(id)
  }
  @Public()
  @ApiOperation({
    summary: '上线任务分页查询接口'
  })
  @Get('/page')
  async getPublisherPage(@Query() query: FindPublisherDto) {
    return this.publisherService.getPublisherPage(query)
  }

  @ApiOperation({
    summary: '获取所有等待上线的任务列表'
  })
  @Get('/depends')
  async findDepend() {
    return this.publisherService.findPendingPublishers()
  }

  @ApiOperation({
    summary: '根据上线任务Id向测试发送飞书卡片消息'
  })
  @Get('/notice')
  async noticeToTester(@Query('id') id: string) {
    await this.publisherService.noticePublisherToTester(id)
  }

  // 上线次数统计
  @ApiOperation({
    summary: '上线次数统计'
  })
  @Get('/statistics')
  async statistics(
    @Query()
    query: {
      startTime: string
      endTime: string
      userId: string
      projectId: string
    }
  ) {
    return this.publisherStatisticService.getOnlineStatistics(query)
  }
}
