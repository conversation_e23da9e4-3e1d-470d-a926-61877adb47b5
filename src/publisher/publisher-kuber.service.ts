import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { ProjectServer } from 'src/project/project.service'
import { UserService } from 'src/user/user.service'
import { FeishuApiService } from 'src/feishu-api/fetshu-service'
import { KuberService } from 'src/kuber/kuber.service'
import { CreateK8sPublisher } from './types'
import { FeishuActionDto } from './dto/feishu-action.dto'
import { PublisherService } from './publisher.service'
import * as k8s from '@kubernetes/client-node'
import { PublisherLimitServcie } from 'src/publisher/publisher-limit.service'
import { Logger } from 'winston'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { BelongsEnumsArray } from 'src/common/enum'
import { SonicService } from 'src/sonic/sonic.service'
import {
  isDeployValidate,
  compareVersions,
  hasDuplicateTag
} from 'src/publisher/utils'
import {
  deploy_abandoned,
  deploy_done,
  deploy_rollback,
  deployment_repeat_tag
} from 'src/feishu-card/card'

@Injectable()
export class PublisherKuberService {
  constructor(
    private readonly configService: ConfigService,
    private readonly projectService: ProjectServer,
    private readonly userService: UserService,
    private readonly feishuService: FeishuApiService,
    private readonly kuberService: KuberService,
    private readonly publisherService: PublisherService,
    private readonly limitService: PublisherLimitServcie,
    private readonly sonicService: SonicService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger
  ) {}

  async getProjectAndPublihser(publisherId: string) {
    const publisher = await this.publisherService.findPublisher(publisherId)
    const project = await this.projectService.findOne(publisher!.project_id)
    const config = await this.projectService.projectConfigEntity.findOne({
      where: {
        id: project?.id
      }
    })
    return {
      publisher,
      project,
      config
    }
  }

  handlerError(message: string, e: any) {
    let error = ''
    try {
      error = JSON.stringify(e)
    } catch {}
    this.logger.error(`${message},e:${error}`)
  }

  /**
   * 飞书卡片更新回调
   * @param body
   * @returns
   */
  async updatePublisherStatusFromCardAction(
    body: FeishuActionDto,
    open_id: string,
    message_id: string,
    target_tag?: string
  ) {
    const publisher_id = body.publisher_data
      ? body.publisher_data.publisher_id
      : body.publisher_id
    const updateStatus = body.publisher_data
      ? body.publisher_data.status
      : body.status
    if (!publisher_id || !updateStatus) {
      return
    }
    const { publisher, project, config } = await this.getProjectAndPublihser(
      publisher_id
    )
    if (!publisher || !project || !config) {
      return
    }
    const name = project?.name
    const projectId = publisher?.project_id
    const name_space = config?.name_space
    const tag = publisher?.tag
    // 查询当前项目最近三天tag: 待上线，废弃，已上线，
    const allTag = await this.publisherService.findUnDeployPublishersTag(
      projectId
    )
    // todoVersion 判断是否有大于当前tag版本
    let hasTagRepeatNoticeConfirm = false
    if (tag) {
      // 判断当前tag版本低
      allTag.map((item) => {
        if (compareVersions(tag, item) < 0) {
          hasTagRepeatNoticeConfirm = true
        }
      })
      // 判断当前tag存在多个
      if (hasDuplicateTag(allTag, tag)) {
        hasTagRepeatNoticeConfirm = true
      }
    }

    const [test, developer, deployer] = await Promise.all([
      this.userService.findUserById(publisher!.tester),
      this.userService.findUserById(publisher!.developer),
      this.userService.findUserByFeishuId(open_id)
    ])
    const officer = await this.userService.getAllOfficer()
    const onDutyOfficer =
      (await this.userService.getOnCallOfficer()) || developer
    const officerIds = officer.map((el) => el.feishu_id)
    const vars = {
      name: project.name,
      publisherId: publisher.id,
      developerName: developer!.name,
      developerFeishuId: developer!.feishu_id,
      testerName: test!.name,
      testerFeishuId: test!.feishu_id,
      tag: publisher.tag,
      sql: publisher.sql,
      content: publisher.publish_content,
      duty: onDutyOfficer!.feishu_id,
      deployerName: deployer?.name,
      deployerFeishuId: deployer?.feishu_id,
      remark: publisher.remark || '无备注',
      rollbackTag: '',
      belong:
        BelongsEnumsArray.find((el) => el.value === project.belong)?.label ?? ''
    }
    if (publisher) {
      if (publisher.status === 'pending') {
        //修改流程取消第一步
        if (updateStatus === 'abandoned') {
          //废弃上线
          if (
            open_id !== test!.feishu_id &&
            !officer.find((el) => el.feishu_id === open_id)
          ) {
            return
          }
          publisher.status = 'abandoned'
          publisher.abandoned_time = new Date()
          await this.publisherService.publisherRepository.save(publisher)
          this.feishuService.updateMessage(
            message_id,
            deploy_abandoned(vars, config.is_white_list)
          )
        } else if (updateStatus === 'deployed_done') {
          //手动上线完成
          if (
            !isDeployValidate(
              config.is_white_list,
              open_id,
              test!.feishu_id,
              officerIds,
              this.limitService.validateTimeLimit()
            )
          ) {
            this.feishuService.sendErrorMessageToUser(
              open_id,
              '项目当前时段不能上线，检查项目卡片是否有绿色通道标识或你没有卡片上限权限！'
            )
            return
          }
          publisher.status = 'deployed'
          publisher.deployed_time = new Date()
          await this.publisherService.publisherRepository.save(publisher)
          this.feishuService.updateMessage(
            message_id,
            deploy_done(vars, config.is_white_list)
          )
        } else if (
          ['deployed', 'repeat_check_deployed'].includes(updateStatus)
        ) {
          //执行部署操作
          if (
            !isDeployValidate(
              config.is_white_list,
              open_id,
              test!.feishu_id,
              officerIds,
              this.limitService.validateTimeLimit()
            )
          ) {
            this.feishuService.sendErrorMessageToUser(
              open_id,
              '项目当前时段不能上线，检查项目卡片是否有绿色通道标识或你没有卡片上限权限！'
            )
            return
          }
          // repeat_check_deployed：检测到有相同的tag弹窗提示后再次上线的回调
          if (hasTagRepeatNoticeConfirm && updateStatus === 'deployed') {
            vars.publisherId = publisher_id
            this.feishuService.updateMessage(
              message_id,
              deployment_repeat_tag(vars, config.is_white_list)
            )
            return
          }

          const res = await this.createDeployK8sPublisher(
            name,
            name_space,
            tag,
            developer!.feishu_id,
            test!.feishu_id,
            config.deployments
          )
          if (publisher.sonic && publisher.sonic.length) {
            publisher.sonic.forEach((el) => {
              if (el.projectName && el.caseSuiteName) {
                this.sonicService.runSonicCase(el)
              }
            })
          }
          if (!res || (Array.isArray(res) && res.some((el) => !el.body))) {
            this.feishuService.sendErrorMessageToUser(
              open_id,
              '项目含有多个deployments，有某个上线异常，请重新上线。'
            )
            return
          }
          if (Array.isArray(res)) {
            const newJob =
              this.kuberService.publishersKuberJobRepository.create({
                receive_id: onDutyOfficer!.feishu_id,
                project_id: project.id,
                publisher_id: publisher.id,
                belong: project.belong,
                name,
                tag,
                trigger: 'deployed',
                developer_name: developer!.name,
                developer_feishuId: developer!.feishu_id,
                tester_name: test!.name,
                tester_feishuId: test!.feishu_id,
                deployer_name: deployer!.name,
                deployer_feishuId: deployer!.feishu_id,
                content: publisher.publish_content,
                check_time: 0,
                sql: publisher.sql,
                remark: publisher.remark,
                meta_name: name,
                meta_namespace: name_space,
                deployments: res.map((el) => {
                  return {
                    replicas: el.body.spec?.replicas,
                    generation: el.body.metadata?.generation,
                    meta_name: el.body.metadata?.name,
                    meta_namespace: el.body.metadata?.namespace
                  }
                })
              })
            await this.kuberService.publishersKuberJobRepository.save(newJob)
          } else {
            const newJob =
              this.kuberService.publishersKuberJobRepository.create({
                receive_id: onDutyOfficer!.feishu_id,
                project_id: project.id,
                publisher_id: publisher.id,
                belong: project.belong,
                name,
                tag,
                trigger: 'deployed',
                developer_name: developer!.name,
                developer_feishuId: developer!.feishu_id,
                tester_name: test!.name,
                tester_feishuId: test!.feishu_id,
                deployer_name: deployer!.name,
                deployer_feishuId: deployer!.feishu_id,
                content: publisher.publish_content,
                replicas: res.body.spec?.replicas,
                generation: res.body.metadata?.generation,
                check_time: 0,
                sql: publisher.sql,
                meta_name: res.body.metadata?.name,
                meta_namespace: res.body.metadata?.namespace,
                remark: publisher.remark
              })
            await this.kuberService.publishersKuberJobRepository.save(newJob)
          }
          publisher.status = 'deployed'
          publisher.deployed_time = new Date()
          await this.publisherService.publisherRepository.save(publisher)
          this.feishuService.updateMessage(
            message_id,
            deploy_done(vars, config.is_white_list)
          )
        }
      }
      if (publisher.status === 'deployed') {
        if (updateStatus === 'rolledBack') {
          if (
            open_id !== test!.feishu_id &&
            !officer.find((el) => el.feishu_id === open_id)
          ) {
            this.feishuService.sendErrorMessageToUser(
              open_id,
              '你不是这个项目测试，没权限回滚！'
            )
            return
          }
          const res = await this.rollback(
            project.name,
            config.name_space,
            config.deployments,
            target_tag
          )
          //执行回滚操作
          if (Array.isArray(res)) {
            const newJob =
              this.kuberService.publishersKuberJobRepository.create({
                receive_id: onDutyOfficer!.feishu_id,
                project_id: project.id,
                publisher_id: publisher.id,
                name,
                belong: project.belong,
                tag,
                trigger: 'rolledBack',
                developer_name: developer!.name,
                developer_feishuId: developer!.feishu_id,
                tester_name: test!.name,
                tester_feishuId: test!.feishu_id,
                deployer_name: deployer!.name,
                deployer_feishuId: deployer!.feishu_id,
                content: publisher.publish_content,
                check_time: 0,
                sql: publisher.sql,
                roll_back_tag: res?.[0]?.rollbackTag,
                deployments: res.map((el) => {
                  return {
                    replicas: el?.roobackRes.body.spec?.replicas,
                    generation: el?.roobackRes.body.metadata?.generation,
                    meta_name: el?.roobackRes.body.metadata?.name,
                    meta_namespace: el?.roobackRes.body.metadata?.namespace
                  }
                })
              })
            await this.kuberService.publishersKuberJobRepository.save(newJob)
            vars.rollbackTag = res[0]!.rollbackTag
          } else {
            const newJob =
              this.kuberService.publishersKuberJobRepository.create({
                receive_id: onDutyOfficer!.feishu_id,
                project_id: project.id,
                publisher_id: publisher.id,
                name,
                belong: project.belong,
                tag,
                trigger: 'rolledBack',
                developer_name: developer!.name,
                developer_feishuId: developer!.feishu_id,
                tester_name: test!.name,
                tester_feishuId: test!.feishu_id,
                deployer_name: deployer!.name,
                deployer_feishuId: deployer!.feishu_id,
                content: publisher.publish_content,
                replicas: res?.roobackRes.body.spec?.replicas,
                generation: res?.roobackRes.body.metadata?.generation,
                check_time: 0,
                sql: publisher.sql,
                meta_name: res?.roobackRes.body.metadata?.name,
                meta_namespace: res?.roobackRes.body.metadata?.namespace,
                roll_back_tag: res?.rollbackTag
              })
            await this.kuberService.publishersKuberJobRepository.save(newJob)
            vars.rollbackTag = res!.rollbackTag
          }
          publisher.status = 'rolledBack'
          await this.publisherService.publisherRepository.save(publisher)

          this.feishuService.updateMessage(
            message_id,
            deploy_rollback(vars, config.is_white_list)
          )
          // 向异常上线多维表格添加记录
          this.feishuService.addRecordToBitable({
            appToken: this.configService.get('EXCEPTION_BITABLE_APPTOKEN'),
            tableId: this.configService.get('EXCEPTION_BITABLE_TABLEID'),
            fields: {
              日期: Date.now(),
              月份: `${new Date().getMonth() + 1}月`,
              上线需求or小版本信息: publisher.publish_content,
              测试: [{ id: test!.feishu_id }],
              开发: [{ id: developer!.feishu_id }],
              业务团队:
                BelongsEnumsArray.find((el) => el.value === deployer?.belong)
                  ?.label ?? '',
              绿色通道: config.is_white_list ? '是' : '否',
              备注: `服务：${name}\n版本：${tag}\n回滚版本：${
                vars.rollbackTag
              }\n操作人：${deployer!.name}\n开发：${developer!.name}\n测试：${
                test!.name
              }\n上线内容：${publisher.publish_content}`
            }
          })
        }
      }
    }
  }

  async createDeployK8sPublisher(
    name: string,
    name_space: string,
    tag: string,
    developerFeishuId: string,
    testerFeishuId: string,
    deployments?: string[]
  ) {
    if (deployments?.length) {
      const res = await Promise.all(
        deployments.map((deployment) => {
          return this.createK8sPublisher({
            name: deployment,
            name_space,
            tag,
            developerFeishuId,
            testerFeishuId
          })
        })
      )
      return res
    } else {
      return this.createK8sPublisher({
        name,
        name_space,
        tag,
        developerFeishuId,
        testerFeishuId
      })
    }
  }

  async createK8sPublisher({
    name,
    name_space,
    tag,
    developerFeishuId,
    testerFeishuId
  }: CreateK8sPublisher) {
    try {
      const deployment = await this.kuberService.getDeployment(name, name_space)
      const container = deployment.body.spec?.template.spec?.containers.find(
        (el) => el.image?.includes(name) || el.name === name
      )
      if (!container) {
        ;[developerFeishuId, testerFeishuId].forEach((el) => {
          this.feishuService.sendMessage('open_id', {
            receive_id: el,
            msg_type: 'text',
            content: {
              text: `${name}未找到对应deployment项目镜像,无法创建上线任务,请联系运维查看具体问题`
            }
          })
        })
        throw new Error()
      }
      const imagePrefix = container.image?.split(':')[0]
      const newImage = `${imagePrefix}:${tag}`
      container.image = newImage
      const res = await this.kuberService.k8sAppV1Api.patchNamespacedDeployment(
        name,
        name_space,
        {
          spec: {
            template: {
              spec: {
                containers: [
                  {
                    name: container.name,
                    image: newImage
                  }
                ]
              }
            }
          }
        },
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        {
          headers: {
            'Content-Type': k8s.PatchUtils.PATCH_FORMAT_STRATEGIC_MERGE_PATCH
          }
        }
      )
      return res
    } catch (e) {
      ;[developerFeishuId, testerFeishuId].forEach((el) => {
        this.feishuService.sendMessage('open_id', {
          receive_id: el,
          msg_type: 'text',
          content: {
            text: `创建k8s上线失败,\ne:${JSON.stringify(e)}`
          }
        })
      })
      this.handlerError('k8s上线失败', e)
      throw e
    }
  }

  async rollbackJob(
    name: string,
    name_space: string,
    duty_feishu_id: string,
    target_tag = ''
  ) {
    try {
      const deploymentRes = await this.kuberService.getDeployment(
        name,
        name_space
      )
      const deployment = deploymentRes.body
      const labelSelector = await this.kuberService.getDeploymentSelectLabel(
        name,
        name_space
      )
      if (labelSelector) {
        const replicaSets =
          await this.kuberService.k8sAppV1Api.listNamespacedReplicaSet(
            deployment.metadata?.namespace as string,
            undefined,
            undefined,
            undefined,
            undefined,
            labelSelector
          )
        let template: any

        if (target_tag) {
          const targetRepliceSet = replicaSets.body.items.find((item) => {
            const container = item.spec?.template?.spec?.containers.find(
              (container) => container.image?.includes(name)
            )
            if (container) {
              const tag = container.image?.split(':')?.[1]
              return tag === target_tag
            }
          })
          if (targetRepliceSet) {
            template = targetRepliceSet?.spec?.template
          }
        } else {
          if (replicaSets.body.items.length < 2) {
            this.feishuService.sendMessage('open_id', {
              receive_id: duty_feishu_id,
              msg_type: 'text',
              content: {
                text: `${name}回滚失败,无可用历史版本`
              }
            })
            return
          }
          const items = replicaSets.body.items.sort((a, b) => {
            return (
              a.metadata!.creationTimestamp!.valueOf() -
              b.metadata!.creationTimestamp!.valueOf()
            )
          })
          const preReplicaSet = items[items.length - 2]
          template = preReplicaSet.spec?.template
          const container = template.spec.containers.find((container) =>
            container.image.includes(name)
          )
          if (container) {
            const tag = container.image.split(':')?.[1]
            target_tag = tag
          }
        }
        if (template) {
          deployment.spec!.template = template
        }

        const res =
          await this.kuberService.k8sAppV1Api.replaceNamespacedDeployment(
            name,
            name_space,
            deployment
          )
        return { roobackRes: res, rollbackTag: target_tag }
      }
    } catch (e) {
      this.handlerError('k8s回滚失败', e)
      throw e
    }
  }

  async rollback(
    name: string,
    name_space: string,
    deployments?: string[],
    target_tag?: string
  ) {
    try {
      const onDutyOfficer = await this.userService.getOnCallOfficer()
      if (!onDutyOfficer) {
        return
      }

      if (deployments?.length) {
        return Promise.all(
          deployments.map((deployment) => {
            return this.rollbackJob(
              deployment,
              name_space,
              onDutyOfficer.feishu_id,
              target_tag
            )
          })
        )
      } else {
        return this.rollbackJob(
          name,
          name_space,
          onDutyOfficer.feishu_id,
          target_tag
        )
      }
    } catch (e) {
      this.handlerError('k8s回滚失败', e)
      throw e
    }
  }
}
