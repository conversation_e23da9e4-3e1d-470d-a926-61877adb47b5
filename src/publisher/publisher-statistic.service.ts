import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, Between, In } from 'typeorm'
import { Publishers } from './entities/publisher.entity'
import { ProjectEntity } from 'src/project/entities/project.entity'

@Injectable()
export class PublisherStatisticService {
  constructor(
    @InjectRepository(Publishers)
    public readonly publisherRepository: Repository<Publishers>
  ) {}

  // 获取各状态上线次数
  async getCountsByStatus(params) {
    const query = {
      status: In(['deployed', 'rolledBack', 'pending', 'abandoned'])
    }
    if (params.startTime && params.endTime) {
      query['update_time'] = Between(
        new Date(params.startTime),
        new Date(params.endTime)
      )
    }
    if (params.projectId) {
      query['project_id'] = params.projectId
    }
    const queryBuilder = this.publisherRepository
      .createQueryBuilder('publisher')
      .select('publisher.status', 'status')
      .addSelect('COUNT(publisher.id)', 'count')
      .where(query)
    if (params.userId) {
      queryBuilder.andWhere(
        '(publisher.developer = :userId OR publisher.tester = :userId)',
        { userId: params.userId }
      )
    }
    const result = await queryBuilder.groupBy('publisher.status').getRawMany()
    return result
  }

  // 获取项目上线排行
  async getCountsByProject(params) {
    const query = {}
    if (params.startTime && params.endTime) {
      query['update_time'] = Between(
        new Date(params.startTime),
        new Date(params.endTime)
      )
    }
    if (params.projectId) {
      query['project_id'] = params.projectId
    }
    const queryBuilder = this.publisherRepository
      .createQueryBuilder('publisher')
      .select('count(*)', 'count')
      .addSelect('publisher.project_id', 'project_id')
      .addSelect('project_entity.name', 'name')
      .innerJoin(
        ProjectEntity,
        'project_entity',
        'publisher.project_id = project_entity.id'
      )
      .where(query)
    if (params.userId) {
      queryBuilder.andWhere(
        '(publisher.developer = :userId OR publisher.tester = :userId)',
        { userId: params.userId }
      )
    }
    const result = await queryBuilder
      .groupBy('publisher.project_id')
      .addGroupBy('project_entity.name')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany()
    return result
  }

  // 获取各个日期的上线数量
  async getHeatmapByDate(params) {
    const query = {}
    if (params.startTime && params.endTime) {
      query['update_time'] = Between(
        new Date(params.startTime),
        new Date(params.endTime)
      )
    }
    if (params.projectId) {
      query['project_id'] = params.projectId
    }
    const queryBuilder = this.publisherRepository
      .createQueryBuilder('publisher')
      .select('DATE(publisher.update_time)', 'date')
      .addSelect('COUNT(*)', 'count')
      .where(query)
    if (params.userId) {
      queryBuilder.andWhere(
        '(publisher.developer = :userId OR publisher.tester = :userId)',
        { userId: params.userId }
      )
    }
    const result = await queryBuilder
      .groupBy('DATE(publisher.update_time)')
      .orderBy('date', 'DESC')
      .limit(366)
      .getRawMany()
    return result
  }

  // 获取各个时间段的上线数量
  async getHeatmapByHour(params) {
    const query = {}
    if (params.startTime && params.endTime) {
      query['update_time'] = Between(
        new Date(params.startTime),
        new Date(params.endTime)
      )
    }
    if (params.projectId) {
      query['project_id'] = params.projectId
    }
    const queryBuilder = this.publisherRepository
      .createQueryBuilder('publishers')
      .select('EXTRACT(HOUR FROM publishers.update_time)', 'hour')
      .addSelect('COUNT(*)', 'count')
      .where(query)

    if (params.userId) {
      queryBuilder.andWhere(
        '(publishers.developer = :userId OR publishers.tester = :userId)',
        { userId: params.userId }
      )
    }
    const result = await queryBuilder
      .groupBy('EXTRACT(HOUR FROM publishers.update_time)')
      .orderBy('hour')
      .getRawMany()

    return result
  }

  // 获取上线数据统计
  async getOnlineStatistics(query) {
    const countsByStatus = await this.getCountsByStatus(query)
    const countsByProject = await this.getCountsByProject(query)
    const heatmapByDate = await this.getHeatmapByDate(query)
    const heatmapByHour = await this.getHeatmapByHour(query)
    return {
      countsByStatus,
      countsByProject,
      heatmapByDate,
      heatmapByHour
    }
  }
}
