import { PublisherStatus } from './entities/publisher.entity'

export const getStatusLabelText = (status: PublisherStatus) => {
  switch (status) {
    case 'pending':
      return '待测试'
    case 'passTest':
      return '待上线'
    case 'deployed':
      return '已部署'
    case 'abandoned':
      return '已废弃'
    case 'rolledBack':
      return '已回滚'
  }
}

export const sleep = async (timeout: number) => {
  return new Promise<void>((reslove) => {
    setTimeout(() => {
      reslove()
    }, timeout)
  })
}

export const isDeployValidate = (
  is_white_list: boolean,
  open_id: string,
  test_id: string,
  duty_ids: string[],
  is_work_time: boolean
) => {
  const has_role = duty_ids.some((el) => el === open_id) || test_id === open_id
  if (is_white_list) {
    return has_role
  }
  if (duty_ids.some((el) => el === open_id)) {
    return true
  }
  if (test_id === open_id) {
    return is_work_time
  }
}

export function compareVersions(version1, version2) {
  // 将版本号拆分为数组并转换为数字
  const v1Parts = version1
    .split('.')
    .map((part) => parseInt(part.replace(/[^\d]/g, ''), 10))
  const v2Parts = version2
    .split('.')
    .map((part) => parseInt(part.replace(/[^\d]/g, ''), 10))

  // 比较每个部分
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const part1 = v1Parts[i] || 0 // 如果某个版本号没有该部分，默认为0
    const part2 = v2Parts[i] || 0

    if (part1 > part2) {
      return 1 // version1 大于 version2
    }
    if (part1 < part2) {
      return -1 // version1 小于 version2
    }
  }
  return 0 // 版本相等
}

export function hasDuplicateTag(arr, tag) {
  // 过滤出与给定标签相同的元素
  const filtered = arr.filter((item) => item.tag === tag)

  // 检查过滤后的数组长度是否大于1
  return filtered.length > 1
}
