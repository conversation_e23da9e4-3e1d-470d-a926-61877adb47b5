import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'src/utils/IsOptional'
import { PageBaseDto } from 'src/utils/pageBaseDto'

export class FindPublisherDto extends PageBaseDto {
  // 项目id
  @ApiProperty({
    required: false,
    description: '项目id'
  })
  @IsOptional()
  projectId: string
  // 开发者
  @ApiProperty({
    required: false,
    description: '开发者'
  })
  @IsOptional()
  developer: string
  // 测试人员
  @ApiProperty({
    required: false,
    description: '测试人员'
  })
  @IsOptional()
  tester: string
  // 上线状态
  @ApiProperty({
    required: false,
    description: '上线状态'
  })
  @IsOptional()
  status: string
  // 创建时间-开始
  @ApiProperty({
    required: false,
    description: '创建时间-开始'
  })
  @IsOptional()
  createTimeStart: string
  // 创建时间-结束
  @ApiProperty({
    required: false,
    description: '创建时间-结束'
  })
  @IsOptional()
  createTimeEnd: string
}
