import { ApiProperty } from '@nestjs/swagger'
import {
  Is<PERSON>rray,
  IsBoolean,
  IsObject,
  IsString,
  MaxLength
} from 'class-validator'
import { Sonic } from 'src/publisher/entities/publisher.entity'
import { IsOptional } from 'src/utils/IsOptional'

export class UpdatePublisherDto {
  @ApiProperty({
    required: false,
    description: '上线任务Id,无即为新增'
  })
  @IsString()
  @IsOptional()
  id: string

  @ApiProperty({
    required: true,
    description: '上线的项目Id'
  })
  @IsString()
  project_id: string

  @ApiProperty({
    required: true,
    description: '上线的项目tag号，由tag接口拉取'
  })
  @IsString()
  tag: string

  @ApiProperty({
    required: true,
    description: '开发人Id'
  })
  @IsString()
  developer: string

  @ApiProperty({
    required: true,
    description: '测试Id'
  })
  @IsString()
  tester: string

  @ApiProperty({
    required: true,
    description: '上线内容'
  })
  @MaxLength(500)
  @IsString()
  publish_content: string

  @ApiProperty({
    required: true,
    description: '上线sql'
  })
  @IsString()
  sql: string

  @ApiProperty({
    required: false,
    description: '依赖项目的Id'
  })
  @IsString()
  depend_project: string

  @ApiProperty({
    required: true,
    description: '备注'
  })
  @MaxLength(500)
  @IsString()
  remark: string

  @ApiProperty({
    required: true,
    description: '是否立即通知测试'
  })
  @IsBoolean()
  notify_immediately: boolean

  sonic: Sonic
}
