import { Body, Controller, HttpCode, Post } from '@nestjs/common'
import { Public } from 'src/auth/jwt-meta'
import { FeishuActionDto } from './dto/feishu-action.dto'
import { PublisherKuberService } from './publisher-kuber.service'

@Controller('/callback')
export class PublisherCallbackController {
  constructor(private readonly publisherKuberService: PublisherKuberService) {}

  @Public()
  @Post()
  @HttpCode(200)
  async feishuChallenge(@Body() body: Record<string, any>) {
    if ('challenge' in body && body.challenge) {
      return {
        challenge: body.challenge
      }
    }
    if ('action' in body && body.action) {
      this.publisherKuberService.updatePublisherStatusFromCardAction(
        body.action.value as FeishuActionDto,
        body.open_id,
        body.open_message_id,
        body.action.tag === 'select_static' && body.action.option
          ? body.action.option
          : ''
      )
      return ''
    }
  }
}
