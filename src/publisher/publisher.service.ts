import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { InjectRepository } from '@nestjs/typeorm'
import { FeishuApiService } from 'src/feishu-api/fetshu-service'
import { ProjectServer } from 'src/project/project.service'
import { UserService } from 'src/user/user.service'
import { createMessage } from 'src/utils/feishuMessage'
import { In, Repository, MoreThan, FindOptionsWhere, Between } from 'typeorm'
import { UpdatePublisherDto } from './dto/update-publisher.dto'
import { Publishers, PublisherStatus } from './entities/publisher.entity'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import { FindPublisherDto } from './dto/find-publisher.dto'
import { AdminUser } from 'src/user/entities/user.entity'
import { getStatusLabelText } from './utils'
import { REQUEST } from '@nestjs/core'
import { getBelongsChatId } from 'src/common/enum'
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston'
import { Logger } from 'winston'
import { deployment_start } from 'src/feishu-card/card'

dayjs.extend(weekOfYear)

@Injectable()
export class PublisherService {
  constructor(
    private readonly configService: ConfigService,
    private readonly projectService: ProjectServer,
    private readonly userService: UserService,
    @InjectRepository(Publishers)
    public readonly publisherRepository: Repository<Publishers>,
    private readonly feishuService: FeishuApiService,
    @Inject(WINSTON_MODULE_NEST_PROVIDER) private readonly logger: Logger
  ) {}

  async findPendingPublishers() {
    const projects = await this.projectService.projectModel.find()
    const publishers = await this.publisherRepository.find({
      where: {
        status: 'pending'
      }
    })
    return publishers.map((publisher) => {
      return {
        ...publisher,
        name: projects.find((pro) => pro.id === publisher.project_id)?.name
      }
    })
  }

  // 当前项目当天未发布的tagList
  async findUnDeployPublishersTag(projectId: string) {
    const threeDaysAgo = new Date()
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
    const publishers = await this.publisherRepository.find({
      where: {
        project_id: projectId,
        // status: Not('deployed'), // 状态不等于 'deploy'
        update_time: MoreThan(threeDaysAgo) // 创建时间在三天内
      }
    })
    return publishers.map((item) => {
      return item.tag
    })
  }

  async getPublisherPage(query: FindPublisherDto) {
    const findQuery: FindOptionsWhere<Publishers> = {}
    if (query.projectId) {
      findQuery.project_id = query.projectId
    }
    if (query.developer) {
      findQuery.developer = query.developer
    }
    if (query.tester) {
      findQuery.tester = query.tester
    }
    if (query.status) {
      findQuery.status = query.status as PublisherStatus
    }
    if (query.createTimeStart && query.createTimeEnd) {
      findQuery.update_time = Between(
        new Date(query.createTimeStart),
        new Date(query.createTimeEnd)
      )
    }
    const [data, projects, users] = await Promise.all([
      this.publisherRepository.find({
        where: findQuery,
        take: query.pageSize,
        skip: (query.page - 1) * query.pageSize,
        order: {
          create_time: 'DESC'
        }
      }),
      this.projectService.projectModel.find(),
      this.userService.userRepository.find()
    ])
    const rawData = data.map((el) => {
      return {
        ...el,
        status_text: getStatusLabelText(el.status),
        name: projects.find((pro) => pro.id === el.project_id)?.name,
        developer_name: users.find((user) => user.id === el.developer)?.name,
        tester_name: users.find((user) => user.id === el.tester)?.name,
        depend_project_name: projects.find(
          (pro) => pro.id === el.depend_project
        )?.name
      }
    })
    const count = await this.publisherRepository.count({
      where: findQuery
    })
    return {
      page: query.page,
      pageSize: query.pageSize,
      list: rawData,
      total: count
    }
  }
  async findPublisher(id: string) {
    return this.publisherRepository.findOne({
      where: {
        id
      }
    })
  }

  /**
   * 项目是否存在循环依赖
   * @param dependProId
   * @param projectId
   * @returns
   */
  async validateCircularDependency(dependProId: string, projectId: string) {
    const dependProject = await this.publisherRepository.findBy({
      depend_project: projectId,
      project_id: dependProId,
      status: In(['pending', 'passTest'])
    })
    if (dependProject.length) {
      return false
    }
    return true
  }
  /**
   * 创建新的上线任务
   * @param data
   * @returns
   */
  async createNewPublisher(data: UpdatePublisherDto) {
    const {
      tag,
      publish_content,
      project_id,
      depend_project,
      tester,
      developer,
      remark,
      sql,
      notify_immediately,
      sonic
    } = data
    const projectConfig = await this.projectService.projectConfigEntity.findOne(
      {
        where: {
          id: project_id
        }
      }
    )
    if (!projectConfig) {
      this.logger.error(`${data.project_id} - ${data.developer} 项目配置不存在`)
      throw new HttpException('项目配置不存在!', HttpStatus.BAD_REQUEST)
    }
    if (depend_project) {
      const validate = await this.validateCircularDependency(
        depend_project,
        project_id
      )
      if (!validate) {
        this.logger.error(
          `${data.project_id} - ${data.developer} 项目存在循环依赖`
        )
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            error: '项目存在循环依赖！'
          },
          HttpStatus.BAD_REQUEST
        )
      }
    }
    const newPublisher = this.publisherRepository.create({
      tag,
      publish_content,
      project_id,
      depend_project,
      tester,
      developer,
      remark,
      sql,
      notify_immediately,
      status: 'pending',
      sonic
    })
    await this.publisherRepository.save(newPublisher)
    if (notify_immediately) {
      const developer_user = await this.userService.findUserById(developer)
      if (developer_user && developer_user.belong) {
        const receive_id = getBelongsChatId(developer_user.belong)
        if (receive_id) {
          const vars = await this.getPublisherMessageVars(newPublisher.id)
          vars!.link = `https://applink.feishu.cn/client/web_app/open?appId=cli_a485bf0cbc38d00e&lk_target_url=${encodeURIComponent(
            `https://jarvis.yc345.tv/release/feishu?type=1&name=jack&id=${newPublisher.id}`
          )}`
          const res = await this.feishuService.sendMessage('chat_id', {
            content: deployment_start(vars!, projectConfig?.is_white_list),
            receive_id,
            msg_type: 'interactive'
          })
          if (res && res.status === 200 && res.data && res.data.code === 0) {
            await this.publisherRepository.update(newPublisher.id, {
              message_id: res.data.data.message_id
            })
          }
        }
      }
    }
    return newPublisher
  }
  /**
   * 根据上线任务构造飞书卡片消息
   * @param publisherId
   * @returns
   */
  async getPublisherMessageVars(publisherId: string, deployer?: AdminUser) {
    const publisher = await this.publisherRepository.findOneBy({
      id: publisherId
    })
    if (!publisher) {
      return
    }
    const [project, developer, tester] = await Promise.all([
      this.projectService.findOne(publisher.project_id),
      this.userService.findUserById(publisher.developer),
      this.userService.findUserById(publisher.tester)
    ])
    const onDutyOfficer = await this.userService.getOnCallOfficer()
    if (!onDutyOfficer) {
      return
    }
    return {
      publisherId: publisherId,
      developerName: developer!.name,
      developerFeishuId: developer!.feishu_id,
      testerName: tester!.name,
      testerFeishuId: tester!.feishu_id,
      name: project!.name,
      tag: publisher.tag,
      content: publisher.publish_content,
      sql: publisher.sql,
      duty: onDutyOfficer.feishu_id,
      deployerName: deployer?.name,
      deployerFeishuId: deployer?.feishu_id,
      remark: publisher.remark || '无备注信息',
      link: ''
    }
  }

  /**
   * 编辑上线任务
   * @param updateData
   */
  async updatePublisher(updateData: UpdatePublisherDto) {
    const {
      id,
      tag,
      publish_content,
      project_id,
      depend_project,
      tester,
      developer,
      remark,
      sql,
      sonic
    } = updateData
    if (depend_project) {
      const validate = await this.validateCircularDependency(
        depend_project,
        project_id
      )
      if (!validate) {
        this.logger.error(
          `${updateData.project_id} - ${updateData.developer} 项目配置不存在`
        )
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            error: '项目存在循环依赖！'
          },
          HttpStatus.BAD_REQUEST
        )
      }
    }
    await this.publisherRepository.update(id, {
      publish_content,
      depend_project,
      tester,
      developer,
      remark,
      sql,
      tag,
      sonic
    })
    const publisher = await this.publisherRepository.findOneBy({
      id
    })
    if (!publisher) {
      return
    }
    if (publisher.message_id) {
      //发送消息
      const vars = await this.getPublisherMessageVars(publisher.id)
      const projectConfig =
        await this.projectService.projectConfigEntity.findOne({
          where: {
            id: publisher.project_id
          }
        })
      vars!.link = `https://applink.feishu.cn/client/web_app/open?appId=cli_a485bf0cbc38d00e&lk_target_url=${encodeURIComponent(
        `https://jarvis.yc345.tv/release/feishu?type=1&name=jack&id=${id}`
      )}`
      this.feishuService.updateMessage(
        publisher.message_id,
        deployment_start(vars!, projectConfig?.is_white_list)
      )
    }
  }
  /**
   * 通知测试发送卡片消息
   * @param id
   * @param status
   */
  async noticePublisherToTester(id: string) {
    const publisher = await this.publisherRepository.findOne({
      where: {
        id
      }
    })
    const projectConfig = await this.projectService.projectConfigEntity.findOne(
      {
        where: {
          id: publisher!.project_id
        }
      }
    )
    if (
      publisher &&
      !publisher.notify_immediately &&
      publisher.status === 'pending'
    ) {
      //发送消息
      const vars = await this.getPublisherMessageVars(publisher.id)
      vars!.link = `https://applink.feishu.cn/client/web_app/open?appId=cli_a485bf0cbc38d00e&lk_target_url=${encodeURIComponent(
        `https://jarvis.yc345.tv/release/feishu?type=1&name=jack&id=${id}`
      )}`
      const developer_user = await this.userService.findUserById(
        publisher.developer
      )
      if (developer_user && developer_user.belong) {
        const receive_id = getBelongsChatId(developer_user.belong)
        if (receive_id) {
          const res = await this.feishuService.sendMessage('chat_id', {
            content: deployment_start(vars!, projectConfig?.is_white_list),
            receive_id,
            msg_type: 'interactive'
          })
          if (res && res.status === 200 && res.data && res.data.code === 0) {
            publisher.message_id = res.data.data.message_id
          }
          publisher.notify_immediately = true
          await this.publisherRepository.save(publisher)
        }
      }
    } else {
      this.logger.error(`${publisher?.project_id} 状态已不是待测试`)
    }
  }
}
