import { Injectable } from '@nestjs/common'
import dayjs from 'dayjs'
import { ConfigService } from '@nestjs/config'
import { workdaysMap } from './publihserConfig'

@Injectable()
export class PublisherLimitServcie {
  constructor(private readonly configService: ConfigService) {}

  /*
   * 上线窗口期校验
   *
   * 下午窗口期时间段：下午2点-下午5点
   * 晚上窗口期时间段：晚上11：30-次日早上5点
   *
   * 1. 寒暑假期间：仅开启晚上窗口期
   * 2. 工作日（非寒暑假）：开启下午+晚上窗口期
   * 3. 非工作日：仅开启晚上窗口期
   * */
  validateTimeLimit() {
    const currentTime = dayjs()
    // 任何日期的晚上窗口期均可上线
    if (
      currentTime.hour() < 5 ||
      (currentTime.hour() === 23 && currentTime.minute() >= 30)
    ) {
      return true
    }
    // 下午窗口期：是工作日 且 非周六日 且 非寒暑假可上线
    const isHoliday = this.configService.get('holidayLimit') === 'yes'
    if (isHoliday) {
      return false
    }
    const workDay = workdaysMap[dayjs().format('YYYY-MM-DD')]
    if (!workDay) {
      return false
    }
    if (currentTime.hour() > 13 && currentTime.hour() < 17) {
      return true
    }
    return false
  }
}
