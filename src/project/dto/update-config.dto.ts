import { ApiProperty } from '@nestjs/swagger'
import { IsBoolean, IsNumber, IsString, IsUUID } from 'class-validator'
import { IsOptional } from 'src/utils/IsOptional'

export class UpdateProjectConfigDto {
  @ApiProperty({
    description: '项目ID',
    required: true
  })
  @IsUUID()
  id: string

  @ApiProperty({
    description: '是否是新建的项目',
    required: true
  })
  @IsBoolean()
  is_new: boolean

  @ApiProperty({
    description: '是否使用docker',
    required: true
  })
  @IsBoolean()
  use_docker: boolean

  @ApiProperty({
    description: '测试环境域名'
  })
  @IsString()
  @IsOptional()
  test_domain: string

  @ApiProperty({
    description: 'stage环境域名'
  })
  @IsString()
  @IsOptional()
  stage_domain: string

  @ApiProperty({
    description: '生产环境域名'
  })
  @IsString()
  @IsOptional()
  production_domain: string

  @ApiProperty({
    description: '容器命名空间'
  })
  @IsString()
  @IsOptional()
  name_space: string

  @ApiProperty({
    description: '环境变量'
  })
  @IsString()
  @IsOptional()
  env: string

  @ApiProperty({
    description: '容器端口号'
  })
  @IsNumber()
  @IsOptional()
  container_port: number

  @ApiProperty({
    description: '容器映射端口'
  })
  @IsString()
  @IsOptional()
  map_port: string

  @ApiProperty({
    description: '前端public路径'
  })
  @IsString()
  @IsOptional()
  public_path: string

  @ApiProperty({
    description: '是否走绿色通道进行上线'
  })
  @IsBoolean()
  @IsOptional()
  is_white_list: boolean
}
