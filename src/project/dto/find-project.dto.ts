import { ApiProperty } from '@nestjs/swagger'
import { IsOptional } from 'src/utils/IsOptional'
import { PageBaseDto } from 'src/utils/pageBaseDto'

export class FindProjectDto extends PageBaseDto {
  @ApiProperty({
    required: false,
    description: '项目名称，支持模糊搜索'
  })
  @IsOptional()
  name: string
  @ApiProperty({
    required: false,
    description: '项目业务归属'
  })
  @IsOptional()
  belong: string
}
