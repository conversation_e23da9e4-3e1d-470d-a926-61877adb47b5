import { ApiBody, ApiProperty } from '@nestjs/swagger'
import {
  ArrayMinSize,
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNotEmptyObject,
  IsNumber,
  IsNumberString,
  IsObject,
  IsString,
  IsUUID,
  Validate,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface
} from 'class-validator'
import { IsOptional } from 'src/utils/IsOptional'
import { Config } from '../entities/project.entity'

@ValidatorConstraint()
class Label implements ValidatorConstraintInterface {
  validate(value: any, validationArguments?: ValidationArguments): boolean {
    return (
      Array.isArray(value) &&
      value.some((el) => el === '1' || el === '2' || el === '3')
    )
  }
}

export class UpdateProjectDto {
  @IsUUID()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: '项目ID,不填即为新增'
  })
  id: string
  @ApiProperty({
    description: '项目名称'
  })
  @IsString()
  @IsNotEmpty()
  name: string
  @IsNumberString()
  @IsNotEmpty()
  @ApiProperty({
    description: '项目业务归属，详见业务归属枚举值'
  })
  belong: string
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: '负责人Id'
  })
  developer: string
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: '项目主要描述'
  })
  describe: string

  @ApiProperty({
    type: [String],
    description:
      '项目标签，多选,具体值见项目标签枚举，必须有前、后端或Native之一'
  })
  @Validate(Label, {
    message: 'label必选选择前端，后端，Native其中一个'
  })
  @ArrayMinSize(1)
  @ArrayNotEmpty()
  @IsArray()
  label: string[]
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: '项目仓库地址'
  })
  address: string
  @IsNotEmptyObject()
  @IsObject()
  @ApiProperty({
    description: '项目构建配置JSON'
  })
  config: Config

  @IsString()
  gitlab_id: string
}

export class UpdateProjectV2Dto {
  @IsUUID()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: '项目ID,不填即为新增'
  })
  id: string
  @ApiProperty({
    description: '项目名称'
  })
  @IsString()
  @IsNotEmpty()
  name: string
  @IsNumberString()
  @IsNotEmpty()
  @ApiProperty({
    description: '项目业务归属，详见业务归属枚举值'
  })
  belong: string
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: '负责人Id'
  })
  developer: string
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: '项目主要描述'
  })
  describe: string

  @ApiProperty({
    type: [String],
    description:
      '项目标签，多选,具体值见项目标签枚举，必须有前、后端或Native之一'
  })
  @Validate(Label, {
    message: 'label必选选择前端，后端，Native其中一个'
  })
  @ArrayMinSize(1)
  @ArrayNotEmpty()
  @IsArray()
  label: string[]
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: '项目仓库地址'
  })
  address: string
  @IsNotEmptyObject()
  @IsObject()
  @ApiProperty({
    description: '项目构建配置JSON'
  })
  config: Config

  @IsString()
  gitlab_id: string

  //项目配置

  @ApiProperty({
    description: '是否使用docker',
    required: true
  })
  @IsBoolean()
  use_docker: boolean

  @ApiProperty({
    description: '容器命名空间'
  })
  @IsString()
  @IsOptional()
  name_space: string

  @ApiProperty({
    description: '容器端口号'
  })
  @IsNumber()
  @IsOptional()
  container_port: number

  @ApiProperty({
    description: '是否走绿色通道进行上线'
  })
  @IsBoolean()
  @IsOptional()
  is_white_list: boolean

  @ApiProperty({
    description: '集群上线使用多deployments'
  })
  @IsArray()
  @IsOptional()
  deployments: string[]
}
