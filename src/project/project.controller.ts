import { UpdateProjectDto, UpdateProjectV2Dto } from './dto/update-project.dto'
import { Body, Controller, Post, Get, Query, Request } from '@nestjs/common'
import { ProjectServer } from './project.service'
import { FindProjectDto } from './dto/find-project.dto'
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { UpdateProjectConfigDto } from './dto/update-config.dto'

@ApiTags('项目相关')
@Controller('project')
export class ProjectController {
  constructor(private readonly projectService: ProjectServer) {}
  @Get()
  @ApiOperation({
    summary: '获取单个项目详情V2'
  })
  async findProject(@Query('id') id: string) {
    return this.projectService.findProjectAndConfig(id)
  }
  // @ApiOperation({
  //   summary: '创建/更新项目'
  // })
  // @Post()
  // async createProject(@Request() req: any, @Body() body: UpdateProjectDto) {
  //   if (req.user.id) {
  //     return this.projectService.update(body, req.user.id)
  //   }
  // }

  @ApiOperation({
    summary: '创建/更新项目v2'
  })
  @Post()
  async updateProjectNew(
    @Request() req: any,
    @Body() body: UpdateProjectV2Dto
  ) {
    if (req.user.id) {
      return this.projectService.updateProjectAndConfig(body, req.user.id)
    }
  }

  @Get('/page')
  @ApiOperation({
    summary: '项目列表分页接口'
  })
  async findProjects(@Query() query: FindProjectDto) {
    return this.projectService.find(query)
  }

  @ApiOperation({
    summary: '获取所有项目数据'
  })
  @Get('/all')
  async findAllProjects() {
    return this.projectService.findAllProjects()
  }

  @Get('/del')
  @ApiOperation({
    summary: '删除单个项目'
  })
  async deleteProject(@Request() req: any, @Query('id') id: string) {
    if (req.user.id) {
      return this.projectService.delOne(id, req.user.id)
    }
  }

  @ApiOperation({
    summary: '根据项目Id获取项目的Gitlab的Tags'
  })
  @Get('/tags')
  async getProjectTags(@Query('id') id: string) {
    const list = await this.projectService.getProjectTagsFromGitlabId(id)
    return list || []
  }
  @ApiOperation({
    summary: '根据项目Id获取项目配置'
  })
  @Get('/config')
  async getProjectConfig(@Query('id') id: string) {
    return this.projectService.getConfig(id)
  }

  @ApiOperation({
    summary: '更新项目配置'
  })
  @Post('/config')
  async updateProjectConfig(@Body() body: UpdateProjectConfigDto) {
    return this.projectService.updateConfig(body)
  }
}
