import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { LoggerModule } from 'src/logger/logger.module'
import { ProjectConfigEntity } from './entities/project-config.entity'
import { ProjectEntity } from './entities/project.entity'
import { ProjectController } from './project.controller'
import { ProjectServer } from './project.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([ProjectEntity, ProjectConfigEntity]),
    LoggerModule
  ],
  controllers: [ProjectController],
  providers: [ProjectServer],
  exports: [ProjectServer]
})
export class ProjectModule {}
