import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
  DeleteDateColumn
} from 'typeorm'

export interface Config {
  nodeVesion: string
  masterBuild: string
  releaseBuild: string
  developBuild: string
  afterBuild: string
  beforeBuild: string
  [key: string]: any
}

@Entity()
export class ProjectEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string
  @Column({
    type: 'varchar',
    length: 100
  })
  name: string
  @Column()
  belong: string
  @Column()
  developer: string
  @Column({
    type: 'varchar',
    length: 100
  })
  describe: string
  @Column({
    type: 'simple-array'
  })
  label: string[]
  @Column({
    type: 'varchar',
    length: 100
  })
  address: string
  @Column()
  creator: string
  @Column()
  update_user: string
  @DeleteDateColumn()
  delete_date: Date

  @Column('simple-json')
  config: Config
  @Column()
  gitlab_id: string
  @CreateDateColumn()
  create_time: Date
  @UpdateDateColumn()
  update_time: Date
}
