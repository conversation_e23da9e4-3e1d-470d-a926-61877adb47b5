import { Column, Entity, PrimaryColumn } from 'typeorm'

@Entity()
export class ProjectConfigEntity {
  @PrimaryColumn('uuid')
  id: string

  //是否新建
  @Column({
    type: 'boolean'
  })
  is_new: boolean

  //是否使用docker
  @Column({
    type: 'boolean',
    default: true
  })
  use_docker: boolean

  @Column({
    type: 'varchar',
    nullable: true
  })
  test_domain: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  stage_domain: string

  @Column({
    type: 'varchar',
    nullable: true
  })
  production_domain: string

  //容器集群命名空间
  @Column({
    type: 'varchar',
    nullable: true
  })
  name_space: string

  //环境变量
  @Column({
    type: 'varchar'
  })
  env: string
  //容器端口
  @Column({
    type: 'int',
    default: 80
  })
  container_port: number

  //端口映射
  @Column({
    type: 'varchar'
  })
  map_port: string

  //路由publicPath
  @Column({
    type: 'varchar',
    nullable: true
  })
  public_path: string

  //是否是绿色通道
  @Column({
    type: 'boolean',
    nullable: true,
    default: false
  })
  is_white_list: boolean

  @Column({
    type: 'jsonb',
    nullable: true
  })
  deployments: string[]
}
