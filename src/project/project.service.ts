import { UpdateProjectConfigDto } from './dto/update-config.dto'
import { ProjectConfigEntity } from './entities/project-config.entity'
import { Inject, Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { FindOptionsWhere, Like, Repository } from 'typeorm'
import { FindProjectDto } from './dto/find-project.dto'
import { UpdateProjectDto, UpdateProjectV2Dto } from './dto/update-project.dto'
import { ProjectEntity } from './entities/project.entity'
import { LoggerService } from 'src/logger/logger.service'
import axios from 'axios'
import { ConfigService } from '@nestjs/config'
import dayjs from 'dayjs'

type GitlabTags = {
  name: string
  message: string
  protected: boolean
  commit: {
    created_at: string
  }
}

@Injectable()
export class ProjectServer {
  constructor(
    @InjectRepository(ProjectEntity)
    public readonly projectModel: Repository<ProjectEntity>,
    @InjectRepository(ProjectConfigEntity)
    public readonly projectConfigEntity: Repository<ProjectConfigEntity>,
    private readonly logger: LoggerService,
    @Inject(ConfigService) private readonly config: ConfigService
  ) {}
  /**
   * 分页查询项目列表
   */
  async find(query: FindProjectDto) {
    const findQuery: FindOptionsWhere<ProjectEntity> = {}
    if (query.name) {
      findQuery.name = Like(`%${query.name}%`)
    }
    if (query.belong && query.belong !== '8') {
      findQuery.belong = query.belong
    }
    const data = await this.projectModel.find({
      where: findQuery,
      withDeleted: false,
      take: query.pageSize,
      skip: (query.page - 1) * query.pageSize
    })
    const count = await this.projectModel.countBy(findQuery)
    return {
      page: query.page,
      pageSize: query.pageSize,
      list: data,
      total: count
    }
  }
  /**
   * 根据id获取单个项目详情
   * @param id
   * @returns
   */
  async findOne(id: string) {
    return this.projectModel.findOne({
      where: {
        id
      },
      withDeleted: false
    })
  }

  async findProjectAndConfig(id: string) {
    const [project, projectConfig] = await Promise.all([
      this.projectModel.findOneBy({ id }),
      this.projectConfigEntity.findOneBy({ id })
    ])
    return {
      ...project,
      ...projectConfig
    }
  }
  /**
   * 新建,更新项目信息
   * @param data
   * @returns
   */
  async update(data: UpdateProjectDto, userId: string) {
    if (data.id) {
      const res = await this.projectModel.update(data.id, {
        ...data,
        update_user: userId
      })
      this.logger.append(data.id, '更新服务', '更新服务', userId)
      return res
    } else {
      const newProBody = { ...data }
      //@ts-ignore
      delete newProBody.id
      const newProject = this.projectModel.create({
        ...newProBody,
        creator: userId,
        update_user: userId
      })
      await this.projectModel.save(newProject)
      this.logger.append(newProject.id, '创建服务', '新建服务', userId)
      return newProject
    }
  }

  async updateProjectAndConfig(data: UpdateProjectV2Dto, userId: string) {
    let project_id = ''
    const {
      id,
      name,
      belong,
      developer,
      describe,
      label,
      address,
      config,
      gitlab_id
    } = data
    if (data.id) {
      project_id = id
      await this.projectModel.update(id, {
        name,
        belong,
        developer,
        describe,
        label,
        address,
        config,
        gitlab_id,
        update_user: userId
      })
      this.logger.append(data.id, '更新服务', '更新服务', userId)
    } else {
      const newProject = this.projectModel.create({
        name,
        belong,
        developer,
        describe,
        label,
        address,
        config,
        gitlab_id,
        creator: userId,
        update_user: userId
      })

      await this.projectModel.save(newProject)
      project_id = newProject.id
      this.logger.append(newProject.id, '创建服务', '新建服务', userId)
    }
    const project_config = await this.projectConfigEntity.findOneBy({
      id: project_id
    })
    if (!project_config) {
      await this.createDefaultConfig(project_id)
    }
    const {
      use_docker,
      name_space,
      container_port,
      is_white_list,
      deployments
    } = data
    await this.projectConfigEntity.update(project_id, {
      use_docker,
      name_space,
      container_port,
      is_white_list,
      deployments
    })
    const [project, projectConfig] = await Promise.all([
      this.projectModel.findOneBy({ id: project_id }),
      this.projectConfigEntity.findOneBy({ id: project_id })
    ])
    return {
      ...project,
      ...projectConfig
    }
  }

  /**
   * 删除项目
   * @param id
   */
  async delOne(id: string, userId: string) {
    await this.projectModel.update(id, {
      update_user: userId
    })
    this.logger.append(id, '创建服务', '新建服务', userId)
    await this.projectModel.softDelete(id)
  }
  async findAllProjects() {
    return this.projectModel.find()
  }
  /**
   * 根据项目gitlab仓库id从gitlab获取项目 tags
   * @param projectId
   * @returns
   */
  async getProjectTagsFromGitlabId(projectId: string) {
    const project = await this.findOne(projectId)
    if (project?.gitlab_id) {
      try {
        const res = await axios.get<GitlabTags[]>(
          `https://gitlab.yc345.tv/api/v4/projects/${project.gitlab_id}/repository/tags?per_page=100`,
          {
            headers: {
              Authorization: `Bearer ${this.config.get('gitLabAccessToken')}`
            }
          }
        )
        if (res.status === 200 && res.data) {
          return res.data.sort((a, b) => {
            return (
              dayjs(b.commit.created_at).valueOf() -
              dayjs(a.commit.created_at).valueOf()
            )
          })
        }
      } catch (e) {
        return []
      }
    }
  }

  async createDefaultConfig(id: string) {
    const config = this.projectConfigEntity.create({
      id,
      is_new: false,
      use_docker: false,
      test_domain: '',
      stage_domain: '',
      production_domain: '',
      name_space: '',
      env: '',
      container_port: 80,
      map_port: '',
      public_path: '',
      is_white_list: false
    })
    await this.projectConfigEntity.save(config)
    return config
  }

  async getConfig(id: string) {
    const project = await this.findOne(id)
    let config = await this.projectConfigEntity.findOneBy({
      id
    })
    if (!config) {
      config = await this.createDefaultConfig(id)
    }
    const resObj: Record<string, any> = { ...config }
    resObj.project_name = project?.name
    resObj.label = project?.label
    return resObj
  }

  async updateConfig(data: UpdateProjectConfigDto) {
    const {
      id,
      production_domain,
      stage_domain,
      public_path,
      test_domain,
      is_new,
      use_docker,
      name_space,
      env,
      container_port,
      map_port,
      is_white_list
    } = data
    await this.projectConfigEntity.update(id, {
      production_domain,
      stage_domain,
      public_path,
      test_domain,
      is_new,
      use_docker,
      name_space,
      env,
      container_port,
      map_port,
      is_white_list
    })
  }
}
