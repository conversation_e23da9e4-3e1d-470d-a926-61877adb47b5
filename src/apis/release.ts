import axios from '~/helpers/request'

export interface FetchReleaseListReq {
  status: string
  createTime?: string[]
  createTimeStart?: string
  createTimeEnd?: string
  projectId: string
  developer: string
  tester: string
  page?: number
  pageSize?: number
}

export interface SonicItem {
  projectName: string
  caseSuiteName: string
}
export interface Release {
  id: string
  name?: string
  createTime?: string
  status?: string
  developer: string
  tester: string
  status_text: string
  notify_immediately: boolean
  sonic: SonicItem[]
  tag: string
  publish_content: string
  sql: string
  remark: string
  depend_project: string
  project_id: string
  belong: string
}

export interface FetchReleaseListResp {
  list: Release[]
  total: number
}

export function fetchReleaseList(params: FetchReleaseListReq): Promise<FetchReleaseListResp> {
  return axios.get('/publisher/page', {
    params,
  })
}

export function fetchReleaseDetail(id: string): Promise<Release> {
  return axios.get('/publisher', {
    params: {
      id,
    },
  })
}

export function fetchReleaseNotice(id: string) {
  return axios.get('/publisher/notice', {
    params: {
      id,
    },
  })
}

export interface ProjectTag {
  name: string
  tag: string
}

export function fetchProjectTags(id: string): Promise<ProjectTag[]> {
  return axios.get('/project/tags', {
    params: {
      id,
    },
  })
}

export interface ReleaseDepend {
  project_id: string
  name: string
  tag: string
}

/**
 * 获取所有等待上线的任务列表
 */
export function fetchReleaseDepends(): Promise<ReleaseDepend[]> {
  return axios.get('/publisher/depends')
}

export interface UITestProject {
  projectName: string
}

/**
 * 获取UI测试用例-项目组列表
 */
export function fetchUITestProjectList(): Promise<{ data: UITestProject[] }> {
  return axios.get('/sonic/sonicProject')
}

export interface UITestProjectDetail {
  data: {
    id: string
    name: string
  }[]
}

/**
 * 获取UI测试用例-项目组下的测试用例集
 */
export function fetchUITestProjectDetail(name: string): Promise<UITestProjectDetail> {
  return axios.get('/sonic/sonicCaseSuite', {
    params: {
      name,
    },
  })
}

export function postRelease(data: Partial<Release>) {
  return axios.post('/publisher', data)
}

interface FetchReleaseStatisticsReq {
  startTime: string | undefined
  endTime: string | undefined
  userId: string | undefined
  projectId: string | undefined
}

export interface FetchReleaseStatisticsResp {
  countsByStatus: {
    status: string
    count: number
  }[]
  countsByProject: {
    projectId: string
    count: number
    name: string
  }[]
  heatmapByDate: {
    date: string
    count: number
  }[]
  heatmapByHour: {
    hour: string
    count: number
  }[]
}

/**
 * 获取上线统计数据
 */
export function fetchReleaseStatistics(params: FetchReleaseStatisticsReq): Promise<FetchReleaseStatisticsResp> {
  return axios.get('/publisher/statistics', {
    params,
  })
}
