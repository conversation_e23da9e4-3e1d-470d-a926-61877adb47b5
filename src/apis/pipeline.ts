import axios from '~/helpers/request'

interface FetchPipelineListReq {
  page: number
  pageSize: number
  projectId: string
}

export interface PipelineStage {
  id: string
  stage: string
  status: string
  count_time: number
  job_data: {
    new_tag: string
  }
}

export interface Pipeline {
  id: string
  project_id: string
  state: string
  count_time: number
  tag_type: string
  branch: string
  last_operator: string
  update_at: string
  create_at: string
  stages: PipelineStage[]
  tag: string
}

interface FetchPipelineListResp {
  list: Pipeline[]
  total: number
}

export function fetchPipelineList(params: FetchPipelineListReq): Promise<FetchPipelineListResp> {
  return axios.get(`/pipeline/page`, {
    params,
  })
}

export async function fetchPipelineStages(pipelineId: string): Promise<PipelineStage[]> {
  return axios.get(`/pipeline/stages`, {
    params: {
      pipelineId,
    },
  })
}

export async function fetchPipelineLogs(pipelineId: string): Promise<{ log: string }> {
  return axios.get(`/pipeline/log`, {
    params: {
      pipelineId,
    },
  })
}

export async function stopPipeline(pipelineId: string) {
  return axios.get(`/pipeline/stop`, {
    params: {
      pipelineId,
    },
  })
}

interface CreateNewPipelineReq {
  project_id: string
  tag_type: string
  package_manager: string
  branch: string
}

export async function createNewPipeline(data: CreateNewPipelineReq) {
  return axios.post(`/pipeline`, data)
}
