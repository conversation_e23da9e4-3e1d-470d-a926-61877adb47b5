import axios from '~/helpers/request'

const host = 'https://data-inspector.yc345.tv'

const monitorMap = {
  performance: 'f3069b7e-1e5d-4b5e-a7e9-4fb6873bacc5',
  abnormal: 'e331de27-c37d-402c-b7f0-4fbfa1e2eba4',
  api: '464e6207-8959-4dde-b033-6660138f4e7d',
  app: 'bd5f39fb-7e5e-4ce9-85e8-c4e0279bc9f6',
  loki: '1e0940dd-b66f-449a-b656-466604217f29',
} as const

export type MonitorName = keyof typeof monitorMap

export interface SearchParam {
  topicId?: string
  sql: string
  monitorName?: MonitorName
  from?: number
  to?: number
}

export interface TlsLogItemType {
  appKey: string
  pathname: string
  pv: number
  uv: number
  jsError?: string
  blankScreen?: string
  resourceError?: string
  _col0?: string
  time?: string
  oversizedChunks?: string
  avgFcp?: string
  errorCount?: string
  msg?: string
  c?: string
  src?: string
}

/**
 * @description 获取TLS日志
 * @param {SearchParam} searchParam 查询参数
 * @param {string} searchParam.topicId 主题ID
 * @param {string} searchParam.sql 查询语句
 * @param {MonitorName} searchParam.monitorName 监控名称
 * @param {number} searchParam.from 开始时间
 * @param {number} searchParam.to 结束时间
 */
export async function getTlsLogsApi(searchParam: SearchParam): Promise<TlsLogItemType[]> {
  if (searchParam.monitorName) {
    searchParam.topicId = monitorMap[searchParam.monitorName]
  }
  if (!searchParam.topicId) {
    throw new Error('topicId is required')
  }
  const now = Math.floor(Date.now() / 1000)
  if (!searchParam.to) {
    searchParam.to = now
  }
  if (!searchParam.from) {
    searchParam.from = now - 3600 * 24 * 7
  }
  return axios.post(
    `${host}/public/tls/logs`,
    searchParam,
  )
}

export interface Alarm {
  alertId: string
  alertName: string
  alertDetailCategory: string
  alertDetailType: string
  alertDetailComputeMode: string
  alertDetailLevel: string
  alertDetailAppKey: string
  conditionCount: number
  conditionStatus: string
  statusType: string
  conditionErrorType: string
  conditionGt: number
  topicName: string
  status: boolean
  requestCycleType: string
  requestCycleTime: number
  userDefineMsg: string
  noticesGroup: string[]
}

export function fetchAlarmList(): Promise<{ list: Alarm[] }> {
  return axios.post(`${host}/alert/vke/fronted/customer/list`)
}

export function alarmCreate(data: Alarm) {
  return axios.post(`${host}/alert/vke/fronted/customer`, data)
}

export function alarmUpdate(data: Alarm) {
  return axios.post(`${host}/alert/vke/fronted/customer`, data)
}

export function alarmDelete(id: string) {
  return axios.delete(`${host}/alert/vke/fronted/customer?id=${id}`)
}

export interface AlertWhiteListItem {
  id: number | null
  category: string
  module: string
  appKey: string
  whiteList: string[]
  status: number
}

export function fetchAlertVkeFrontedWhiteList(): Promise<AlertWhiteListItem[]> {
  return axios.get(
    `${host}/alert/vke/fronted/white/list`,
  )
}

export function postAlertVkeFrontedWhite(data: AlertWhiteListItem) {
  return axios.post(`${host}/alert/vke/fronted/white`, data)
}

export function deleteAlertVkeFrontedWhite(id: number) {
  return axios.delete(
    `${host}/alert/vke/fronted/white`,
    { data: { id } },
  )
}

export function putAlertVkeFrontedWhite(data: AlertWhiteListItem) {
  return axios.put(`${host}/alert/vke/fronted/white`, data)
}
