import axios from '~/helpers/request'

interface FetchProjectListReq {
  page: number
  pageSize: number
}

export interface ConfigType {
  developBuild: string
  releaseBuild: string
  masterBuild: string
  installCmd: string
  nodeVesion: string
  delNodeModule?: string
}

export interface Project {
  id: string
  gitlab_id: string
  name: string
  belong: string
  config: ConfigType
  label: string[]
  address: string
  developer: string
  describe: string
  name_space: string
  use_docker: boolean
  is_white_list?: boolean
  use_deployments?: boolean
  deployments?: string[]
}

export interface FetchProjectListResp {
  list: Project[]
  total: number
}

export async function fetchProjectList(params: FetchProjectListReq): Promise<FetchProjectListResp> {
  return axios.get('/project/page', {
    params,
  })
}

export async function fetchAllProjectList(): Promise<Project[]> {
  return axios.get('/project/all')
}

// 获取项目详情
export async function fetchProjectDetail(id: string): Promise<Project> {
  return axios.get('/project', { params: { id } })
}

// 删除项目
export async function deleteProjectApi(id: string): Promise<Project> {
  return axios.get('/project/del', { params: { id } })
}

// 创建/更新项目
export async function updateProjectApi(data: Project): Promise<Project> {
  return axios.post('/project', data)
}
