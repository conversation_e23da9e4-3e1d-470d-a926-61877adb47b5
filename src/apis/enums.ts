import axios from '~/helpers/request'

export interface Belong {
  label: string
  value: string
}

export function fetchBelongs(): Promise<Belong[]> {
  return axios.get('/config/belongs')
}

export interface Label {
  label: string
  value: string
}

export async function fetchLabels(): Promise<Label[]> {
  return axios.get('/config/labels')
}

export interface User {
  label: string
  value: string
  role: string
}

export async function fetchAllUsers(): Promise<User[]> {
  return axios.get('/user/all')
}
