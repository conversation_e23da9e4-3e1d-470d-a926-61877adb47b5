import axios from '~/helpers/request'

export interface UserInfoType {
  id: string
  name: string
  feishu_id: string
  belong: string
  role: string
  avatar_url: string
  phone: string
  status: string
  create_time: string
  update_time: string
  update_user: string
  delete_time: string | null
}

// 登录
export function login(code: string): Promise<{ token: string }> {
  return axios.post('/user/login', { code })
}

// 获取用户信息
export function fetchUserInfo(): Promise<UserInfoType> {
  return axios.get('/user/info')
}

export interface UserListType {
  page: number
  pageSize: number
  total: number
  list: UserInfoType[]
}

// 获取用户列表分页
export function getUserListApi(params: {
  page: number
  pageSize: number
}): Promise<UserListType> {
  return axios.get('/user/page', { params })
}

export interface UserType {
  id?: string
  name: string
  role: string
  belong: string
  phone: string
  [key: string]: string | number | undefined
}
// 新建、更新用户信息
export function setUpdateUserApi(data: UserType) {
  return axios.post('/user', data)
}

// 删除用户
export function delUserApi(id: string) {
  return axios.get('/user/del', { params: { id } })
}
