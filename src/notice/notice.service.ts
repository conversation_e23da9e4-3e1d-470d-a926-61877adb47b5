import { Injectable } from '@nestjs/common'
import { FeishuApiService } from 'src/feishu-api/fetshu-service'

@Injectable()
export class NoticeService {
  constructor(private readonly feiShuApiService: FeishuApiService) {}
  async sendFeiShuCardMessage() {
    // this.feiShuApiService.sendMessage('chat_id', {
    //   receive_id: 'oc_a5f385ef52eea0ad201967934f32e2b4',
    //   msg_type: 'interactive',
    //   content: {}
    // })
  }
}
