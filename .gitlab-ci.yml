variables:
  NAMESPACE: 7to12
  DOCKER_REGISTRY: docker.yc345.tv

stages:
  - build_dev
  - build_prod

before_script:
  - git config --global http.https://github.com.proxy http://********:8118 && git config --global https.https://github.com.proxy http://********:8118

build_dev:
  stage: build_dev
  only:
    - dev
    - v2
  tags:
    - ops
  script:
    - |
      if ! nvm list | grep -q "v22.12.0"; then
        echo "Node.js v22.12.0 not found, installing..."
        nvm install v22.12.0
      else
        echo "Node.js v22.12.0 already exists, skipping installation"
      fi
    - nvm use v22.12.0
    - |
      if ! command -v pnpm &> /dev/null; then
        echo "pnpm not found, installing..."
        npm install -g pnpm@10.5.2
      else
        echo "pnpm already exists, version: $(pnpm --version)"
      fi
    - pnpm install
    - pnpm run build:test
    - docker login --username=$DOCKER_USERNAME $DOCKER_REGISTRY -p $DOCKER_PASSWORD
    - docker build -t $DOCKER_REGISTRY/$NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_SHORT_SHA .
    - docker push $DOCKER_REGISTRY/$NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_SHORT_SHA
    - kubectl config set-cluster test-cluster --server=$KUBE_HOST_TEST --insecure-skip-tls-verify=true
    - kubectl config set-credentials gitlab-ci --token=$KUBE_TOKEN_TEST
    - kubectl config set-context testenv --cluster=test-cluster --user=gitlab-ci && kubectl config use-context testenv
    - kubectl -n $NAMESPACE set image deployment $CI_PROJECT_NAME $CI_PROJECT_NAME=$DOCKER_REGISTRY/$NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_SHORT_SHA --record
    - kubectl -n $NAMESPACE rollout status deployment $CI_PROJECT_NAME

build_prod:
  stage: build_prod
  only:
    - /^v\d+(?:\.\d+){2}$/
  tags:
    - ops
  script:
    - |
      if ! nvm list | grep -q "v22.12.0"; then
        echo "Node.js v22.12.0 not found, installing..."
        nvm install v22.12.0
      else
        echo "Node.js v22.12.0 already exists, skipping installation"
      fi
    - nvm use v22.12.0
    - |
      if ! command -v pnpm &> /dev/null; then
        echo "pnpm not found, installing..."
        npm install -g pnpm@10.5.2
      else
        echo "pnpm already exists, version: $(pnpm --version)"
      fi
    - pnpm install
    - pnpm run build:prod
    - docker login --username=$DOCKER_USERNAME $DOCKER_REGISTRY -p $DOCKER_PASSWORD
    - docker build -t $DOCKER_REGISTRY/$NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME .
    - docker push $DOCKER_REGISTRY/$NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME
    - kubectl config set-cluster test-cluster --server=$KUBE_HOST_TEST --insecure-skip-tls-verify=true
    - kubectl config set-credentials gitlab-ci --token=$KUBE_TOKEN_TEST
    - kubectl config set-context testenv --cluster=test-cluster --user=gitlab-ci && kubectl config use-context testenv
    - kubectl -n $NAMESPACE set image deployment ${CI_PROJECT_NAME}-prod $CI_PROJECT_NAME=$DOCKER_REGISTRY/$NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME --record
    - kubectl -n $NAMESPACE rollout status deployment ${CI_PROJECT_NAME}-prod
